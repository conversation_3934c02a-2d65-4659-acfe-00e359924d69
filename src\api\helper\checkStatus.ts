import { ElMessage } from "element-plus";
import i18n from "@/languages/index";
const t = i18n.global.t;

/**
 * @description: 校验网络请求状态码
 * @param {Number} status
 * @return void
 */
export const checkStatus = (status: number) => {
  switch (status) {
    case 400:
      ElMessage.error(t("errors.badRequestTip"));
      break;
    case 401:
      ElMessage.error(t("errors.loginFailedTip"));
      break;
    case 403:
      ElMessage.error(t("errors.forbiddenTip"));
      break;
    case 404:
      ElMessage.error(t("errors.resourceNotFoundTip"));
      break;
    case 405:
      ElMessage.error(t("errors.methodNotAllowedTip"));
      break;
    case 408:
      ElMessage.error(t("errors.requestTimeoutTip"));
      break;
    case 500:
      ElMessage.error(t("errors.serverErrorTip"));
      break;
    case 502:
      ElMessage.error(t("errors.badGatewayTip"));
      break;
    case 503:
      ElMessage.error(t("errors.serviceUnavailableTip"));
      break;
    case 504:
      ElMessage.error(t("errors.gatewayTimeoutTip"));
      break;
    default:
      ElMessage.error(t("errors.badRequestTip"));
  }
};
