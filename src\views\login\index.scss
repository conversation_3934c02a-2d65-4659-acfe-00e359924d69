.login-container {
  position: relative;
  height: 100%;
  min-height: 550px;
  overflow: hidden;
  background-color: #f0f2f5;
  background-image: url("@/assets/images/login_bg.svg");
  background-size: 100% 100%;
  background-size: cover;

  // 动态背景渐变
  &::before {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
    width: 100%;
    height: 100%;
    content: "";
    background: linear-gradient(135deg, rgb(64 158 255 / 10%), rgb(103 194 58 / 5%), rgb(230 162 60 / 5%));
    background-size: 400% 400%;
    backdrop-filter: blur(5px);
    animation: gradient 15s ease infinite;
  }

  // 添加装饰元素
  &::after {
    position: absolute;
    z-index: 0;
    width: 100%;
    height: 100%;
    content: "";
    background:
      radial-gradient(circle at 80% 20%, rgb(64 158 255 / 10%) 0%, transparent 40%),
      radial-gradient(circle at 20% 80%, rgb(103 194 58 / 10%) 0%, transparent 40%);
    opacity: 0.8;
  }
  .login-box {
    position: relative;
    z-index: 1;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-around;
    width: 96.5%;
    height: 94%;
    padding: 0 50px;
    background-color: rgb(255 255 255 / 30%);
    backdrop-filter: blur(10px);
    border: 1px solid rgb(255 255 255 / 20%);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgb(0 0 0 / 10%);
    .login-actions {
      position: absolute;
      top: 13px;
      right: 18px;
      z-index: 10;
      display: flex;
      gap: 15px;
      align-items: center;
      .dark-switch,
      .language-switch {
        cursor: pointer;
        transition: all 0.3s ease;
        &:hover {
          transform: scale(1.1);
        }
      }
    }
    .login-left {
      width: 800px;
      margin-right: 10px;
      .login-left-img {
        width: 100%;
        height: 100%;
        filter: drop-shadow(0 10px 15px rgb(0 0 0 / 10%));
        transition: all 0.5s ease;
        &:hover {
          transform: translateY(-5px);
        }
      }
    }
    .login-form {
      position: relative;
      width: 420px;
      padding: 50px 40px 45px;
      overflow: hidden;
      background-color: rgb(255 255 255 / 50%);
      backdrop-filter: blur(20px);
      border: 1px solid rgb(255 255 255 / 40%);
      border-radius: 24px;
      box-shadow:
        0 15px 35px rgb(0 0 0 / 10%),
        0 3px 10px rgb(0 0 0 / 5%);
      transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
      animation: fadeIn 0.8s ease-out;

      // 添加装饰元素
      &::before,
      &::after {
        position: absolute;
        z-index: 0;
        content: "";
        border-radius: 50%;
        opacity: 0.4;
        transition: all 0.6s ease;
      }
      &::before {
        top: -30px;
        right: -30px;
        width: 150px;
        height: 150px;
        background: radial-gradient(circle, rgb(64 158 255 / 40%) 0%, rgb(64 158 255 / 0%) 70%);
      }
      &::after {
        bottom: -50px;
        left: -50px;
        width: 200px;
        height: 200px;
        background: radial-gradient(circle, rgb(103 194 58 / 30%) 0%, rgb(103 194 58 / 0%) 70%);
      }
      &:hover {
        background-color: rgb(255 255 255 / 60%);
        border-color: rgb(255 255 255 / 50%);
        box-shadow:
          0 25px 45px rgb(0 0 0 / 15%),
          0 5px 15px rgb(0 0 0 / 7%);
        transform: translateY(-7px) scale(1.01);
        &::before {
          opacity: 0.6;
          transform: scale(1.2) translate(-10px, 10px);
        }
        &::after {
          opacity: 0.5;
          transform: scale(1.1) translate(10px, -10px);
        }
      }
      .login-logo {
        position: relative;
        z-index: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 45px;
        .login-icon {
          width: 80px;
          height: 80px;
          filter: drop-shadow(0 4px 8px rgb(0 0 0 / 15%));
          transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
          animation: float 3s ease-in-out infinite;
          &:hover {
            filter: drop-shadow(0 8px 12px rgb(0 0 0 / 20%)) brightness(1.05);
            transform: scale(1.1) rotate(8deg);
          }
        }
        .logo-text {
          position: relative;
          padding: 0 0 0 25px;
          margin: 0;
          font-size: 42px;
          font-weight: bold;
          text-shadow: 0 2px 10px rgb(0 0 0 / 10%);
          white-space: nowrap;
          background: linear-gradient(135deg, #34495e 0%, #4a6b8a 100%);
          background-clip: text;
          transition: all 0.3s ease;
          -webkit-text-fill-color: transparent;
          &::after {
            position: absolute;
            bottom: 0;
            left: 25px;
            width: 0;
            height: 3px;
            content: "";
            background: linear-gradient(90deg, #409eff, #67c23a);
            border-radius: 3px;
            opacity: 0.7;
            transition: width 0.5s ease;
          }
          &:hover::after {
            width: calc(100% - 25px);
          }
        }
      }
      .el-form-item {
        margin-bottom: 40px;
      }
      .login-btn {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        margin-top: 40px;
        white-space: nowrap;
        .el-button {
          width: 185px;
          transition: all 0.3s ease;
          &:hover {
            box-shadow: 0 5px 15px rgb(0 0 0 / 10%);
            transform: translateY(-3px);
          }
          &:active {
            transform: translateY(0);
          }
        }
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
  100% {
    transform: translateY(0);
  }
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@media screen and (width <= 1250px) {
  .login-left {
    display: none;
  }
  .login-box {
    justify-content: center;
    padding: 0 20px;
  }
}

@media screen and (width <= 768px) {
  .login-container {
    min-height: 100vh;
    padding: 20px 0;
  }
  .login-box {
    width: 100%;
    height: auto;
    min-height: calc(100vh - 40px);
    padding: 20px;
    .login-form {
      width: 100% !important;
      max-width: 400px;
      padding: 40px 30px 35px;
      margin: 0 auto;
      .login-logo {
        margin-bottom: 35px;
        .login-icon {
          width: 60px;
          height: 60px;
        }
        .logo-text {
          font-size: 32px;
        }
      }
      .el-form-item {
        margin-bottom: 25px;
      }
      .login-btn {
        margin-top: 30px;
        .el-button {
          width: 48%;
          min-width: 120px;
        }
      }
    }
  }
}

@media screen and (width <= 480px) {
  .login-box {
    padding: 15px;
    .login-form {
      padding: 30px 20px 25px;
      .login-logo {
        flex-direction: column;
        gap: 10px;
        margin-bottom: 25px;
        .login-icon {
          width: 50px;
          height: 50px;
        }
        .logo-text {
          padding: 0;
          font-size: 24px;
        }
      }
      .el-form-item {
        margin-bottom: 20px;
      }
      .login-btn {
        flex-direction: column;
        gap: 15px;
        margin-top: 25px;
        .el-button {
          width: 100%;
          min-width: auto;
        }
      }
    }
  }
}

@media screen and (width <= 600px) {
  .login-form {
    width: 97% !important;
  }
}

// 弹窗响应式优化
@media screen and (width <= 768px) {
  :deep(.glass-dialog) {
    width: 90% !important;
    max-width: 500px !important;
    margin: 20px auto !important;
    .el-dialog__body {
      padding: 20px !important;
    }
    .el-dialog__footer {
      padding: 15px 20px 20px !important;
      .el-button {
        min-width: 80px;
      }
    }
  }
}

@media screen and (width <= 480px) {
  :deep(.glass-dialog) {
    width: 95% !important;
    margin: 10px auto !important;
    .el-dialog__header {
      padding: 15px 20px 10px !important;
      .el-dialog__title {
        font-size: 16px !important;
      }
    }
    .el-dialog__body {
      padding: 15px !important;
      .dialog-form {
        .el-form-item {
          margin-bottom: 15px !important;
        }
        .el-input {
          .el-input__wrapper {
            border-radius: 8px !important;
          }
        }
      }
    }
    .el-dialog__footer {
      padding: 10px 15px 15px !important;
      .el-button {
        min-width: 70px;
        padding: 8px 15px !important;
      }
    }
  }
}

// 对话框中的表单样式
.dialog-form {
  .el-form-item {
    margin-bottom: 20px;
    opacity: 1;
    transform: none;
    animation: none;
  }
  .el-input__wrapper {
    background-color: #ffffff;
    backdrop-filter: none;
    box-shadow: 0 0 0 1px #dcdfe6 inset !important;
    transform: none !important;
    &:hover {
      box-shadow: 0 0 0 1px #c0c4cc inset !important;
      transform: none !important;
    }
    &.is-focus {
      box-shadow: 0 0 0 1px #409eff inset !important;
    }
  }
  .el-button {
    border-radius: 4px;
    transform: none !important;
    &:hover {
      box-shadow: none !important;
      transform: none !important;
    }
  }
}

// 美化输入框样式 - 增强版
.login-form {
  .el-form-item {
    position: relative;
    overflow: hidden;

    // 添加装饰元素
    &::before {
      position: absolute;
      top: -50px;
      left: -50px;
      z-index: 0;
      width: 100px;
      height: 100px;
      pointer-events: none;
      content: "";
      background: radial-gradient(circle, rgb(64 158 255 / 20%) 0%, rgb(64 158 255 / 0%) 70%);
      border-radius: 50%;
      opacity: 0;
      transition: opacity 0.5s ease;
    }
    &:hover::before {
      opacity: 1;
      animation: pulse 2s infinite;
    }
  }
  .el-input {
    --el-input-hover-border-color: #409eff;
    --el-input-focus-border-color: #409eff;

    position: relative;
    z-index: 1;
    .el-input__wrapper {
      padding: 0 15px;
      overflow: hidden;
      background-color: rgb(255 255 255 / 25%);
      backdrop-filter: blur(10px);
      border: 1px solid rgb(255 255 255 / 10%);
      border-radius: 16px;
      box-shadow: 0 0 0 1px rgb(255 255 255 / 30%) inset;
      transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);

      // 添加光效应
      &::before {
        position: absolute;
        top: -10px;
        left: -10px;
        width: 40px;
        height: 40px;
        content: "";
        background: rgb(255 255 255 / 50%);
        filter: blur(30px);
        opacity: 0;
        transition: opacity 0.3s ease;
      }
      &:hover {
        background-color: rgb(255 255 255 / 35%);
        border-color: rgb(255 255 255 / 30%);
        box-shadow:
          0 0 0 1px var(--el-input-hover-border-color) inset,
          0 5px 15px rgb(0 0 0 / 10%);
        transform: translateY(-3px) scale(1.01);
        &::before {
          opacity: 1;
          animation: shine 1.5s infinite alternate;
        }
      }
      &.is-focus {
        background-color: rgb(255 255 255 / 40%);
        border-color: rgb(64 158 255 / 30%);
        box-shadow:
          0 0 0 2px var(--el-input-focus-border-color) inset,
          0 8px 20px rgb(0 0 0 / 10%);
        transform: translateY(-3px) scale(1.02);
      }
    }
    .el-input__inner {
      height: 45px;
      font-size: 16px;
      font-weight: 500;
      color: #333333;
      letter-spacing: 0.3px;
    }
    .el-input__icon {
      font-size: 20px;
      color: rgb(64 158 255 / 70%);
      filter: drop-shadow(0 0 2px rgb(64 158 255 / 30%));
      transition: all 0.3s ease;
      &:hover {
        transform: scale(1.1);
      }
    }
    &:hover .el-input__icon {
      color: var(--el-input-hover-border-color);
      animation: pulse 2s infinite;
    }
  }

  // 密码框特殊样式
  .el-input--password {
    .el-input__wrapper {
      padding-right: 45px;
    }
    .el-input__icon.is-clickable {
      color: rgb(103 194 58 / 70%);
      &:hover {
        color: rgb(103 194 58 / 100%);
      }
    }
  }

  // 添加按钮动效
  .login-btn {
    .el-button {
      position: relative;
      overflow: hidden;
      border-radius: 20px;
      transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
      &::before {
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        content: "";
        background: linear-gradient(90deg, transparent, rgb(255 255 255 / 20%), transparent);
        transition: all 0.4s ease;
      }
      &:hover {
        box-shadow: 0 8px 25px rgb(0 0 0 / 15%);
        transform: translateY(-5px);
        &::before {
          left: 100%;
          transition: all 0.8s ease;
        }
      }
      &:active {
        box-shadow: 0 5px 15px rgb(0 0 0 / 10%);
        transform: translateY(-2px);
      }
    }
  }
}

// 动画定义
@keyframes shine {
  0% {
    left: -100px;
    opacity: 0.5;
  }
  100% {
    left: 100%;
    opacity: 0.2;
  }
}

@keyframes pulse {
  0% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  100% {
    opacity: 0.8;
    transform: scale(1);
  }
}
.el-link {
  position: relative;
  margin-right: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  &::after {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 1px;
    content: "";
    background: currentColor;
    opacity: 0.7;
    transition: width 0.3s ease;
  }
  &:hover {
    text-shadow: 0 2px 4px rgb(0 0 0 / 10%);
    transform: translateY(-2px);
    &::after {
      width: 100%;
    }
  }
}
.el-link .el-icon--right.el-icon {
  vertical-align: text-bottom;
  transition: transform 0.3s ease;
}
.el-link:hover .el-icon--right.el-icon {
  transform: translateX(3px);
}
