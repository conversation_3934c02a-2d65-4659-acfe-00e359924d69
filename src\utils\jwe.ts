import * as jose from "jose";

// 加密函数 (兼容 jose v5.x)
/**
export async function encryptData(payload: any, publicKey: string) {
  try {
    // 解析 JWK 格式公钥
    const publicKeyJWK = await jose.importJWK(JSON.parse(publicKey), "RSA-OAEP-256");

    // 加密数据
    const encrypted = await new jose.CompactEncrypt(new TextEncoder().encode(payload))
      .setProtectedHeader({ alg: "RSA-OAEP-256", enc: "A256GCM" })
      .encrypt(publicKeyJWK);

    return encrypted;
  } catch (error) {
    console.error("Encryption failed:", error);
    throw error;
  }
}

// 解密函数 (兼容 jose v5.x)
export async function decryptData(jweToken: string, privateKey: string) {
  try {
    // 解析 PKCS8 格式私钥
    const privateKeyJWK = await jose.importPKCS8(privateKey, "RSA-OAEP-256");

    // 解密数据
    const { plaintext } = await jose.compactDecrypt(jweToken, privateKeyJWK);
    return new TextDecoder().decode(plaintext);
  } catch (error) {
    console.error("Decryption failed:", error);
    throw error;
  }
}
**/

export async function encryptData(payload: any, publicKey: string) {
  try {
    // 将任意类型的数据转换为字符串
    const payloadString = typeof payload === "string" ? payload : JSON.stringify(payload);

    // 解析 JWK 格式公钥
    const publicKeyJWK = await jose.importJWK(JSON.parse(publicKey), "RSA-OAEP-256");

    // 加密数据
    const encrypted = await new jose.CompactEncrypt(new TextEncoder().encode(payloadString))
      .setProtectedHeader({ alg: "RSA-OAEP-256", enc: "A256GCM" })
      .encrypt(publicKeyJWK);

    return encrypted;
  } catch (error) {
    console.error("Encryption failed:", error);
    throw error;
  }
}

export async function decryptData(jweToken: string, privateKey: string) {
  try {
    // 解析 PKCS8 格式私钥
    const privateKeyJWK = await jose.importPKCS8(privateKey, "RSA-OAEP-256");

    // 解密数据
    const { plaintext } = await jose.compactDecrypt(jweToken, privateKeyJWK);
    const decryptedString = new TextDecoder().decode(plaintext);

    // 尝试将解密后的字符串解析为原始类型
    try {
      return JSON.parse(decryptedString);
    } catch (jsonError) {
      // 如果解析失败，返回原始字符串
      return decryptedString;
    }
  } catch (error) {
    console.error("Decryption failed:", error);
    throw error;
  }
}

// const publicKey =
//   '{"kty":"RSA","e":"AQAB","n":"szg3mon_GPg8pLsXoErAyZx3OzGApAvNDOhjgwPeSbeRVNAp-Hk3y1yuPnCdj9U95zCeUqGcQ9r8KSXnfwkcEtmcmH5dasZULp4-Fwrnsdgu2OX9B8mPrUrtz9-_3ckjshATb2ji9gyJFQUMTGnwNaSysC8IFy-djtVKCSuIKmL-6KOOPIPBDcYAWD7596DmCx3Lm11__0HO9HQTg-CY3mx0cKf3_dMBm1zld0q7xx2zYFPXouW_Pj80e_mhxygmirW3g9TbrGp8ibl0V_hKXsQI5fbqjQXLgS2oym7q4CiF9GbAPGciSRoBKrbv84tqCYDC9BWquKd4WhocO2aGSw"}';
// const privateKey =
//   "-----BEGIN PRIVATE KEY-----\\nMIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCzODeaif8Y+DykuxegSsDJnHc7MYCkC80M6GODA95Jt5FU0Cn4eTfLXK4+cJ2P1T3nMJ5SoZxD2vwpJed/CRwS2ZyYfl1qxlQunj4XCuex2C7Y5f0HyY+tSu3P37/dySOyEBNvaOL2DIkVBQxMafA1pLKwLwgXL52O1UoJK4gqYv7oo448g8ENxgBYPvn3oOYLHcubXX//Qc70dBOD4JjebHRwp/f90wGbXOV3SrvHHbNgU9ei5b8+PzR7+aHHKCaKtbeD1NusanyJuXRX+EpexAjl9uqNBcuBLajKburgKIX0ZsA8ZyJJGgEqtu/zi2oJgML0Faq4p3haGhw7ZoZLAgMBAAECggEAPkCmS9doVVeQ1RWp4MNmkQF5BwYsbY8t1DB6tH0o/wk1AbVnBsiwYULZwZWjnV0Qahhgvkolugo8cdyZO27CIigVtYCbWWX3wtOztJRUo9l4eF33FPfmIOc4kO7jOhpOfGEKJzZTM+1pxnPg8EASMMf7S6iikk83VvBujZ3h5/qzOyg2hcv/Ya/oVOKit24NX0oFx2omaAo9oFChtuyrMplohXrcTcWZ6QfemzkcXLlEC0u/UklzVWyMUSwjjzt4ZplRkkj4jA6+d6+SLGItX8sO+MGXxoy2vSYn/g1Kex8f4IlGdt5s6g9rFumdyM5ulcCAlL3gsFuYQTuSWaa25QKBgQDQCQkBrG9ZEZAoEtGYFbcLCs53vnI6hgEDPC7mSb4gDnRKXy3c05/bT517hvQk2laQ+ROHPbADJ/Yj1eHWKY86KK+qZRV4A2QYyZODnEukl+JFQUWSCdiVJBFCZQ3oAA/nmjDqx1RTT2nZMwHFqEacvNJBwzBVtE4LH4hPuKV3TQKBgQDcimE0sgNVWWTBark+b4gccldbLc28pBMaZhzcZhsu9DrFQi7TM/CF6mLOEBOSECTOmy+HWLOmbfIvQbzmfnawuW1soK22j/X+ScKK0g93F1hC9OU/1odqYEWp/xjjKH/owzhEuw/DbSGpZRdTXo7gt/U3/xQQWr2ERd4fJuqX9wKBgBZrktvqUInB2c5n0MzaMkLvZDcFgFwMSxmlJ8zLz8C2rhPB1DiDWiwRlYBVmARMv52aKl+d51U/vkg4acFh8zyqW9w6TqkDfIsrVkmN050mfU1Hbo8BnqTcITwNlgUV1KD6PiFfYBgma9rro1pA+3My6loKBjCGYQ7T1CX7WWHVAoGAd2iFUR7BJ0kLgJ3kRqXksGJIYhnetzCvFzAKmFrAhR+/YmZ/K+OHazqI3MFfl0oeY68qgMHKI2aMJoIVYUc/xFfho1ssMxinPFN7feZWcUi2hSpLpdDl6PbPQUfZlcSr3L82jCAsXMeN3kXZ503fVlTD9NmrZ196FVAu//b2NekCgYBEFUDx57a0W1BH1Tys/A7TWwUJKG16FN4AYM87ljgWuCTODMm+RSQrhaPEFtal6Ww/BMrvFAgFP+e07SgKilB6P4fcB+xKdpX3gcVCVdinWAk1/D+p3dehGIOlMYXKFqju5RnWJ79yfiqfAnrW53QwVC1AdHZLytkyOkAmmKSi7Q==\\n-----END PRIVATE KEY-----";

// 主函数，用于测试加密和解密
async function main() {
  try {
    // // 测试密钥对（需要替换为真实有效的密钥对）
    // const publicKey =
    //   '{"kty":"RSA","e":"AQAB","n":"szg3mon_GPg8pLsXoErAyZx3OzGApAvNDOhjgwPeSbeRVNAp-Hk3y1yuPnCdj9U95zCeUqGcQ9r8KSXnfwkcEtmcmH5dasZULp4-Fwrnsdgu2OX9B8mPrUrtz9-_3ckjshATb2ji9gyJFQUMTGnwNaSysC8IFy-djtVKCSuIKmL-6KOOPIPBDcYAWD7596DmCx3Lm11__0HO9HQTg-CY3mx0cKf3_dMBm1zld0q7xx2zYFPXouW_Pj80e_mhxygmirW3g9TbrGp8ibl0V_hKXsQI5fbqjQXLgS2oym7q4CiF9GbAPGciSRoBKrbv84tqCYDC9BWquKd4WhocO2aGSw"}';
    // const privateKey =
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

    // 测试密钥对（需要替换为真实有效的密钥对）
    const publicKey =
      '{"kty":"RSA","e":"AQAB","n":"mTQHMdu9ZEPhPvZM35V2xxw-cw2Ann12LRB85i8Jzz3OR4tdl9tuWcrHRKGy4BAty2tdZ5Q19p_X6kOikNq4TivSnl6QILIuk9pUqUOAGU9b2ob0Y5tyrsAD2xwWILMV0_Yed94Tz8zli5uSgZlpvEpn_Ho5fGcr3wJkOxGdU1sTuxE-jExkVvx5g8rUqlCAgEZQMIdVW_BxWRs09VAMNHFlIN_gzqQkjkjSjTQv0xpld0HiBJLW9GskVJ38JP0xG0x3JRmdsS81ts114x7LeKW-bn7b57AWKx9w24ycnxnBF9qeopc7RYy1VL5mKiGRaszkytzghcQTjZBTir5vIw"}';
    const privateKey =
*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

    // 原始数据
    const originalData = {
      cmd: 4,
      deviceId: "22F0100080CFFD",
      userId: "78T34WV4RI3R",
      data: {
        system: ["userList"]
      }
    };
    console.log("Original Data:", originalData);

    // 加密测试
    const encrypted = await encryptData(originalData, publicKey);
    console.log("\nEncrypted JWE:", encrypted);

    // 解密测试
    const decrypted = await decryptData(encrypted, privateKey);
    console.log("\nDecrypted Data:", decrypted);

    // 验证结果
    console.log("\nTest Result:", JSON.stringify(originalData) === JSON.stringify(decrypted) ? "✅ Success" : "❌ Failed");
  } catch (error) {
    console.error("Main test error:", error);
  }
}

// 执行测试
main();
