<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拓扑图与项目目录合并实现</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #1890ff;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 10px;
        }
        h2 {
            color: #1890ff;
            margin-top: 30px;
        }
        .implementation {
            background-color: #f8f8f8;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .code {
            background-color: #f5f5f5;
            border-left: 4px solid #1890ff;
            padding: 15px;
            margin: 15px 0;
            overflow-x: auto;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
        }
        .screenshot {
            max-width: 100%;
            border: 1px solid #eee;
            border-radius: 4px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .note {
            background-color: #e6f7ff;
            border-left: 4px solid #1890ff;
            padding: 10px 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <h1>拓扑图与项目目录合并实现</h1>
    
    <div class="implementation">
        <h2>实现概述</h2>
        <p>根据需求，我们将拓扑图功能合并到项目目录功能中，并以标签页形式显示，默认显示拓扑图标签页。</p>
        
        <h2>主要修改</h2>
        <ol>
            <li>在 <code>src/views/project/index.vue</code> 中添加标签页组件，创建两个标签页：拓扑图和项目目录</li>
            <li>从 <code>topology/index.vue</code> 导入 D3Topology 组件和相关逻辑</li>
            <li>确保两个标签页共享相同的 TreeFilter 组件和项目选择逻辑</li>
            <li>调整 CSS 样式，确保拓扑图在标签页中正确显示</li>
        </ol>
        
        <h2>关键代码片段</h2>
        
        <h3>标签页结构</h3>
        <div class="code">
<pre>&lt;el-tabs v-model="activeTab" class="tabs-container"&gt;
  &lt;!-- 拓扑图标签页 --&gt;
  &lt;el-tab-pane :label="t('topology.title')" name="topology"&gt;
    &lt;div class="topology-tab-content"&gt;
      &lt;D3Topology
        :data="topologyData"
        :width="chartWidth"
        :height="chartHeight"
        @node-click="handleNodeClick"
        @refresh="handleLoad"
      /&gt;
    &lt;/div&gt;
  &lt;/el-tab-pane&gt;
  &lt;!-- 项目目录标签页 --&gt;
  &lt;el-tab-pane :label="t('project.title')" name="project"&gt;
    &lt;div class="project-tab-content"&gt;
      &lt;!-- 原项目目录内容 --&gt;
    &lt;/div&gt;
  &lt;/el-tab-pane&gt;
&lt;/el-tabs&gt;</pre>
        </div>
        
        <h3>CSS 样式调整</h3>
        <div class="code">
<pre>.main-box {
  /* 其他样式... */
  height: calc(100vh - 120px); /* 设置合适的高度 */
}

.table-box {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  height: 100%;
  
  /* 标签页容器样式 */
  .tabs-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    
    :deep(.el-tabs__content) {
      flex: 1;
      overflow: hidden;
    }
    
    :deep(.el-tab-pane) {
      height: 100%;
    }
  }
  
  /* 拓扑图标签页内容 */
  .topology-tab-content {
    height: 100%;
    position: relative;
    
    /* 让D3Topology组件充满容器 */
    :deep(.topology-wrapper) {
      width: 100% !important;
      height: 100% !important;
      overflow: hidden;
    }
  }
}</pre>
        </div>
        
        <h3>数据加载逻辑</h3>
        <div class="code">
<pre>// 树形筛选切换
const changeTreeFilter = (val: string | string[]) => {
  // 确保 groupId 是字符串而不是数组
  initParam.groupId = val;

  // 更新项目目录数据
  proTable.value!.pageable.current = 1;
  fetchDeviceStatistics();

  // 清空拓扑数据
  topologyData.children = [];

  // 使用防抖机制，避免短时间内发送多次请求
  debounceTimer = window.setTimeout(() => {
    // 当切换树形筛选时，重新加载数据
    if (initParam.groupId) {
      handleLoad();
    }
  }, 300);
};</pre>
        </div>
        
        <h2>实现效果</h2>
        <p>实现后的效果如下：</p>
        <ul>
            <li>页面加载时默认显示拓扑图标签页</li>
            <li>用户可以通过标签页切换查看拓扑图或项目目录</li>
            <li>两个标签页共享相同的项目选择逻辑，切换项目时两个标签页的数据都会更新</li>
            <li>拓扑图在标签页中能够正确显示，并且能够响应窗口大小变化</li>
        </ul>
        
        <div class="note">
            <p><strong>注意：</strong> 由于拓扑图和项目目录共享相同的项目选择逻辑，因此在切换项目时，两个标签页的数据都会更新。这样可以确保用户在不同标签页之间切换时看到的是同一个项目的数据。</p>
        </div>
    </div>
</body>
</html>
