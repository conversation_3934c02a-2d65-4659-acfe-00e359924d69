<template>
  <div class="topology-legend">
    <div class="legend-title">{{ t("topology.legend") }}</div>
    <div class="legend-item">
      <div class="legend-line solid"></div>
      <div class="legend-text">{{ t("network.wiredConnection") }}</div>
    </div>
    <div class="legend-item">
      <div class="legend-line dashed"></div>
      <div class="legend-text">{{ t("network.wirelessConnection") }}</div>
    </div>
    <div class="legend-item">
      <div class="legend-device online">
        <div class="legend-device-icon"></div>
      </div>
      <div class="legend-text">{{ t("topology.online") }}</div>
    </div>
    <div class="legend-item">
      <div class="legend-device offline">
        <div class="legend-device-icon"></div>
        <div class="legend-device-marker"></div>
      </div>
      <div class="legend-text">{{ t("topology.offline") }}</div>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from "vue-i18n";
import { useGlobalStore } from "@/stores/modules/global";
import { computed } from "vue";

const { t } = useI18n();
const globalStore = useGlobalStore();
const isDark = computed(() => globalStore.isDark);
</script>

<style scoped>
/* 图例样式 */
.topology-legend {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 100;
  min-width: 150px;
  padding: 10px;
  background-color: v-bind('isDark ? "rgba(30, 30, 30, 0.8)" : "rgba(255, 255, 255, 0.8)"');
  border: 1px solid v-bind('isDark ? "rgba(255, 255, 255, 0.1)" : "rgba(0, 0, 0, 0.05)"');
  border-radius: 5px;
  box-shadow: v-bind('isDark ? "0 2px 12px 0 rgba(0, 0, 0, 0.3)" : "0 2px 12px 0 rgba(0, 0, 0, 0.1)"');
}
.legend-title {
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: bold;
  color: v-bind('isDark ? "#ffffff" : "#333"');
  text-shadow: v-bind('isDark ? "0 1px 2px rgba(0, 0, 0, 0.8)" : "none"');
}
.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
}
.legend-line {
  width: 30px;
  height: 2px;
  margin-right: 10px;
  background-color: #409eff;
}
.legend-line.solid {
  /* 实线样式 */
  height: 2px;
  background-color: #409eff;
}
.legend-line.dashed {
  height: 0;
  background-color: transparent;
  border-top: 2px dashed #409eff;
}

/* 设备图例样式 */
.legend-device {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin-right: 10px;
}
.legend-device-icon {
  width: 20px;
  height: 20px;
  background-color: #409eff;
  border-radius: 4px;
}

/* 在线设备样式 */
.legend-device.online .legend-device-icon {
  opacity: 1;
}

/* 离线设备样式 */
.legend-device.offline .legend-device-icon {
  background-color: #999999;
  filter: grayscale(100%);
  opacity: 0.6;
}

/* 离线标记 */
.legend-device-marker {
  position: absolute;
  top: 0;
  right: 0;
  width: 8px;
  height: 8px;
  background-color: #ff0000;
  border: 1px solid v-bind('isDark ? "#333" : "#ffffff"');
  border-radius: 50%;
  box-shadow: v-bind('isDark ? "0 0 3px rgba(255, 255, 255, 0.5)" : "none"');
}
.legend-text {
  font-size: 12px;
  color: v-bind('isDark ? "#ffffff" : "#333"');
  text-shadow: v-bind('isDark ? "0 1px 2px rgba(0, 0, 0, 0.8)" : "none"');
}
</style>
