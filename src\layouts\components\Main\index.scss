.table-box {
  padding: 20px;
  background-color: var(--el-bg-color);
  border-radius: 16px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);
  transition: all 0.3s ease;
  &:hover {
    box-shadow: 0 4px 16px 0 rgb(0 0 0 / 10%);
  }
  :deep(.el-table) {
    overflow: hidden;
    border-radius: 8px;
    th {
      font-weight: 600;
      color: var(--el-text-color-primary);
      background-color: var(--el-bg-color-page);
    }
    td {
      padding: 12px 0;
    }
    .el-table__row {
      transition: all 0.3s ease;
      &:hover {
        background-color: var(--el-fill-color-light);
      }
    }
  }
  :deep(.el-pagination) {
    justify-content: flex-end;
    padding: 0 20px;
    margin-top: 20px;
  }
  :deep(.el-input__wrapper) {
    box-shadow: 0 0 0 1px var(--el-border-color) inset;
    transition: all 0.3s ease;
    &:hover {
      box-shadow: 0 0 0 1px var(--el-border-color-hover) inset;
    }
    &.is-focus {
      box-shadow: 0 0 0 1px var(--el-color-primary) inset;
    }
  }
  :deep(.el-button) {
    transition: all 0.3s ease;
    &.view-button {
      &:hover {
        box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
        transform: translateY(-1px);
      }
    }
  }
  :deep(.el-table__header-wrapper) {
    th {
      font-weight: 600;
      color: var(--el-text-color-primary);
      background-color: var(--el-bg-color-page);
    }
  }
  :deep(.el-table__body-wrapper) {
    td {
      color: var(--el-text-color-primary);
    }
  }
}
.el-footer {
  height: auto;
  padding: 0;
}
