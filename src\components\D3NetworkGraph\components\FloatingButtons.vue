<template>
  <div class="floating-buttons">
    <button class="reset-button" @click="resetZoom" :title="t('topology.resetZoom')"></button>
  </div>
</template>

<script setup>
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const emit = defineEmits(["resetZoom"]);

const resetZoom = () => {
  emit("resetZoom");
};
</script>

<style scoped>
/* 悬浮按钮样式 */
.floating-buttons {
  position: absolute; /* 使用absolute定位，相对于父容器 */
  right: 20px;
  bottom: 20px; /* 距离底部 */
  z-index: 9999; /* 增加z-index值，确保始终在最上层 */
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center; /* 确保按钮居中对齐 */
  width: 48px; /* 固定宽度，确保所有按钮对齐 */
  pointer-events: auto !important; /* 确保可以点击 */
}
.reset-button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px; /* 增大按钮尺寸 */
  height: 48px;
  color: white;
  cursor: pointer;
  background-color: #409eff;
  border: none;
  border-radius: 50%;
  box-shadow: 0 4px 16px 0 rgb(0 0 0 / 20%);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

/* 使用伪元素创建图标 */
.reset-button::before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  content: "";
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  transform: translate(-50%, -50%);
}
.reset-button:hover {
  background-color: #66b1ff;
  box-shadow: 0 6px 20px 0 rgb(0 0 0 / 25%);
  transform: translateY(-3px);
}
.reset-button:active {
  background-color: #3a8ee6;
  box-shadow: 0 2px 10px 0 rgb(0 0 0 / 15%);
  transform: translateY(0);
}
</style>
