<template>
  <div class="verification-code-container">
    <el-input v-model="inputValue" v-bind="$attrs">
      <template #append>
        <el-button :disabled="countdown > 0" type="primary" class="send-button" @click="handleSendCode">
          {{ countdown > 0 ? $t("user.resendCodeTime", { time: countdown }) : $t("user.sendCode") }}
        </el-button>
      </template>
    </el-input>
  </div>
</template>

<script lang="ts" setup name="VerificationCode">
import { ref, watch, onBeforeUnmount } from "vue";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";
import { sendEmailCode, sendPhoneCode } from "@/api/modules/mine";
import { isRegisterApi } from "@/api/modules/login";

const { t } = useI18n();

// 定义组件属性
const props = defineProps({
  modelValue: {
    type: String,
    default: ""
  },
  type: {
    type: String,
    default: "email", // 'email' 或 'phone'
    validator: (value: string) => ["email", "phone", "auto"].includes(value)
  },
  value: {
    type: String,
    default: ""
  },
  originalValue: {
    type: String,
    default: ""
  },
  step: {
    type: Number,
    default: 0
  },
  checkExist: {
    type: Boolean,
    default: true
  }
});

// 定义事件
const emit = defineEmits(["update:modelValue", "sendSuccess", "sendError"]);

// 内部状态
const inputValue = ref(props.modelValue);
const countdown = ref(0);
const countdownTimer = ref<number | null>(null);

// 监听输入值变化
watch(inputValue, newValue => {
  emit("update:modelValue", newValue);
});

// 监听props变化
watch(
  () => props.modelValue,
  newValue => {
    inputValue.value = newValue;
  }
);

// 邮箱正则表达式
const emailRegex =
  /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[^\s@](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+$/;

// 手机号正则表达式
const phoneRegex = /^(?:(?:\+|00)86)?1(?:6[567]|7[013-8]|3\d|5[0-35-9]|8\d|9\d)\d{8}$/;

// 开始倒计时函数
const startCountdown = () => {
  // 清除之前的定时器
  if (countdownTimer.value) {
    window.clearInterval(countdownTimer.value);
  }

  // 设置倒计时初始值为180秒（3分钟）
  countdown.value = 180;

  // 创建定时器，每秒减1
  countdownTimer.value = window.setInterval(() => {
    if (countdown.value > 0) {
      countdown.value--;
    } else {
      // 倒计时结束，清除定时器
      if (countdownTimer.value) {
        window.clearInterval(countdownTimer.value);
        countdownTimer.value = null;
      }
    }
  }, 1000);
};

// 发送验证码
const handleSendCode = async () => {
  try {
    const value = props.value;
    if (!value) {
      ElMessage.warning({ message: t("user.emailOrPhoneNotEmpty") });
      return;
    }

    // 自动检测类型或使用指定类型
    let type = props.type;
    if (type === "auto") {
      if (emailRegex.test(value)) {
        type = "email";
      } else if (phoneRegex.test(value)) {
        type = "phone";
      } else {
        ElMessage.warning({ message: t("user.emailOrPhoneErrorTip") });
        return;
      }
    } else if (type === "email" && !emailRegex.test(value)) {
      ElMessage.warning({ message: t("user.emailErrorTip") });
      return;
    } else if (type === "phone" && !phoneRegex.test(value)) {
      ElMessage.warning({ message: t("user.phoneErrorTip") });
      return;
    }

    // 第一步验证时，检查是否是当前用户的邮箱/手机号
    if (props.step === 0 && props.originalValue && value !== props.originalValue) {
      ElMessage.warning({
        message: type === "email" ? t("user.notCurrentUserEmail") : t("user.notCurrentUserPhone")
      });
      return;
    }

    // 第二步设置新值时，检查是否已被其他用户绑定
    if (props.step === 1 && props.checkExist) {
      const isExist = await isRegisterApi(value);
      if (isExist && isExist.code !== "200") {
        ElMessage.warning({
          message: type === "email" ? t("user.emailBeenBind") : t("user.phoneBeenBind")
        });
        return;
      }
    }

    // 发送验证码
    let response;
    if (type === "email") {
      response = await sendEmailCode({ email: value });
    } else {
      response = await sendPhoneCode({ phone: value });
    }

    if (!response || response.code !== "200") {
      ElMessage.error({ message: response?.msg || t("user.sendCodeFail") });
      emit("sendError", response);
      return;
    }

    ElMessage.success({ message: t("user.sendCodeSuccess") + ", " + t("user.codeValidTime") });
    startCountdown();
    emit("sendSuccess");
  } catch (error) {
    console.error("发送验证码失败:", error);
    ElMessage.error({ message: t("user.sendCodeFail") });
    emit("sendError", error);
  }
};

// 组件卸载前清除定时器
onBeforeUnmount(() => {
  if (countdownTimer.value) {
    window.clearInterval(countdownTimer.value);
    countdownTimer.value = null;
  }
});
</script>

<style lang="scss" scoped>
.verification-code-container {
  width: 100%;
  .send-button {
    height: 32px !important;
    padding: 0 16px;
    margin-left: 0;
    font-size: 14px;
    border-radius: 4px;
  }
}
</style>
