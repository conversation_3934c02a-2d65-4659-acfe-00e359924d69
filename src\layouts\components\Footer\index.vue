<template>
  <div class="footer">
    <div class="footer-content">
      <div class="footer-main-row">
        <div class="beian-info">
          <a
            class="beian-link"
            href="https://beian.miit.gov.cn"
            target="_blank"
            rel="noopener noreferrer"
            :title="$t('footer.beianLink')"
          >
            {{ $t("footer.beianNumber") }}
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 页尾组件不需要引入useI18n，因为模板中直接使用了$t()
</script>

<style scoped lang="scss">
.footer {
  min-height: 32px;
  padding: 6px 12px;
  background: linear-gradient(135deg, var(--el-bg-color) 0%, var(--el-bg-color-page) 100%);
  border-top: 1px solid var(--el-border-color-light);
  box-shadow: 0 -2px 8px rgb(0 0 0 / 4%);
  transition: all 0.3s ease;
  .footer-content {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 1400px;
    min-height: 28px;
    margin: 0 auto;
  }
  .footer-main-row {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    min-width: 0;
    font-size: 11px;
    .beian-info {
      display: flex;
      align-items: center;
      justify-content: center;
      .beian-link {
        display: inline-flex;
        align-items: center;
        padding: 2px 8px;
        font-size: 10px;
        color: #999999;
        text-decoration: none;
        letter-spacing: 0.1px;
        background: transparent;
        border-radius: 6px;
        opacity: 0.6;
        transition: all 0.2s ease;
        &:hover {
          color: #4b73c9;
          background: rgb(64 158 255 / 5%);
          opacity: 0.8;
        }
      }
    }
  }

  // 移动端自动变为上下排列
  @media screen and (width <= 480px) {
    min-height: 26px;
    padding: 3px 4px;
    .footer-content {
      align-items: center;
      justify-content: center;
      width: 100%;
      min-width: 0;
    }
    .footer-main-row {
      flex-direction: column;
      gap: 2px;
      align-items: center;
      justify-content: center;
      width: 100%;
      font-size: 11px;
      .copyright,
      .beian-info {
        justify-content: center;
        width: 100%;
        overflow: visible;
        text-align: center;
        word-break: break-all;
        white-space: normal;
      }
      .beian-link {
        padding: 2px 8px 2px 6px;
        font-size: 11px;
        border-radius: 10px;
        .iconfont {
          font-size: 12px;
        }
      }
    }
  }

  @media screen and (width <= 350px) {
    .footer-main-row {
      gap: 1px;
      font-size: 10px;
    }
    .beian-link {
      padding: 2px 4px;
      font-size: 10px;
    }
  }

  // 暗色主题适配
  .dark & {
    background: linear-gradient(135deg, var(--el-bg-color) 0%, rgb(0 0 0 / 10%) 100%);
    border-top-color: var(--el-border-color);
    box-shadow: 0 -2px 8px rgb(0 0 0 / 15%);
    .beian-link {
      color: #666666;
      background: transparent;
      opacity: 0.5;
      &:hover {
        color: #8bb9ff;
        background: rgb(139 185 255 / 8%);
        opacity: 0.7;
      }
    }
  }
}
</style>
