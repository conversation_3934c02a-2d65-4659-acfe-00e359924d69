/* 全局毛玻璃效果样式 */

// 基础毛玻璃效果
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

// 卡片毛玻璃效果
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.2);
  }
}

// 背景渐变
.glass-bg {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

// 暗色模式适配
.dark {
  .glass-effect, .glass-card {
    background: rgba(30, 30, 30, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .glass-card:hover {
    background: rgba(40, 40, 40, 0.4);
  }
  
  .glass-bg {
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
  }
}
