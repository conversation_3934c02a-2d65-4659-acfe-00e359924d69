/* 自定义 element 暗黑模式 */
html.dark {
  /* 毛玻璃效果 - 暗色模式 */
  .glass-effect {
    background: rgb(30 30 30 / 30%);
    border: 1px solid rgb(255 255 255 / 10%);
  }
  .glass-card:hover {
    background: rgb(40 40 40 / 40%);
  }
  .glass-container {
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
    &::before {
      background: radial-gradient(circle, rgb(100 100 100 / 50%) 0%, rgb(100 100 100 / 0%) 70%);
    }
    &::after {
      background: radial-gradient(circle, rgb(70 130 180 / 30%) 0%, rgb(70 130 180 / 0%) 70%);
    }
  }

  /* 图表样式 - 暗色模式 */
  #echarts {
    .echarts-tooltip {
      color: #ffffff !important;
      background: rgb(30 30 30 / 80%) !important;
      border: 1px solid rgb(255 255 255 / 10%) !important;
    }

    /* 确保图例文字在暗色模式下可见 */
    .echarts-legend-text {
      color: #ffffff !important;
    }
  }

  /* 对话框 */
  .el-dialog {
    background: rgb(30 30 30 / 70%);
    border: 1px solid rgb(255 255 255 / 10%);
    .el-dialog__header {
      background: rgb(40 40 40 / 50%);
      border-bottom: 1px solid rgb(255 255 255 / 10%);
    }
    .el-dialog__body {
      /* 确保对话框中的文本可见 */
      color: #e5eaf3;
      background: rgb(30 30 30 / 30%);

      /* 对话框中的链接样式 */
      .el-link {
        font-weight: 600;
        color: #409eff !important;
        text-shadow: 0 0 8px rgb(64 158 255 / 50%);
        &:hover {
          color: #79bbff !important;
          text-shadow: 0 0 12px rgb(64 158 255 / 80%);
        }
      }
    }
    .el-dialog__footer {
      background: rgb(40 40 40 / 50%);
      border-top: 1px solid rgb(255 255 255 / 10%);
    }
  }

  /* wangEditor */
  --w-e-toolbar-color: #eeeeee;
  --w-e-toolbar-bg-color: #141414;
  --w-e-textarea-bg-color: #141414;
  --w-e-textarea-color: #eeeeee;
  --w-e-toolbar-active-bg-color: #464646;
  --w-e-toolbar-border-color: var(--el-border-color-darker);
  .w-e-bar-item button:hover,
  .w-e-menu-tooltip-v5::before {
    color: #eeeeee;
  }

  /* login */
  .login-container {
    background-color: #191919 !important;
    .login-box {
      background-color: rgb(0 0 0 / 80%) !important;
      .login-form {
        background-color: rgb(30 30 30 / 70%) !important;
        box-shadow: rgb(255 255 255 / 12%) 0 2px 10px 2px !important;
        .logo-text {
          color: var(--el-text-color-primary) !important;
        }

        /* 登录按钮区域 */
        .login-btn {
          color: #e5eaf3 !important;

          /* 注册和忘记密码链接 */
          .el-link {
            font-weight: 600;
            color: #409eff !important;
            text-shadow: 0 0 8px rgb(64 158 255 / 50%);
            &:hover {
              color: #79bbff !important;
              text-shadow: 0 0 12px rgb(64 158 255 / 80%);
            }
          }
        }
      }
    }
  }

  /* ECharts 图表组件样式 */
  .echarts-for-react,
  .echarts,
  #echarts,
  .chart-container {
    /* 确保图例文字在暗色模式下清晰可见 */
    .echarts-legend {
      color: #ffffff !important;
    }

    /* 修复图例文字颜色 */
    text {
      font-weight: bold !important;
      fill: #ffffff !important;
    }

    /* 直接针对图例元素 */
    .defs + g {
      g {
        text {
          font-size: 14px !important;
          font-weight: bold !important;
          fill: #ffffff !important;
        }
      }
    }

    /* 针对图例元素 - 更具体的选择器 */
    div[style*="position: absolute"] {
      padding: 5px !important;
      background-color: rgb(50 50 50 / 80%) !important;
      border: 1px solid rgb(255 255 255 / 20%) !important;
      border-radius: 4px !important;
      span {
        font-size: 14px !important;
        font-weight: bold !important;
        color: #ffffff !important;
      }
    }
  }
}
