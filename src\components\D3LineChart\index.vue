<template>
  <div class="d3-line-chart" ref="chartContainer" :style="{ width: `${width}px`, height: `${height}px` }"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from "vue";
import * as d3 from "d3";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

interface ChartData {
  date: string;
  rxByte: number;
  txByte: number;
}

// 格式化字节单位的函数
const formatBytes = (bytes: number): string => {
  if (bytes === 0) return "0 B";

  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

// 获取合适的单位和除数
const getBestUnit = (maxValue: number): { unit: string; divisor: number; formatValue: (value: number) => string } => {
  if (maxValue < 1024) {
    return {
      unit: "B",
      divisor: 1,
      formatValue: (value: number) => Math.round(value).toString()
    };
  } else if (maxValue < 1024 * 1024) {
    return {
      unit: "KB",
      divisor: 1024,
      formatValue: (value: number) => (value / 1024).toFixed(1)
    };
  } else if (maxValue < 1024 * 1024 * 1024) {
    return {
      unit: "MB",
      divisor: 1024 * 1024,
      formatValue: (value: number) => (value / (1024 * 1024)).toFixed(1)
    };
  } else if (maxValue < 1024 * 1024 * 1024 * 1024) {
    return {
      unit: "GB",
      divisor: 1024 * 1024 * 1024,
      formatValue: (value: number) => (value / (1024 * 1024 * 1024)).toFixed(2)
    };
  } else {
    return {
      unit: "TB",
      divisor: 1024 * 1024 * 1024 * 1024,
      formatValue: (value: number) => (value / (1024 * 1024 * 1024 * 1024)).toFixed(2)
    };
  }
};

interface Props {
  data: ChartData[];
  width?: number;
  height?: number;
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  width: 640,
  height: 400
});

const chartContainer = ref<HTMLElement>();
let svg: d3.Selection<SVGSVGElement, unknown, null, undefined>;
let resizeObserver: ResizeObserver;

// 图表边距
const margin = { top: 20, right: 80, bottom: 40, left: 60 };

// 创建图表
const createChart = () => {
  if (!chartContainer.value || !props.data.length) return;

  // 清除现有的SVG
  d3.select(chartContainer.value).selectAll("*").remove();

  // 计算实际绘制区域
  const width = props.width - margin.left - margin.right;
  const height = props.height - margin.top - margin.bottom;

  // 创建SVG
  svg = d3.select(chartContainer.value).append("svg").attr("width", props.width).attr("height", props.height);

  const g = svg.append("g").attr("transform", `translate(${margin.left},${margin.top})`);

  // 解析日期
  const parseDate = d3.timeParse("%Y-%m-%d");

  // 处理数据
  const data = props.data.map(d => ({
    date: parseDate(d.date)!,
    rxByte: d.rxByte,
    txByte: d.txByte
  }));

  // 根据数据量调整日期格式
  const formatDate = data.length <= 7 ? d3.timeFormat("%m-%d") : d3.timeFormat("%m/%d");

  // 设置比例尺
  const xScale = d3
    .scaleTime()
    .domain(d3.extent(data, d => d.date) as [Date, Date])
    .range([0, width]);

  const maxValue = d3.max(data, d => Math.max(d.rxByte, d.txByte)) || 0;

  // 获取最佳单位
  const { unit, divisor } = getBestUnit(maxValue);

  const yScale = d3
    .scaleLinear()
    .domain([0, maxValue / divisor])
    .nice()
    .range([height, 0]);

  // 创建线条生成器
  const rxLine = d3
    .line<{ date: Date; rxByte: number; txByte: number }>()
    .x(d => xScale(d.date))
    .y(d => yScale(d.rxByte / divisor))
    .curve(d3.curveMonotoneX);

  const txLine = d3
    .line<{ date: Date; rxByte: number; txByte: number }>()
    .x(d => xScale(d.date))
    .y(d => yScale(d.txByte / divisor))
    .curve(d3.curveMonotoneX);

  // 添加渐变定义
  const defs = svg.append("defs");

  const rxGradient = defs
    .append("linearGradient")
    .attr("id", "rxGradient")
    .attr("gradientUnits", "userSpaceOnUse")
    .attr("x1", 0)
    .attr("y1", height)
    .attr("x2", 0)
    .attr("y2", 0);

  rxGradient.append("stop").attr("offset", "0%").attr("stop-color", "#3b82f6").attr("stop-opacity", 0.1);

  rxGradient.append("stop").attr("offset", "100%").attr("stop-color", "#3b82f6").attr("stop-opacity", 0.3);

  const txGradient = defs
    .append("linearGradient")
    .attr("id", "txGradient")
    .attr("gradientUnits", "userSpaceOnUse")
    .attr("x1", 0)
    .attr("y1", height)
    .attr("x2", 0)
    .attr("y2", 0);

  txGradient.append("stop").attr("offset", "0%").attr("stop-color", "#ef4444").attr("stop-opacity", 0.1);

  txGradient.append("stop").attr("offset", "100%").attr("stop-color", "#ef4444").attr("stop-opacity", 0.3);

  // 创建面积生成器
  const rxArea = d3
    .area<{ date: Date; rxByte: number; txByte: number }>()
    .x(d => xScale(d.date))
    .y0(height)
    .y1(d => yScale(d.rxByte / divisor))
    .curve(d3.curveMonotoneX);

  const txArea = d3
    .area<{ date: Date; rxByte: number; txByte: number }>()
    .x(d => xScale(d.date))
    .y0(height)
    .y1(d => yScale(d.txByte / divisor))
    .curve(d3.curveMonotoneX);

  // 添加面积
  g.append("path").datum(data).attr("fill", "url(#rxGradient)").attr("d", rxArea);

  g.append("path").datum(data).attr("fill", "url(#txGradient)").attr("d", txArea);

  // 添加线条
  g.append("path").datum(data).attr("fill", "none").attr("stroke", "#3b82f6").attr("stroke-width", 2).attr("d", rxLine);

  g.append("path").datum(data).attr("fill", "none").attr("stroke", "#ef4444").attr("stroke-width", 2).attr("d", txLine);

  // 添加数据点
  g.selectAll(".rx-dot")
    .data(data)
    .enter()
    .append("circle")
    .attr("class", "rx-dot")
    .attr("cx", d => xScale(d.date))
    .attr("cy", d => yScale(d.rxByte / divisor))
    .attr("r", 4)
    .attr("fill", "#3b82f6")
    .attr("stroke", "#fff")
    .attr("stroke-width", 2);

  g.selectAll(".tx-dot")
    .data(data)
    .enter()
    .append("circle")
    .attr("class", "tx-dot")
    .attr("cx", d => xScale(d.date))
    .attr("cy", d => yScale(d.txByte / divisor))
    .attr("r", 4)
    .attr("fill", "#ef4444")
    .attr("stroke", "#fff")
    .attr("stroke-width", 2);

  // 添加坐标轴
  // 根据数据点数量和宽度动态调整刻度数量，避免日期重复
  const maxTicks = Math.floor(width / 60); // 每60px一个刻度，给日期更多空间
  const tickCount = Math.min(data.length, maxTicks, 10); // 最多显示10个刻度

  const xAxis = d3
    .axisBottom(xScale)
    .ticks(tickCount)
    .tickFormat(formatDate as any);
  const yAxis = d3.axisLeft(yScale).tickFormat((d: any) => d + " " + unit);

  g.append("g")
    .attr("transform", `translate(0,${height})`)
    .call(xAxis)
    .selectAll("text")
    .style("text-anchor", "middle")
    .style("font-size", "11px");

  g.append("g").call(yAxis);

  // 添加网格线
  g.append("g")
    .attr("class", "grid")
    .attr("transform", `translate(0,${height})`)
    .call(
      d3
        .axisBottom(xScale)
        .ticks(tickCount)
        .tickSize(-height)
        .tickFormat("" as any)
    )
    .style("stroke-dasharray", "3,3")
    .style("opacity", 0.3);

  g.append("g")
    .attr("class", "grid")
    .call(
      d3
        .axisLeft(yScale)
        .tickSize(-width)
        .tickFormat("" as any)
    )
    .style("stroke-dasharray", "3,3")
    .style("opacity", 0.3);

  // 添加图例
  const legend = g
    .append("g")
    .attr("class", "legend")
    .attr("transform", `translate(${width - 120}, 20)`);

  // 接收字节图例
  const rxLegend = legend.append("g").attr("class", "rx-legend");
  rxLegend
    .append("line")
    .attr("x1", 0)
    .attr("y1", 0)
    .attr("x2", 20)
    .attr("y2", 0)
    .attr("stroke", "#3b82f6")
    .attr("stroke-width", 2);
  rxLegend
    .append("text")
    .attr("x", 25)
    .attr("y", 0)
    .attr("dy", "0.35em")
    .attr("font-size", "12px")
    .text(t("common.receivedBytes"));

  // 发送字节图例
  const txLegend = legend.append("g").attr("class", "tx-legend").attr("transform", "translate(0, 20)");
  txLegend
    .append("line")
    .attr("x1", 0)
    .attr("y1", 0)
    .attr("x2", 20)
    .attr("y2", 0)
    .attr("stroke", "#ef4444")
    .attr("stroke-width", 2);
  txLegend
    .append("text")
    .attr("x", 25)
    .attr("y", 0)
    .attr("dy", "0.35em")
    .attr("font-size", "12px")
    .text(t("common.transmittedBytes"));

  // 添加工具提示
  const tooltip = d3
    .select("body")
    .append("div")
    .attr("class", "d3-tooltip")
    .style("opacity", 0)
    .style("position", "absolute")
    .style("background", "rgba(0, 0, 0, 0.8)")
    .style("color", "white")
    .style("padding", "8px")
    .style("border-radius", "4px")
    .style("font-size", "12px")
    .style("pointer-events", "none")
    .style("z-index", "1000");

  // 添加交互覆盖层
  g.append("rect")
    .attr("width", width)
    .attr("height", height)
    .attr("fill", "none")
    .attr("pointer-events", "all")
    .on("mousemove", function (event) {
      const [mouseX] = d3.pointer(event);
      const x0 = xScale.invert(mouseX);
      const bisectDate = d3.bisector((d: any) => d.date).left;
      const i = bisectDate(data, x0, 1);
      const d0 = data[i - 1];
      const d1 = data[i];
      const d = d0 && d1 && x0.getTime() - d0.date.getTime() > d1.date.getTime() - x0.getTime() ? d1 : d0;

      if (d) {
        tooltip.transition().duration(200).style("opacity", 0.9);
        tooltip
          .html(
            `
            <div><strong>${formatDate(d.date)}</strong></div>
            <div>${t("common.receivedBytes")}: ${formatBytes(d.rxByte)}</div>
            <div>${t("common.transmittedBytes")}: ${formatBytes(d.txByte)}</div>
          `
          )
          .style("left", event.pageX + 10 + "px")
          .style("top", event.pageY - 28 + "px");
      }
    })
    .on("mouseout", function () {
      tooltip.transition().duration(500).style("opacity", 0);
    });
};

// 监听数据变化
watch(
  () => props.data,
  () => {
    nextTick(() => {
      createChart();
    });
  },
  { deep: true }
);

// 监听尺寸变化
watch([() => props.width, () => props.height], () => {
  nextTick(() => {
    createChart();
  });
});

onMounted(() => {
  nextTick(() => {
    createChart();
  });

  // 监听容器尺寸变化
  if (chartContainer.value) {
    resizeObserver = new ResizeObserver(() => {
      createChart();
    });
    resizeObserver.observe(chartContainer.value);
  }
});

onBeforeUnmount(() => {
  // 清理工具提示
  d3.selectAll(".d3-tooltip").remove();

  // 清理观察器
  if (resizeObserver && chartContainer.value) {
    resizeObserver.unobserve(chartContainer.value);
  }
});
</script>

<style scoped>
.d3-line-chart {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
:deep(.grid line) {
  stroke: #e0e0e0;
}
:deep(.grid path) {
  stroke-width: 0;
}
:deep(.domain) {
  stroke: #666666;
}
:deep(text) {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  fill: #666666;
}
</style>
