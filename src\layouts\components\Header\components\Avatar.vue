<template>
  <el-dropdown trigger="click">
    <div class="avatar">
      <img src="@/assets/images/logo-circle.png" alt="avatar" />
    </div>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item @click="navigateToMine()">
          <el-icon><User /></el-icon>{{ $t("header.personalData") }}
        </el-dropdown-item>
        <el-dropdown-item @click="openPasswordDrawer">
          <el-icon><Edit /></el-icon>{{ $t("header.changePassword") }}
        </el-dropdown-item>
        <el-dropdown-item divided @click="logout">
          <el-icon><SwitchButton /></el-icon>{{ $t("header.logout") }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
  <!-- infoDialog -->
  <InfoDialog ref="infoRef"></InfoDialog>
  <!-- passwordDialog -->
  <PasswordDialog ref="passwordRef"></PasswordDialog>
  <PasswordDrawer ref="passwordDrawerRef" />
</template>

<script setup lang="ts">
import { ref } from "vue";
import { LOGIN_URL } from "@/config";
import { useRouter } from "vue-router";
import { logoutApi } from "@/api/modules/login";
import { useUserStore } from "@/stores/modules/user";
import { ElMessageBox, ElMessage } from "element-plus";
import InfoDialog from "./InfoDialog.vue";
import PasswordDialog from "./PasswordDialog.vue";
import PasswordDrawer from "@/views/mine/components/PasswordDrawer.vue";
import { useI18n } from "vue-i18n";
import bus from "@/utils/bus";
// import mine from "../../../../views/mine/index.vue";
const { t } = useI18n();

const router = useRouter();
const userStore = useUserStore();

// 退出登录
const logout = () => {
  ElMessageBox.confirm(t("header.confirmLogout"), t("header.warmRemind"), {
    confirmButtonText: t("common.confirm"),
    cancelButtonText: t("common.cancel"),
    type: "warning"
  }).then(async () => {
    // 1.执行退出登录接口
    console.log("调用退出登录接口");
    await logoutApi();

    // 2.清除 Token
    console.log("清除 Token");
    userStore.setToken("");

    // 3.重定向到登陆页
    console.log("logout------");
    await router.replace(LOGIN_URL);
    ElMessage.success(t("header.logoutSuccess"));
  });
};

// 打开修改密码和个人信息弹窗
const infoRef = ref<InstanceType<typeof InfoDialog> | null>(null);
const passwordRef = ref<InstanceType<typeof PasswordDialog> | null>(null);
const passwordDrawerRef = ref<InstanceType<typeof PasswordDrawer> | null>(null);
// const openDialog = (ref: string) => {
//   if (ref == "infoRef") infoRef.value?.openDialog();
//   if (ref == "passwordRef") passwordRef.value?.openDialog();
// };
// const mineRef = ref<InstanceType<typeof mine> | null>(null);
const navigateToMine = () => {
  router.push("/mine");
  // mineRef.value?.openTour();
};

const openPasswordDrawer = () => {
  if (router.currentRoute.value.path === "/mine") {
    // 已在 mine，直接弹窗
    bus.emit("open-password-drawer");
  } else {
    // 不在 mine，跳转并带参数
    router.push({ path: "/mine", query: { openPassword: 1 } });
  }
};
</script>

<style scoped lang="scss">
.avatar {
  width: 40px;
  height: 40px;
  overflow: hidden;
  cursor: pointer;
  border-radius: 50%;
  img {
    width: 100%;
    height: 100%;
  }
}
</style>
