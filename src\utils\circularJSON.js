/**
 * 移除对象中的循环引用
 * @param {Object} obj - 要处理的对象
 * @param {Array} [exclude=[]] - 要排除的属性名数组
 * @returns {Object} - 移除循环引用后的对象副本
 */
export function removeCircularReferences(obj, exclude = []) {
  // 用于存储已经访问过的对象
  const seen = new WeakSet();

  /**
   * 递归处理对象
   * @param {Object} obj - 当前处理的对象
   * @returns {Object} - 处理后的对象
   */
  function process(obj) {
    // 如果不是对象或者是 null，直接返回
    if (obj === null || typeof obj !== "object") {
      return obj;
    }

    // 如果已经访问过这个对象，返回一个简单的引用标记
    if (seen.has(obj)) {
      return "[Circular Reference]";
    }

    // 标记这个对象已经被访问过
    seen.add(obj);

    // 处理数组
    if (Array.isArray(obj)) {
      return obj.map(item => process(item));
    }

    // 处理对象
    const result = {};
    for (const key in obj) {
      // 跳过排除的属性
      if (exclude.includes(key)) {
        continue;
      }

      // 递归处理属性值
      result[key] = process(obj[key]);
    }

    return result;
  }

  return process(obj);
}

/**
 * 安全地将对象转换为 JSON 字符串，自动处理循环引用
 * @param {Object} obj - 要转换的对象
 * @param {Array} [exclude=[]] - 要排除的属性名数组
 * @returns {string} - JSON 字符串
 */
export function safeStringify(obj, exclude = []) {
  const processed = removeCircularReferences(obj, exclude);
  return JSON.stringify(processed);
}
