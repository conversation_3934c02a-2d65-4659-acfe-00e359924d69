import { defineStore } from "pinia";
import { UserState } from "@/stores/interface";
import piniaPersistConfig from "@/stores/helper/persist";

export const useUserStore = defineStore({
  id: "geeker-user",
  state: (): UserState => ({
    token: "",
    userInfo: { name: "", userId: "" },
    lang: navigator.language
  }),
  getters: {},
  actions: {
    // Set Token，并设置本地过期时间
    setToken(token: string) {
      this.token = token;
      const TOKEN_KEY = "access_token";
      const TOKEN_EXPIRE_KEY = "token_expire";
      const TOKEN_EXPIRE_MS = 24 * 60 * 60 * 1000; // 1天
      if (token) {
        localStorage.setItem(TOKEN_KEY, token);
        localStorage.setItem(TOKEN_EXPIRE_KEY, (Date.now() + TOKEN_EXPIRE_MS).toString());
      } else {
        localStorage.removeItem(TOKEN_KEY);
        localStorage.removeItem(TOKEN_EXPIRE_KEY);
      }
    },
    // 获取 Token，自动校验有效期
    getToken(): string {
      const TOKEN_KEY = "access_token";
      const TOKEN_EXPIRE_KEY = "token_expire";
      const token = localStorage.getItem(TOKEN_KEY);
      const expire = localStorage.getItem(TOKEN_EXPIRE_KEY);
      if (!token) return "";
      if (expire && Date.now() > Number(expire)) {
        this.removeToken();
        return "";
      }
      return token;
    },
    // 清除 Token 并跳转登录页
    removeToken() {
      this.token = "";
      const TOKEN_KEY = "access_token";
      const TOKEN_EXPIRE_KEY = "token_expire";
      localStorage.removeItem(TOKEN_KEY);
      localStorage.removeItem(TOKEN_EXPIRE_KEY);
      // 跳转登录页
      // 这里不能直接引入 router，否则 SSR 会有问题，建议在业务层调用 router.replace('/login')
    },
    // 每次活跃时自动顺延本地 token 有效期
    renewTokenExpire() {
      const TOKEN_KEY = "access_token";
      const TOKEN_EXPIRE_KEY = "token_expire";
      const TOKEN_EXPIRE_MS = 24 * 60 * 60 * 1000; // 1天
      const token = localStorage.getItem(TOKEN_KEY);
      if (token) {
        localStorage.setItem(TOKEN_EXPIRE_KEY, (Date.now() + TOKEN_EXPIRE_MS).toString());
      }
    },
    // Set setUserInfo
    setUserInfo(userInfo: UserState["userInfo"]) {
      this.userInfo = userInfo;
    }
  },
  persist: piniaPersistConfig("geeker-user")
});
