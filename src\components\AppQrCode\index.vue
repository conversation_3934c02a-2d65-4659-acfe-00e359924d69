<template>
  <div class="app-qr-code">
    <el-tooltip placement="bottom" :show-after="500">
      <template #content>
        <div class="tooltip-content">
          <div class="qr-image-container">
            <img :src="qrCodeImage" alt="APP Download QR Code" class="qr-image" />
          </div>
          <div class="qr-description">
            <p>{{ $t("common.scanToDownload") }}</p>
            <p class="qr-subtitle">{{ $t("common.supportAndroidIos") }}</p>
          </div>
        </div>
      </template>
      <div class="qr-trigger">
        <el-icon class="qr-icon">
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              fill="currentColor"
              d="M64 64h256v256H64V64zm64 64v128h128V128H128zM64 704h256v256H64V704zm64 64v128h128V768H128zM704 64h256v256H704V64zm64 64v128h128V128H768zM192 192h64v64h-64v-64zM192 832h64v64h-64v-64zM832 192h64v64h-64v-64zM464 64h80v64h-80V64zM624 64h16v64h-16V64zM464 192h16v64h-16v-64zM544 192h80v64h-80v-64zM464 256h16v64h-16v-64zM544 256h16v64h-16v-64zM624 256h80v64h-80v-64zM464 320h80v64h-80v-64zM624 320h16v64h-16v-64zM464 384h16v64h-16v-64zM544 384h80v64h-80v-64zM704 384h16v64h-16v-64zM784 384h80v64h-80v-64zM464 448h80v64h-80v-64zM624 448h16v64h-16v-64zM784 448h16v64h-16v-64zM464 512h16v64h-16v-64zM544 512h80v64h-80v-64zM704 512h80v64h-80v-64zM864 512h16v64h-16v-64zM464 576h80v64h-80v-64zM624 576h16v64h-16v-64zM784 576h80v64h-80v-64zM64 464h80v80H64v-80zM464 640h16v64h-16v-64zM544 640h80v64h-80v-64zM704 640h16v64h-16v-64zM784 640h16v64h-16v-64zM864 640h80v64h-80v-64zM464 704h80v64h-80v-64zM624 704h16v64h-16v-64zM784 704h80v64h-80v-64zM464 768h16v64h-16v-64zM544 768h80v64h-80v-64zM704 768h16v64h-16v-64zM784 768h16v64h-16v-64zM864 768h80v64h-80v-64zM464 832h80v64h-80v-64zM624 832h16v64h-16v-64zM784 832h80v64h-80v-64zM464 896h16v64h-16v-64zM544 896h80v64h-80v-64zM704 896h80v64h-80v-64zM864 896h16v64h-16v-64z"
            />
          </svg>
        </el-icon>
      </div>
    </el-tooltip>
  </div>
</template>

<script setup lang="ts">
import { getQrCodeImage } from "@/utils";

// 根据环境获取对应的二维码图片
const qrCodeImage = getQrCodeImage();
</script>

<style scoped lang="scss">
.app-qr-code {
  .qr-trigger {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 34px;
    height: 34px;
    color: var(--el-text-color-primary);
    cursor: pointer;
    background: var(--el-bg-color);
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
    transition: all 0.3s ease;
    &:hover {
      color: var(--el-color-primary);
      background: var(--el-color-primary-light-9);
      border-color: var(--el-color-primary-light-7);
      transform: translateY(-1px);
    }
    .qr-icon {
      font-size: 16px;
    }
  }
}
:deep(.el-tooltip__trigger) {
  display: inline-block;
}

// Tooltip 内容样式
.tooltip-content {
  padding: 16px;
  text-align: center;
  .qr-image-container {
    margin-bottom: 12px;
    .qr-image {
      width: 120px;
      height: 120px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
    }
  }
  .qr-description {
    p {
      margin: 0 0 4px;
      font-size: 14px;
      line-height: 1.4;
      color: var(--el-text-color-primary);
    }
    .qr-subtitle {
      font-size: 12px;
      color: var(--el-text-color-secondary);
    }
  }
}

// 响应式设计
@media screen and (width <= 768px) {
  .app-qr-code {
    .qr-trigger {
      width: 32px;
      height: 32px;
      .qr-icon {
        font-size: 14px;
      }
    }
  }
  .tooltip-content {
    padding: 12px;
    .qr-image-container {
      .qr-image {
        width: 100px;
        height: 100px;
      }
    }
  }
}
</style>
