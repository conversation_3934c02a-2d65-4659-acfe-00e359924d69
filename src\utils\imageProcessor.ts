/**
 * 图像处理工具函数
 */

/**
 * 处理图像URL，如果包含gray:前缀，则将图像转换为灰色
 * @param url 图像URL
 * @returns 处理后的图像URL
 */
export const processImageUrl = (url: string): Promise<string> => {
  return new Promise(resolve => {
    // 检查URL是否包含gray:前缀
    if (url.startsWith("image://gray:")) {
      // 提取实际图像URL
      const imageUrl = url.replace("image://gray:", "");

      // 创建图像对象
      const img = new Image();
      img.crossOrigin = "anonymous"; // 处理跨域问题
      img.src = imageUrl;

      img.onload = () => {
        // 创建画布
        const canvas = document.createElement("canvas");
        canvas.width = img.width;
        canvas.height = img.height;

        const ctx = canvas.getContext("2d");
        if (ctx) {
          // 绘制原始图像
          ctx.drawImage(img, 0, 0);

          // 获取图像数据
          const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
          const data = imageData.data;

          // 应用灰度滤镜和透明度
          for (let i = 0; i < data.length; i += 4) {
            const avg = (data[i] + data[i + 1] + data[i + 2]) / 3;
            data[i] = avg; // 红
            data[i + 1] = avg; // 绿
            data[i + 2] = avg; // 蓝
            data[i + 3] = data[i + 3] * 0.6; // 降低不透明度
          }

          // 将处理后的图像数据放回画布
          ctx.putImageData(imageData, 0, 0);

          // 返回处理后的图像URL
          resolve("image://" + canvas.toDataURL());
        } else {
          // 如果无法获取上下文，返回原始URL
          resolve(url);
        }
      };

      img.onerror = () => {
        // 如果图像加载失败，返回原始URL
        resolve(url);
      };
    } else {
      // 如果不需要处理，直接返回原始URL
      resolve(url);
    }
  });
};

/**
 * 处理ECharts节点的图标URL和标签
 * @param node ECharts节点数据
 * @returns 处理后的节点数据
 */
export const processNodeSymbol = async (node: any): Promise<any> => {
  if (!node) return node;

  // 处理当前节点的symbol
  if (node.symbol && typeof node.symbol === "string" && node.symbol.includes("gray:")) {
    node.symbol = await processImageUrl(node.symbol);
  }

  // 添加节点标签属性，确保显示节点名称
  if (node.name) {
    // 如果节点没有 label 属性，添加一个
    if (!node.label) {
      node.label = {
        show: true,
        position: "bottom",
        formatter: node.name
      };
    }
  }

  // 递归处理子节点
  if (node.children && Array.isArray(node.children)) {
    for (let i = 0; i < node.children.length; i++) {
      node.children[i] = await processNodeSymbol(node.children[i]);
    }
  }

  return node;
};
