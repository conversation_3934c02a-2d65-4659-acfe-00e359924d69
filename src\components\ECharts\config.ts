import * as echarts from "echarts/core";
import {
  BarSeriesOption,
  LineSeriesOption,
  PieSeriesOption,
  ScatterSeriesOption,
  RadarSeriesOption,
  GaugeSeriesOption,
  LinesSeriesOption
} from "echarts/charts";

// 自定义图表类型
interface GraphSeriesOption {
  type: "graph";
  layout?: "none" | "circular" | "force";
  data?: any[];
  links?: any[];
  categories?: any[];
  roam?: boolean;
  focusNodeAdjacency?: boolean;
  symbolSize?: number | Function;
  label?: any;
  lineStyle?: any;
  emphasis?: any;
  force?: {
    repulsion?: number;
    edgeLength?: number;
    gravity?: number;
    [key: string]: any;
  };
  [key: string]: any;
}

interface TreeSeriesOption {
  type: "tree";
  data?: any[];
  layout?: "orthogonal" | "radial";
  orient?: "LR" | "RL" | "TB" | "BT";
  symbolSize?: number | Function;
  label?: any;
  lineStyle?: any;
  emphasis?: any;
  expandAndCollapse?: boolean;
  initialTreeDepth?: number;
  [key: string]: any;
}

// 导出 ECOption 类型
export type ECOption = echarts.ComposeOption<
  | BarSeriesOption
  | LineSeriesOption
  | PieSeriesOption
  | ScatterSeriesOption
  | RadarSeriesOption
  | GaugeSeriesOption
  | LinesSeriesOption
> & {
  series?: (GraphSeriesOption | TreeSeriesOption | any)[];
  tooltip?: any;
  xAxis?: any;
  yAxis?: any;
  legend?: any;
};

// 默认导出 echarts
export default echarts;
