<template>
  <div class="echarts-instance" ref="chartRef" :style="echartsStyle" />
</template>

<script setup lang="ts" name="ECharts">
import { ref, onMounted, onBeforeUnmount, watch, computed, markRaw, nextTick, onActivated } from "vue";
import { EChartsType, ECElementEvent } from "echarts/core";
import echarts, { ECOption } from "./config/index";
import { useDebounceFn } from "@vueuse/core";
import { useGlobalStore } from "@/stores/modules/global";
import { storeToRefs } from "pinia";
import { deepMerge } from "@/utils";

interface Props {
  option: ECOption;
  renderer?: "canvas" | "svg";
  resize?: boolean;
  theme?: Object | string;
  width?: number | string;
  height?: number | string;
  onClick?: (event: ECElementEvent) => any;
}

const props = withDefaults(defineProps<Props>(), {
  renderer: "canvas",
  resize: true
});

const echartsStyle = computed(() => {
  return props.width || props.height
    ? { height: props.height + "px", width: props.width + "px" }
    : { height: "100%", width: "100%" };
});

const chartRef = ref<HTMLDivElement | HTMLCanvasElement>();
const chartInstance = ref<EChartsType>();

const globalStore = useGlobalStore();
const { maximize, isCollapse, tabs, footer, isDark } = storeToRefs(globalStore);

// 根据暗黑模式调整图表配置
const getThemeOption = () => {
  // 默认的暗黑模式下的文字颜色
  const darkTextColor = "#ffffff"; // 使用纯白色以确保最大对比度
  const darkLegendBgColor = "rgba(50, 50, 50, 0.7)"; // 深色背景以增强对比度
  const lightTextColor = "#333333";

  // 根据当前主题设置文字颜色
  const textColor = isDark.value ? darkTextColor : lightTextColor;

  // 暗黑模式下的图表配置
  const darkThemeOption: Partial<ECOption> = {
    // 全局文字样式
    textStyle: {
      color: textColor,
      fontWeight: isDark.value ? "bold" : "normal" // 在暗黑模式下使用粗体增强可读性
    },
    // 图例样式
    legend: {
      textStyle: {
        color: textColor,
        fontWeight: isDark.value ? "bold" : "normal",
        fontSize: isDark.value ? 14 : 12 // 增大字体大小
      },
      // 在暗黑模式下为图例添加背景色以增强对比度
      backgroundColor: isDark.value ? darkLegendBgColor : "transparent",
      borderRadius: 4,
      padding: isDark.value ? 8 : 5,
      itemGap: 15
    },
    // X轴样式
    xAxis: {
      axisLabel: {
        color: textColor,
        fontWeight: isDark.value ? "bold" : "normal"
      }
    },
    // Y轴样式
    yAxis: {
      axisLabel: {
        color: textColor,
        fontWeight: isDark.value ? "bold" : "normal"
      }
    },
    // 标题样式
    title: {
      textStyle: {
        color: textColor,
        fontWeight: "bold"
      },
      subtextStyle: {
        color: isDark.value ? "#dddddd" : "#888888"
      }
    },
    // 提示框样式
    tooltip: {
      backgroundColor: isDark.value ? "rgba(50, 50, 50, 0.9)" : "rgba(255, 255, 255, 0.9)",
      borderColor: isDark.value ? "rgba(255, 255, 255, 0.2)" : "rgba(0, 0, 0, 0.1)",
      textStyle: {
        color: isDark.value ? "#ffffff" : "#333333"
      }
    }
  };

  return darkThemeOption;
};

const draw = () => {
  if (chartInstance.value) {
    try {
      // 合并原始配置和主题配置
      const themeOption = getThemeOption();
      const mergedOption = deepMerge(props.option, themeOption);

      // 保留特殊图表的配置，如异地组网图
      if (props.option.series && Array.isArray(props.option.series) && props.option.series.length > 0) {
        // 如果是特殊的图表类型，保留原始配置
        if (props.option.series[0].type && props.option.series[0].type.toString() === "graph") {
          // 对于网络图，保留原始的节点和连线数据，但确保背景色正确
          // 先更新背景色
          if (isDark.value) {
            // eslint-disable-next-line vue/no-mutating-props
            props.option.backgroundColor = "rgba(30, 32, 40, 0.8)";
          } else {
            // eslint-disable-next-line vue/no-mutating-props
            props.option.backgroundColor = "transparent";
          }
          chartInstance.value.setOption(props.option, { notMerge: false });
          return;
        }
      }

      // 对于普通图表，应用合并后的配置
      chartInstance.value.setOption(mergedOption, { notMerge: true });
    } catch (error) {
      console.error("Error setting chart options:", error);
      // 出错时回退到原始配置
      chartInstance.value.setOption(props.option, { notMerge: false });
    }
  }
};

watch(props, () => {
  draw();
});

// 监听暗黑模式变化，采用简化的方式重新绘制图表
watch(isDark, async newVal => {
  try {
    console.log("ECharts component theme changed to:", newVal ? "dark" : "light");

    // 如果图表实例存在，先销毁
    if (chartInstance.value) {
      try {
        // 保存当前配置
        const currentOption = chartInstance.value.getOption();
        const seriesData = currentOption.series;

        // 销毁当前实例
        chartInstance.value.dispose();
        chartInstance.value = null;

        // 等待DOM更新
        await nextTick();

        // 创建新实例，使用当前主题
        if (chartRef.value) {
          chartInstance.value = markRaw(
            echarts.init(chartRef.value, newVal ? "dark" : undefined, {
              renderer: props.renderer || "canvas"
            })
          );

          // 重新注册点击事件
          chartInstance.value.on("click", handleClick);

          // 准备新的配置
          const newOption = { ...props.option };

          // 明确设置背景色
          if (newVal) {
            newOption.backgroundColor = "rgba(30, 32, 40, 0.8)";
          } else {
            newOption.backgroundColor = "transparent";
          }

          // 如果有保存的数据，使用保存的数据
          if (seriesData && Array.isArray(seriesData)) {
            newOption.series = seriesData;
          }

          // 应用配置
          chartInstance.value.setOption(newOption, { notMerge: true });

          // 强制重新渲染
          chartInstance.value.resize();

          console.log("ECharts theme switch completed successfully");
        }
      } catch (error) {
        console.error("Error during ECharts theme switch:", error);

        // 出错时尝试使用原始配置重新初始化
        if (chartRef.value) {
          try {
            chartInstance.value = markRaw(
              echarts.init(chartRef.value, newVal ? "dark" : undefined, {
                renderer: props.renderer || "canvas"
              })
            );
            chartInstance.value.on("click", handleClick);

            // 创建新的配置对象，确保背景色正确
            const fallbackOption = { ...props.option };
            if (newVal) {
              fallbackOption.backgroundColor = "rgba(30, 32, 40, 0.8)";
            } else {
              fallbackOption.backgroundColor = "transparent";
            }

            chartInstance.value.setOption(fallbackOption, { notMerge: true });
          } catch (fallbackError) {
            console.error("Failed to recover from theme switch error:", fallbackError);
          }
        }
      }
    }
  } catch (outerError) {
    console.error("Unexpected error during theme switch:", outerError);
  }
});

const handleClick = (event: ECElementEvent) => props.onClick && props.onClick(event);

const init = () => {
  try {
    console.log("Initializing chart...");
    if (!chartRef.value) {
      console.error("chartRef is null or undefined");
      return;
    }

    // 先检查是否已经有实例
    try {
      chartInstance.value = echarts.getInstanceByDom(chartRef.value);
      if (chartInstance.value) {
        console.log("Found existing chart instance");
        return;
      }
    } catch (error) {
      console.warn("Error checking for existing instance:", error);
    }

    console.log("Creating new chart instance");
    // 如果没有指定主题，则根据当前模式自动选择
    const theme = props.theme || (isDark.value ? "dark" : undefined);

    try {
      // 创建新实例
      chartInstance.value = markRaw(
        echarts.init(chartRef.value, theme, {
          renderer: props.renderer || "canvas" // 确保始终有渲染器值
        })
      );
      console.log("Chart instance created successfully");

      // 注册点击事件
      chartInstance.value.on("click", handleClick);

      // 绘制图表
      draw();
      console.log("Chart drawn successfully");
    } catch (error) {
      console.error("Error creating chart instance:", error);
    }
  } catch (error) {
    console.error("Unexpected error in init():", error);
  }
};

const resize = () => {
  if (chartInstance.value && props.resize) {
    chartInstance.value.resize({ animation: { duration: 300 } });
  }
};

const debouncedResize = useDebounceFn(resize, 300, { maxWait: 800 });

watch(
  () => [maximize, isCollapse, tabs, footer],
  () => {
    debouncedResize();
  },
  { deep: true }
);

onMounted(async () => {
  console.log("ECharts component mounted");

  // 等待DOM完全渲染
  await nextTick();

  // 等待一些时间再初始化图表，确保 DOM 已经完全渲染
  setTimeout(() => {
    console.log("Initializing ECharts instance");
    init();
    window.addEventListener("resize", debouncedResize);
  }, 200);
});

onActivated(() => {
  if (chartInstance.value) {
    chartInstance.value.resize();
  }
});

onBeforeUnmount(() => {
  chartInstance.value?.dispose();
  window.removeEventListener("resize", debouncedResize);
});

defineExpose({
  getInstance: () => chartInstance.value,
  resize,
  draw
});
</script>
