/* flex */
.flx-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flx-justify-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flx-align-center {
  display: flex;
  align-items: center;
}

/* clearfix */
.clearfix::after {
  display: block;
  height: 0;
  overflow: hidden;
  clear: both;
  content: "";
}

/* 文字单行省略号 */
.sle {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 文字多行省略号 */
.mle {
  display: -webkit-box;
  overflow: hidden;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

/* 文字多了自动換行 */
.break-word {
  word-break: break-all;
  word-wrap: break-word;
}

/* fade-transform */
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all 0.2s;
}
.fade-transform-enter-from {
  opacity: 0;
  transition: all 0.2s;
  transform: translateX(-30px);
}
.fade-transform-leave-to {
  opacity: 0;
  transition: all 0.2s;
  transform: translateX(30px);
}

/* 引入毛玻璃效果样式 */
@import "./glassmorphism";

/* breadcrumb-transform */
.breadcrumb-enter-active {
  transition: all 0.2s;
}
.breadcrumb-enter-from,
.breadcrumb-leave-active {
  opacity: 0;
  transform: translateX(10px);
}

/* scroll bar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
::-webkit-scrollbar-thumb {
  background-color: var(--el-border-color-darker);
  border-radius: 20px;
}

/* nprogress */
#nprogress .bar {
  background: var(--el-color-primary) !important;
}
#nprogress .spinner-icon {
  border-top-color: var(--el-color-primary) !important;
  border-left-color: var(--el-color-primary) !important;
}
#nprogress .peg {
  box-shadow:
    0 0 10px var(--el-color-primary),
    0 0 5px var(--el-color-primary) !important;
}

/* 外边距、内边距全局样式 */
@for $i from 0 through 100 {
  .mt#{$i} {
    margin-top: #{$i}px !important;
  }
  .mr#{$i} {
    margin-right: #{$i}px !important;
  }
  .mb#{$i} {
    margin-bottom: #{$i}px !important;
  }
  .ml#{$i} {
    margin-left: #{$i}px !important;
  }
  .pt#{$i} {
    padding-top: #{$i}px !important;
  }
  .pr#{$i} {
    padding-right: #{$i}px !important;
  }
  .pb#{$i} {
    padding-bottom: #{$i}px !important;
  }
  .pl#{$i} {
    padding-left: #{$i}px !important;
  }
}
.table-header {
  .cell {
    display: inline-block;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
body.dark .card.table-search,
html.dark .card.table-search,
.dark .card.table-search {
  color: #f5f6fa !important;
  background: #23272e !important;
  border: 1px solid #33363d !important;
  border-radius: 12px !important;
  box-shadow: none !important;
}
body.dark .card.table-search *,
html.dark .card.table-search *,
.dark .card.table-search * {
  color: #f5f6fa !important;
  background: transparent !important;
  border-color: #33363d !important;
}
body.dark .card.table-search input,
html.dark .card.table-search input,
.dark .card.table-search input {
  color: #f5f6fa !important;
  background: #23272e !important;
  border-color: #33363d !important;
}
body.dark .card.table-search input::placeholder {
  color: #888888 !important;
  opacity: 1;
}
body.dark .card.table-search .el-button,
html.dark .card.table-search .el-button,
.dark .card.table-search .el-button {
  color: #f5f6fa !important;
  background: #23272e !important;
  border-color: #33363d !important;
}
body.dark .el-select-dropdown,
html.dark .el-select-dropdown,
.dark .el-select-dropdown {
  color: #f5f6fa !important;
  background: #23272e !important;
  border: 1px solid #33363d !important;
}
body.dark .el-select-dropdown__item,
html.dark .el-select-dropdown__item,
.dark .el-select-dropdown__item {
  color: #f5f6fa !important;
}
body.dark .el-select-dropdown__item.is-selected,
html.dark .el-select-dropdown__item.is-selected,
.dark .el-select-dropdown__item.is-selected {
  color: #ffffff !important;
  background: #2d323b !important;
}
body.dark .el-select-dropdown__item.is-disabled,
html.dark .el-select-dropdown__item.is-disabled,
.dark .el-select-dropdown__item.is-disabled {
  color: #666666 !important;
  background: transparent !important;
}
