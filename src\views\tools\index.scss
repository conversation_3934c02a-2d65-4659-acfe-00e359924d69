/* 亮色模式样式 */
.tools-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden; /* 禁止滚动条 */
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

  /* 添加装饰元素 */
  &::before {
    position: absolute;
    top: -50px;
    right: -50px;
    z-index: 0;
    width: 300px;
    height: 300px;
    content: "";
    background: radial-gradient(circle, rgb(255 255 255 / 80%) 0%, rgb(255 255 255 / 0%) 70%);
    border-radius: 50%;
    opacity: 0.4;
  }
  &::after {
    position: absolute;
    bottom: -100px;
    left: -100px;
    z-index: 0;
    width: 400px;
    height: 400px;
    content: "";
    background: radial-gradient(circle, rgb(173 216 230 / 80%) 0%, rgb(173 216 230 / 0%) 70%);
    border-radius: 50%;
    opacity: 0.3;
  }
}
.table-box {
  position: relative;
  z-index: 1; /* 确保在装饰元素之上 */
  box-sizing: border-box;
  display: grid;
  grid-template-columns: repeat(4, minmax(220px, 220px));
  gap: 20px;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 20px;
  margin: 0;
  .avatar-div {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    aspect-ratio: 1;
    padding: 10px;
    cursor: pointer;
    background: rgb(255 255 255 / 10%);
    backdrop-filter: blur(10px);
    border: 1px solid rgb(255 255 255 / 18%);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgb(0 0 0 / 10%);
    transition: all 0.3s ease;
    &:hover {
      background: rgb(255 255 255 / 20%);
      box-shadow: 0 12px 32px rgb(0 0 0 / 15%);
      transform: translateY(-5px);
      .el-avatar {
        border-color: var(--el-color-primary);
        transform: scale(1.05);
      }
    }
    .el-avatar {
      padding: 12px;
      margin-bottom: 12px;
      background: rgb(255 255 255 / 20%);
      border: 2px solid rgb(255 255 255 / 50%);
      border-radius: 16px !important;
      box-shadow: 0 4px 16px rgb(0 0 0 / 10%);
      transition: all 0.3s ease;
    }
    .el-text {
      margin-top: 8px;
      font-size: 15px;
      font-weight: 600;
      color: rgb(0 0 0 / 80%);
      text-shadow: 0 1px 2px rgb(255 255 255 / 20%);
    }
  }
}

// 响应式图标尺寸
.avatar-desktop {
  width: 100px !important;
  height: 100px !important;
}
.avatar-tablet {
  width: 80px !important;
  height: 80px !important;
}
.avatar-mobile {
  width: 60px !important;
  height: 60px !important;
}

// 对话框样式
.tools-dialog {
  :deep(.el-dialog) {
    z-index: 2001 !important;
    overflow: hidden;
    border-radius: 16px;
    .el-dialog__header {
      padding: 20px 24px;
      margin: 0;
      background: var(--el-bg-color-overlay);
      border-bottom: 1px solid var(--el-border-color-lighter);
      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
      }
    }
    .el-dialog__body {
      max-height: 70vh;
      padding: 24px;
      overflow-y: auto;
    }
    .el-dialog__footer {
      padding: 16px 24px;
      background: var(--el-bg-color-overlay);
      border-top: 1px solid var(--el-border-color-lighter);
    }
  }
}
.ip-input {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.ip-part {
  margin-right: 2px; /* 调整输入框间距 */
}
.split {
  margin: 0 2px;
  font-size: 16px;
  color: #666666;
}
.el-input {
  width: 60px;
}
.el-button {
  margin-left: 10px;
}
.ip-search-container {
  padding: 10px;
  margin: 20px;
}

/* 黑暗模式样式 */
html.dark {
  .tools-container {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d3748 100%);

    /* 装饰元素 - 黑暗模式 */
    &::before {
      background: radial-gradient(circle, rgb(100 100 100 / 50%) 0%, rgb(100 100 100 / 0%) 70%);
    }
    &::after {
      background: radial-gradient(circle, rgb(70 130 180 / 30%) 0%, rgb(70 130 180 / 0%) 70%);
    }
  }
  .table-box {
    .avatar-div {
      /* 毕玻璃效果 - 黑暗模式 */
      background: rgb(30 30 30 / 30%);
      border: 1px solid rgb(255 255 255 / 10%);
      box-shadow: 0 8px 32px rgb(0 0 0 / 20%);
      &:hover {
        background: rgb(40 40 40 / 40%);
        box-shadow: 0 12px 32px rgb(0 0 0 / 30%);
      }
      .el-avatar {
        background: rgb(40 40 40 / 50%);
        border: 2px solid rgb(255 255 255 / 20%);
        box-shadow: 0 4px 16px rgb(0 0 0 / 20%);
      }
      .el-text {
        color: var(--el-text-color-primary);
        text-shadow: 0 1px 2px rgb(0 0 0 / 30%);
      }
    }
  }

  /* 对话框样式 - 黑暗模式 */
  .tools-dialog {
    :deep(.el-dialog) {
      background: rgb(30 30 30 / 70%);
      border: 1px solid rgb(255 255 255 / 10%);
      box-shadow: 0 8px 32px rgb(0 0 0 / 30%);
      .el-dialog__header {
        background: rgb(40 40 40 / 50%);
        border-bottom: 1px solid rgb(255 255 255 / 10%);
      }
      .el-dialog__body {
        background: rgb(30 30 30 / 30%);
      }
      .el-dialog__footer {
        background: rgb(40 40 40 / 50%);
        border-top: 1px solid rgb(255 255 255 / 10%);
      }
    }
  }

  /* 其他元素样式 - 黑暗模式 */
  .split {
    color: var(--el-text-color-secondary);
  }
}

// 移动端适配
@media screen and (width <= 768px) {
  .tools-container {
    background: var(--el-bg-color-page);
    &::before,
    &::after {
      display: none;
    }
  }
  .table-box {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    padding: 16px;
    .avatar-div {
      padding: 16px;
      background: var(--el-bg-color);
      border: 1px solid var(--el-border-color-light);
      border-radius: 12px;
      box-shadow: 0 2px 8px rgb(0 0 0 / 6%);
      &:hover {
        background: var(--el-bg-color);
        box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
        transform: translateY(-2px);
      }
      .el-avatar {
        padding: 8px;
        margin-bottom: 8px;
        background: var(--el-fill-color-light);
        border: 1px solid var(--el-border-color-lighter);
      }
      .el-text {
        margin-top: 4px;
        font-size: 14px;
        color: var(--el-text-color-primary);
        text-shadow: none;
      }
    }
  }
  .tools-dialog {
    :deep(.el-dialog) {
      margin: 5vh auto !important;
      border-radius: 12px;
      .el-dialog__header {
        padding: 16px 20px;
        .el-dialog__title {
          font-size: 16px;
        }
      }
      .el-dialog__body {
        max-height: 65vh;
        padding: 16px 20px;
      }
      .el-dialog__footer {
        padding: 12px 20px;
      }
    }
  }
}

@media screen and (width <= 480px) {
  .table-box {
    grid-template-columns: 1fr;
    gap: 12px;
    padding: 12px;
    .avatar-div {
      flex-direction: row;
      justify-content: flex-start;
      min-height: 80px;
      aspect-ratio: unset;
      padding: 16px;
      .el-avatar {
        flex-shrink: 0;
        margin-right: 16px;
        margin-bottom: 0;
      }
      .el-text {
        align-self: center;
        margin-top: 0;
        font-size: 15px;
        text-align: left;
      }
    }
  }
  .tools-dialog {
    :deep(.el-dialog) {
      margin: 2vh auto !important;
      border-radius: 8px;
      .el-dialog__header {
        padding: 12px 16px;
        .el-dialog__title {
          font-size: 15px;
        }
      }
      .el-dialog__body {
        max-height: 75vh;
        padding: 12px 16px;
      }
      .el-dialog__footer {
        padding: 8px 16px;
      }
    }
  }
}

@media screen and (width <= 350px) {
  .table-box {
    gap: 8px;
    padding: 8px;
    .avatar-div {
      min-height: 70px;
      padding: 12px;
      .el-avatar {
        margin-right: 12px;
      }
      .el-text {
        font-size: 14px;
      }
    }
  }
}

// 工具组件内部移动端优化
@media screen and (width <= 480px) {
  .ip-search-container,
  .mac-search-container,
  .ping-container,
  .port-scan,
  .bridge-height-container {
    gap: 12px;
    padding: 8px;
  }
  .search-card,
  .result-card,
  .scan-card {
    padding: 12px;
    border-radius: 8px;
    .card-header {
      margin-bottom: 8px;
      h3 {
        font-size: 15px;
      }
      .description {
        font-size: 12px;
      }
    }
  }
  .ip-input,
  .mac-input {
    flex-wrap: wrap;
    justify-content: center;
    padding: 6px 8px;
    .ip-part,
    .mac-part {
      margin: 2px;
    }
    .ip-part-input,
    .mac-part-input {
      width: 40px;
      :deep(.el-input__inner) {
        padding: 0 2px;
        font-size: 12px;
      }
    }
    .split {
      margin: 0;
      font-size: 12px;
    }
  }
  .button-container,
  .button-group {
    gap: 6px;
    margin-top: 8px;
    .action-button {
      padding: 6px 10px;
      font-size: 12px;
    }
  }
  .result-content {
    padding: 8px;
    .result-grid {
      grid-template-columns: 1fr;
      gap: 6px;
    }
    .result-item {
      padding: 8px;
      .result-label {
        margin-bottom: 2px;
        font-size: 11px;
      }
      .result-value {
        font-size: 12px;
      }
    }
  }
  .port-scan {
    .form-row {
      flex-direction: column;
      gap: 8px;
    }
    .input-group,
    .port-range {
      width: 100%;
      min-width: unset;
    }
    .port-inputs {
      gap: 4px;
      .port-input {
        width: 80px;
      }
    }
    .port-chips {
      gap: 4px;
      .port-chip {
        padding: 2px 6px;
        font-size: 11px;
      }
    }
    .scanning-animation {
      width: 100px;
      height: 100px;
    }
  }
  .ping-container {
    .input-area {
      gap: 6px;
      .input-group {
        margin-bottom: 6px;
        .ping-input {
          font-size: 12px;
        }
      }
    }
    .el-table {
      font-size: 11px;
      :deep(.el-table__cell) {
        padding: 4px 2px;
      }
    }
    .stats {
      margin-top: 8px;
      font-size: 12px;
      p {
        margin: 2px 0;
      }
    }
  }
}
