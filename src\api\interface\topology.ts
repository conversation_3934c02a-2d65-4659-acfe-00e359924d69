import type { Project } from "@/api/interface/project";

/**
 * 网桥客户端数据接口
 */
export interface BridgeClientData extends Partial<Project.ResDeviceList> {
  extra?: {
    type?: number;
    model?: string;
    version?: string;
    macaddr?: string;
    txByte?: number;
    rxByte?: number;
    txRate?: number;
    rxRate?: number;
    latency?: number;
    time?: number;
    rssi?: number;
    online?: number;
  };
  name?: string;
  brClient?: any[];
  system?: any; // 系统相关属性
}

/**
 * 抽屉组件属性接口
 */
export interface DrawerProps {
  title: string;
  isView: boolean;
  row: BridgeClientData;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

/**
 * 拓扑图节点数据接口
 */
export interface TopologyNode {
  name: string;
  symbol: string;
  children?: TopologyNode[];
  extra?: any;
}

/**
 * 拓扑图数据接口
 */
export interface TopologyData {
  name: string;
  symbol: string;
  children: TopologyNode[];
}

/**
 * 设备名称修改请求参数
 */
export interface SaveDeviceNameParams {
  cmd: number;
  deviceId: string;
  userId: string;
  data: {
    network: {
      brClient: [
        {
          name: string;
          macaddr: string;
        }
      ];
    };
  };
}

/**
 * 删除网桥客户端请求参数
 */
export interface DeleteBridgeClientParams {
  cmd: number;
  deviceId: string;
  userId: string;
  data: {
    network: {
      brClient: [
        {
          macaddr: string;
          delete: number;
        }
      ];
    };
  };
}

/**
 * 获取拓扑数据请求参数
 */
export interface GetTopologyParams {
  cmd: number;
  deviceId: string;
  userId: string;
  data: {
    system: string[];
  };
}

/**
 * 获取设备列表请求参数
 */
export interface Params {
  groupId: string;
}

export interface topologyItem {
  name: string;
  macaddr: string;
  deviceType: string;
  model: string;
  conn: string;
  ipaddr: string;
  sport: string;
  dport: string;
  offline: number;
  sn: string;
  medium: string;
}
