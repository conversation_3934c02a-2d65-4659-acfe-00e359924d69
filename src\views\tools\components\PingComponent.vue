<template>
  <div class="ping-container">
    <div class="input-area">
      <div class="input-group">
        <el-input
          v-model="pingOptions.host"
          :placeholder="t('common.pingHost')"
          class="ping-input host-input"
          @input="validateHostInput"
          :status="hostInputStatus"
          clearable
        >
          <template #prefix>
            <el-icon><Connection /></el-icon>
          </template>
        </el-input>
        <el-tooltip v-if="hostInputStatus === 'error'" effect="dark" :content="hostErrorMessage" placement="top">
          <el-icon class="error-icon"><WarningFilled /></el-icon>
        </el-tooltip>
      </div>

      <div class="input-group">
        <el-input
          v-model="pingOptions.count"
          :placeholder="t('common.pingCount')"
          class="ping-input count-input"
          clearable
          @input="validateCountInput"
          :status="countInputStatus"
        >
          <template #prefix>
            <el-icon><Histogram /></el-icon>
          </template>
        </el-input>
      </div>

      <div class="input-group">
        <el-input
          v-model="pingOptions.interval"
          :placeholder="t('common.pingInterval')"
          class="ping-input interval-input"
          clearable
          @input="validateIntervalInput"
          :status="intervalInputStatus"
        >
          <template #prefix>
            <el-icon><Timer /></el-icon>
          </template>
        </el-input>
      </div>

      <div class="button-group">
        <el-button type="primary" @click="startPing" :icon="VideoPause" class="action-button">
          {{ t("common.startPing") }}
        </el-button>
        <el-button type="danger" @click="resetPing" :icon="Delete" class="action-button">
          {{ t("common.reset") }}
        </el-button>
      </div>
    </div>

    <el-table :data="pingResults" style="width: 100%; margin-top: 20px" height="250">
      <el-table-column prop="seq" :label="t('common.sequenceNumber')" width="100" />
      <el-table-column prop="time" :label="t('common.pingTime')" width="180" />
      <el-table-column prop="result" :label="t('common.pingResult')" width="180" />
      <el-table-column prop="delay" :label="t('common.pingDelay')" />
      <el-table-column prop="ttl" :label="t('common.pingTTL')" width="80" />
    </el-table>

    <div class="stats" style="margin-top: 20px">
      <p>{{ t("common.packetsSent") }}: {{ stats.packetsSent }}</p>
      <p>{{ t("common.packetsReceived") }}: {{ stats.packetsReceived }}</p>
      <p>{{ t("common.packetLoss") }}: {{ stats.packetLoss }}%</p>
      <p>{{ t("common.averageDelay") }}: {{ stats.averageDelay }}ms</p>
    </div>
  </div>
</template>

<script setup lang="ts" name="PingTestComponent">
import { ref, reactive, onMounted } from "vue";
import { pingTest } from "@/api/modules/tools";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
import { WarningFilled, VideoPause, Connection, Histogram, Timer, Delete } from "@element-plus/icons-vue";
const { t } = useI18n();

interface PingOptions {
  host: string;
  count: string | number;
  interval: string | number;
}

interface PingResult {
  seq: number;
  time: string;
  result: string;
  delay: number | null;
  ttl: number | null;
}

// Define interface for ping response
interface PingResponse {
  code: string;
  msg: string;
  seq: number;
  delay: number;
  time: string;
  ttl: number;
  result: string;
}

const pingOptions = reactive<PingOptions>({
  host: "",
  count: "4",
  interval: "1000"
});

const hostInputStatus = ref("");
const hostErrorMessage = ref("");
const countInputStatus = ref("");
const intervalInputStatus = ref("");

// 验证IP地址格式
const isValidIP = (ip: string): boolean => {
  const ipRegex =
    /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  return ipRegex.test(ip);
};

// 验证域名格式
const isValidDomain = (domain: string): boolean => {
  // 简单的域名验证，允许字母、数字、连字符和点，至少有一个点，不以点开头或结尾
  const domainRegex = /^([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$/;
  return domainRegex.test(domain);
};

// 验证主机输入
const validateHostInput = () => {
  if (!pingOptions.host) {
    hostInputStatus.value = "";
    hostErrorMessage.value = "";
    return;
  }

  if (isValidIP(pingOptions.host) || isValidDomain(pingOptions.host)) {
    hostInputStatus.value = "";
    hostErrorMessage.value = "";
  } else {
    hostInputStatus.value = "error";
    hostErrorMessage.value = t("common.invalidHostFormat");
  }
};

// 验证Ping次数输入
const validateCountInput = () => {
  // 确保输入是数字
  const countValue = Number(pingOptions.count);

  // 如果输入为空，清除错误状态
  if (pingOptions.count === "") {
    countInputStatus.value = "";
    return;
  }

  // 检查是否是正整数
  if (isNaN(countValue) || countValue <= 0 || !Number.isInteger(countValue)) {
    countInputStatus.value = "error";
    // 只保留数字部分
    pingOptions.count = pingOptions.count.toString().replace(/[^0-9]/g, "");
  } else {
    countInputStatus.value = "";
    // 确保存储的是数字类型
    pingOptions.count = countValue;
  }
};

// 验证Ping间隔输入
const validateIntervalInput = () => {
  // 确保输入是数字
  const intervalValue = Number(pingOptions.interval);

  // 如果输入为空，清除错误状态
  if (pingOptions.interval === "") {
    intervalInputStatus.value = "";
    return;
  }

  // 检查是否是正整数且大于100
  if (isNaN(intervalValue) || intervalValue < 100 || !Number.isInteger(intervalValue)) {
    intervalInputStatus.value = "error";
    // 只保留数字部分
    pingOptions.interval = pingOptions.interval.toString().replace(/[^0-9]/g, "");
  } else {
    intervalInputStatus.value = "";
    // 确保存储的是数字类型
    pingOptions.interval = intervalValue;
  }
};

const pingResults = ref<PingResult[]>([]);
const stats = reactive({
  packetsSent: 0,
  packetsReceived: 0,
  packetLoss: 0,
  averageDelay: 0
});

const chartData = reactive({
  columns: ["time", "delay", "loss"],
  rows: []
});

const simulatePing = async (host: string): Promise<PingResult | null> => {
  try {
    const rawResponse = await pingTest(host, 1);
    const response = rawResponse as unknown as PingResponse;

    if (typeof response !== "object" || response === null) {
      console.error(t("common.invalidDataFormat"));
      return null;
    }

    const delay = response.delay;
    const time = response.time;
    const ttl = response.ttl;

    if (typeof delay !== "number" || typeof ttl !== "number") {
      console.error(t("common.invalidDelayFormat"));
      return null;
    }

    return {
      seq: response.seq,
      time: time,
      result: response.result === "success" ? t("common.success") : t("common.failed"),
      delay: delay,
      ttl: ttl
    };
  } catch (error) {
    console.error(t("common.pingTestFailed"), error);
    return null;
  }
};

// 重置函数
const resetPing = () => {
  pingOptions.host = "";
  pingOptions.count = "4";
  pingOptions.interval = "1000";
  pingResults.value = [];
  stats.packetsSent = 0;
  stats.packetsReceived = 0;
  stats.packetLoss = 0;
  stats.averageDelay = 0;
  chartData.rows = [];
  hostInputStatus.value = "";
  hostErrorMessage.value = "";
  countInputStatus.value = "";
  intervalInputStatus.value = "";
};

const startPing = async () => {
  pingResults.value = [];
  stats.packetsSent = 0;
  stats.packetsReceived = 0;
  stats.packetLoss = 0;
  stats.averageDelay = 0;
  chartData.rows = [];

  if (!pingOptions.host) {
    ElMessage.warning(t("common.pingHost"));
    return;
  }

  // 验证主机格式
  if (!isValidIP(pingOptions.host) && !isValidDomain(pingOptions.host)) {
    ElMessage.error(t("common.invalidHostFormat"));
    return;
  }

  // 转换为数字并验证
  const countValue = Number(pingOptions.count);
  if (isNaN(countValue) || countValue < 1 || !Number.isInteger(countValue)) {
    ElMessage.warning(t("common.pingCountError"));
    return;
  }

  const intervalValue = Number(pingOptions.interval);
  if (isNaN(intervalValue) || intervalValue < 100 || !Number.isInteger(intervalValue)) {
    ElMessage.warning(t("common.pingIntervalError"));
    return;
  }

  for (let i = 0; i < Number(pingOptions.count); i++) {
    const result = await simulatePing(pingOptions.host);

    if (result) {
      pingResults.value.push(result);
      stats.packetsSent += 1;
      if (result.delay !== null) {
        stats.packetsReceived += 1;
        stats.averageDelay = Number(
          ((stats.averageDelay * (stats.packetsReceived - 1) + result.delay) / stats.packetsReceived).toFixed(2)
        );
      }
      stats.packetLoss = Number((((stats.packetsSent - stats.packetsReceived) / stats.packetsSent) * 100).toFixed(2));

      chartData.rows.push({
        time: new Date().getTime(),
        delay: result.delay ?? 0,
        loss: stats.packetLoss
      });
    }

    await new Promise(resolve => setTimeout(resolve, Number(pingOptions.interval)));
  }
};

// 对外暴露的重置状态方法
const resetState = () => {
  resetPing();
};

// 将重置方法暴露给父组件
defineExpose({
  resetState
});

onMounted(() => {
  // 页面加载时初始化
});
</script>

<style scoped>
.ping-container {
  padding: 20px;
}
.input-area {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
  margin-bottom: 20px;
}
.input-group {
  display: flex;
  align-items: center;
}
.ping-input {
  border-radius: 4px;
  transition: all 0.3s ease;
}
.ping-input:hover {
  box-shadow: 0 2px 8px rgb(0 0 0 / 5%);
  transform: translateY(-2px);
}
.host-input {
  width: 200px;
}
.count-input {
  width: 120px;
}
.interval-input {
  width: 150px;
}
.button-group {
  display: flex;
  gap: 10px;
}
.error-icon {
  margin-left: 8px;
  font-size: 18px;
  color: #f56c6c;
}
.action-button {
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}
.action-button:hover {
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
  transform: translateY(-2px);
}
</style>
