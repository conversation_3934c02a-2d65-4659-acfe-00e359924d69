<template>
  <el-drawer
    v-model="drawerVisible"
    :destroy-on-close="true"
    :size="drawerSize"
    :title="`${drawerProps.title} ${t('device.device')}`"
    @open="handleDrawerOpen"
    @close="onDrawerClose"
  >
    <el-form
      ref="ruleFormRef"
      label-width="100px"
      label-suffix=" :"
      :rules="rules"
      :disabled="drawerProps.isView"
      :model="deviceConfig"
      :hide-required-asterisk="true"
      :validate-on-rule-change="false"
    >
      <el-form-item>
        <DeviceIcon :device-type="drawerProps.row?.deviceType" />
      </el-form-item>
      <el-form-item :label="$t('device.deviceName')" label-width="160px" prop="deviceName">
        <el-text v-if="!editName">{{ displayDeviceName }}</el-text>
        <el-input
          v-model="deviceConfig.deviceName"
          v-if="editName"
          clearable
          style="width: 60%"
          :placeholder="t('device.deviceNamePlaceholder')"
          @input="deviceNameChanged = true"
        >
          <template #append>
            <div style="display: flex; gap: 8px; align-items: center">
              <el-link
                type="danger"
                :icon="Close"
                @click="cancelEdit"
                v-if="editName"
                style="display: flex; align-items: center; justify-content: center; height: 20px; padding: 0"
              />
              <el-divider direction="vertical" style="margin: 0" />
              <el-link
                type="primary"
                :icon="Check"
                @click="saveDeviceName(ruleFormRef)"
                v-if="editName"
                :disabled="deviceNameChanged === false"
                style="display: flex; align-items: center; justify-content: center; height: 20px; padding: 0"
              />
            </div>
          </template>
        </el-input>
        <el-link target="_blank" :icon="Edit" @click="editDeviceName()" v-if="!editName">
          {{ $t("common.edit") }}
        </el-link>
      </el-form-item>
      <el-tabs type="card" v-model="activeName" @tab-change="handleTabChange">
        <el-tab-pane :label="t('device.deviceInfo')" name="first">
          <DeviceInfo
            :device-id="drawerProps.row.deviceId"
            :device-model="drawerProps.row.deviceModel"
            :device-type="drawerProps.row.deviceType"
            :device-types="deviceTypes"
            :boot-time="drawerProps.row.bootTime"
            :mac="drawerProps.row.mac"
            :ipaddr="drawerProps.row.ipaddr"
            :status-tag-type="statusTagType"
            :status-label="statusLabel"
            :network="deviceStatus.network"
            :system="deviceStatus.system"
          />
        </el-tab-pane>
        <el-tab-pane :label="$t('device.networkSettings')" name="second">
          <el-collapse v-model="activeNames">
            <el-collapse-item :title="$t('device.wifiSettings')" v-if="drawerProps.row?.supports?.wireless" name="wifi">
              <!-- wifi设置 -->
              <el-card
                shadow="always"
                :style="{ marginTop: '10px', marginBottom: '10px' }"
                class="box-card"
                v-if="drawerProps.row?.supports?.wireless?.supports.includes('wifiTime') && deviceConfig.wireless?.wifiTime"
              >
                <el-header>
                  <el-form-item :label="$t('device.timeSwitch')" label-position="left">
                    <el-switch
                      v-model="deviceConfig.wireless.wifiTime.enabled"
                      active-color="#13ce66"
                      inactive-color="#ff4949"
                      :active-value="1"
                      :inactive-value="0"
                      :active-text="$t('device.open')"
                      :inactive-text="$t('device.close')"
                    />
                  </el-form-item>
                </el-header>
                <el-main v-if="deviceConfig.wireless.wifiTime.enabled === 1">
                  <el-form-item
                    :label="$t('device.timingTime')"
                    label-position="left"
                    label-width="160px"
                    prop="wireless.wifiTime.week"
                  >
                    <el-select v-model="deviceConfig.wireless.wifiTime.week" :placeholder="$t('device.timingTimePlaceholder')">
                      <el-option :label="$t('device.everyday')" :value="'*'" />
                      <el-option :label="$t('device.sunday')" :value="'0'" />
                      <el-option :label="$t('device.monday')" :value="'1'" />
                      <el-option :label="$t('device.tuesday')" :value="'2'" />
                      <el-option :label="$t('device.wednesday')" :value="'3'" />
                      <el-option :label="$t('device.thursday')" :value="'4'" />
                      <el-option :label="$t('device.friday')" :value="'5'" />
                      <el-option :label="$t('device.saturday')" :value="'6'" />
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    :label="$t('device.startupTime')"
                    label-position="left"
                    label-width="160px"
                    prop="wireless.wifiTime.endTime"
                  >
                    <el-time-picker
                      v-model="deviceConfig.wireless.wifiTime.endTime"
                      format="HH:mm"
                      value-format="HH:mm"
                      :placeholder="t('device.beginTimePlaceholder')"
                      :clearable="false"
                    ></el-time-picker>
                  </el-form-item>
                  <el-form-item
                    :label="$t('device.closingTime')"
                    label-position="left"
                    label-width="160px"
                    prop="wireless.wifiTime.beginTime"
                  >
                    <el-time-picker
                      v-model="deviceConfig.wireless.wifiTime.beginTime"
                      format="HH:mm"
                      value-format="HH:mm"
                      :placeholder="t('device.endTimePlaceholder')"
                      :clearable="false"
                    />
                  </el-form-item>
                </el-main>
              </el-card>
              <!-- 2.4GHz -->
              <el-card
                class="box-card"
                shadow="always"
                :style="{ marginTop: '10px', marginBottom: '10px' }"
                v-if="drawerProps.row?.supports?.wireless?.supports.includes('radio0') && deviceConfig.wireless?.radio0"
              >
                <el-header>
                  <el-form-item :label="$t('device.wifi24GHz')" label-position="left">
                    <el-switch
                      v-model="deviceConfig.wireless.radio0.disabled"
                      :active-text="$t('device.open')"
                      :inactive-text="$t('device.close')"
                      :active-value="0"
                      :inactive-value="1"
                      active-color="#13ce66"
                      inactive-color="#ff4949"
                    />
                  </el-form-item>
                </el-header>
                <el-main v-if="deviceConfig.wireless.radio0.disabled === 0">
                  <el-form-item :label="$t('device.name')" label-position="left" label-width="160px" prop="wireless.radio0.ssid">
                    <el-input v-model="deviceConfig.wireless.radio0.ssid" clearable />
                  </el-form-item>
                  <el-form-item :label="$t('device.hidden')" label-position="left" label-width="160px">
                    <el-switch v-model="deviceConfig.wireless.radio0.hidden" :active-value="1" :inactive-value="0" />
                  </el-form-item>
                  <el-form-item :label="$t('device.encryptionMethod')" label-position="left" label-width="160px">
                    <el-select v-model="encryptionRadio0Method">
                      <el-option :label="$t('device.encryption')" :value="true" />
                      <el-option :label="$t('device.encryptionNone')" :value="false" />
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    :label="$t('device.key')"
                    label-position="left"
                    label-width="160px"
                    v-if="encryptRadio0"
                    prop="wireless.radio0.key"
                  >
                    <el-input
                      type="password"
                      v-model="deviceConfig.wireless.radio0.key"
                      show-password
                      clearable
                      @clear="handlePasswordClear('radio0')"
                    />
                  </el-form-item>
                  <el-form-item :label="$t('device.channel')" label-position="left" label-width="160px">
                    <el-select v-model="deviceConfig.wireless.radio0.channel">
                      <el-option :label="t('common.auto')" :value="0" />
                      <el-option
                        v-for="channel in deviceConfig.wireless.radio0.chanList"
                        :key="channel"
                        :label="channel"
                        :value="channel"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item :label="$t('device.txpower')" label-position="left" label-width="160px">
                    <el-select v-model="deviceConfig.wireless.radio0.txpower">
                      <el-option
                        v-for="percent in [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]"
                        :key="percent"
                        :label="`${percent}%`"
                        :value="percent"
                      />
                    </el-select>
                  </el-form-item>
                </el-main>
              </el-card>
              <!-- 5G -->
              <el-card
                class="box-card"
                shadow="always"
                :style="{ marginTop: '10px', marginBottom: '10px' }"
                v-if="drawerProps.row?.supports?.wireless?.supports.includes('radio1') && deviceConfig.wireless?.radio1"
              >
                <el-header>
                  <el-form-item :label="$t('device.wifi5GHz')" label-position="left" label-width="160px">
                    <el-switch
                      v-model="deviceConfig.wireless.radio1.disabled"
                      active-color="#13ce66"
                      :active-value="0"
                      :inactive-value="1"
                      inactive-color="#ff4949"
                      :active-text="$t('device.open')"
                      :inactive-text="$t('device.close')"
                    />
                  </el-form-item>
                </el-header>
                <el-main v-if="deviceConfig.wireless.radio1.disabled === 0">
                  <el-form-item :label="$t('device.name')" label-position="left" label-width="160px" prop="wireless.radio1.ssid">
                    <el-input v-model="deviceConfig.wireless.radio1.ssid" clearable />
                  </el-form-item>
                  <el-form-item :label="$t('device.hidden')" label-position="left" label-width="160px">
                    <el-switch v-model="deviceConfig.wireless.radio1.hidden" :active-value="1" :inactive-value="0" />
                  </el-form-item>
                  <el-form-item :label="$t('device.encryptionMethod')" label-position="left" label-width="160px">
                    <el-select v-model="encryptionRadio1Method">
                      <el-option :label="$t('device.encryption')" :value="true" />
                      <el-option :label="$t('device.encryptionNone')" :value="false" />
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    :label="$t('device.key')"
                    label-position="left"
                    label-width="160px"
                    v-if="encryptRadio1 === true"
                    prop="wireless.radio1.key"
                  >
                    <el-input
                      type="password"
                      v-model="deviceConfig.wireless.radio1.key"
                      show-password
                      clearable
                      @clear="handlePasswordClear('radio1')"
                    />
                  </el-form-item>
                  <el-form-item :label="$t('device.channel')" label-position="left" label-width="160px">
                    <el-select v-model="deviceConfig.wireless.radio1.channel">
                      <el-option :label="t('common.auto')" :value="0" />
                      <el-option
                        v-for="channel in deviceConfig.wireless.radio1.chanList"
                        :key="channel"
                        :label="channel"
                        :value="channel"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item :label="$t('device.txpower')" label-position="left" label-width="160px">
                    <el-select v-model="deviceConfig.wireless.radio1.txpower">
                      <el-option
                        v-for="percent in [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]"
                        :key="percent"
                        :label="`${percent}%`"
                        :value="percent"
                      />
                    </el-select>
                  </el-form-item>
                </el-main>
              </el-card>
              <el-card
                class="box-card"
                shadow="always"
                :style="{ marginTop: '10px', marginBottom: '10px' }"
                v-if="drawerProps.row?.supports?.wireless?.supports.includes('guest') && deviceConfig.wireless?.guest"
              >
                <el-header>
                  <el-form-item :label="$t('device.guestWifi')" label-position="left" label-width="160px">
                    <el-switch
                      v-model="deviceConfig.wireless.guest.disabled"
                      active-color="#13ce66"
                      :active-value="0"
                      :inactive-value="1"
                      inactive-color="#ff4949"
                      :active-text="$t('device.open')"
                      :inactive-text="$t('device.close')"
                    />
                  </el-form-item>
                </el-header>
                <el-main v-if="deviceConfig.wireless.guest.disabled === 0">
                  <el-form-item :label="$t('device.name')" label-position="left" label-width="160px" prop="wireless.guest.ssid">
                    <el-input v-model="deviceConfig.wireless.guest.ssid" clearable />
                  </el-form-item>
                  <el-form-item :label="$t('device.encryptionMethod')" label-position="left" label-width="160px">
                    <el-select v-model="encryptionGuestMethod">
                      <el-option :label="$t('device.encryption')" :value="true" />
                      <el-option :label="$t('device.encryptionNone')" :value="false" />
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    :label="$t('device.key')"
                    label-position="left"
                    label-width="160px"
                    v-if="encryptGuest"
                    prop="wireless.guest.key"
                  >
                    <el-input
                      type="password"
                      v-model="deviceConfig.wireless.guest.key"
                      show-password
                      clearable
                      @clear="handlePasswordClear('guest')"
                    />
                  </el-form-item>
                  <el-form-item :label="$t('device.rate')" label-position="left" label-width="160px" prop="wireless.guest.rate">
                    <el-select
                      v-model="deviceConfig.wireless.guest.rate"
                      allow-create
                      filterable
                      :placeholder="t('device.wirelessRateTip')"
                      @change="handleGuestRateChange"
                    >
                      <el-option v-for="item in guestRateOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-form-item>
                  <el-form-item :label="$t('device.wifiTime')" label-position="left" label-width="160px">
                    <el-select v-model="deviceConfig.wireless.guest.wifiTime">
                      <el-option :label="$t('device.forever')" :value="0" />
                      <el-option :label="`${2} ${$t('device.hour')}`" :value="2" />
                      <el-option :label="`${4} ${$t('device.hour')}`" :value="4" />
                      <el-option :label="`${8} ${$t('device.hour')}`" :value="8" />
                      <el-option :label="`${12} ${$t('device.hour')}`" :value="12" />
                      <el-option :label="`${24} ${$t('device.hour')}`" :value="24" />
                    </el-select>
                  </el-form-item>
                </el-main>
              </el-card>
            </el-collapse-item>
            <!-- DHCP -->
            <el-collapse-item
              :title="$t('device.DhcpSettings')"
              v-if="drawerProps.row?.supports?.network?.supports.includes('dhcp') && deviceConfig.network?.dhcp"
              name="dhcp"
            >
              <el-card>
                <el-form-item :label="t('device.startIp')" prop="network.dhcp.start" label-position="left" label-width="160px">
                  <el-input
                    v-model="deviceConfig.network.dhcp.start"
                    @input="handleStartIpChange"
                    :placeholder="t('device.startIpPlaceholder')"
                    clearable
                  >
                    <template #prepend>
                      <span>{{ startIpPrefix }}</span>
                    </template>
                  </el-input>
                  <div style="margin-top: 2px; font-size: 13px; color: #909399">
                    <el-icon style="margin-right: 2px; font-size: 14px; vertical-align: middle">
                      <InfoFilled />
                    </el-icon>
                    {{ t("device.startIpTip") }}
                  </div>
                </el-form-item>
                <el-form-item
                  :label="t('device.allocateNumber')"
                  prop="network.dhcp.limit"
                  label-position="left"
                  label-width="160px"
                >
                  <el-input
                    v-model="deviceConfig.network.dhcp.limit"
                    :placeholder="t('device.allocateNumberPlaceholder')"
                    clearable
                  ></el-input>
                  <div style="margin-top: 2px; font-size: 13px; color: #909399">
                    <el-icon style="margin-right: 2px; font-size: 14px; vertical-align: middle">
                      <InfoFilled />
                    </el-icon>
                    {{ t("device.limitTip") }}
                  </div>
                </el-form-item>
              </el-card>
            </el-collapse-item>
            <!-- 网桥WIFI -->
            <el-collapse-item
              :title="$t('device.wifiSettings')"
              v-if="drawerProps.row?.supports?.network?.supports.includes('brAp')"
              name="brAp"
            >
              <el-card shadow="always" :style="{ marginTop: '10px', marginBottom: '10px' }" class="box-card">
                <el-main>
                  <el-form-item :label="$t('device.name')" label-position="left" label-width="160px" prop="network.brAp.ssid">
                    <el-input v-model="deviceConfig.network.brAp.ssid" clearable />
                  </el-form-item>
                  <el-form-item :label="$t('device.hidden')" label-position="left" label-width="160px">
                    <el-switch v-model="deviceConfig.network.brAp.hidden" :active-value="1" :inactive-value="0" />
                  </el-form-item>
                  <el-form-item :label="$t('device.key')" label-position="left" label-width="160px" prop="network.brAp.key">
                    <el-input type="password" v-model="deviceConfig.network.brAp.key" show-password clearable />
                  </el-form-item>
                  <el-form-item :label="$t('device.channel')" label-position="left" label-width="160px">
                    <el-select v-model="deviceConfig.network.brAp.channel">
                      <el-option :label="t('common.auto')" :value="0" />
                      <el-option
                        v-for="channel in deviceConfig.network.brAp.chanList"
                        :key="channel"
                        :label="channel"
                        :value="channel"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item :label="$t('device.txpower')" label-position="left" label-width="160px">
                    <el-select v-model="deviceConfig.network.brAp.txpower" clearable>
                      <el-option
                        v-for="percent in [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]"
                        :key="percent"
                        :label="`${percent}%`"
                        :value="percent"
                      />
                    </el-select>
                  </el-form-item>
                </el-main>
              </el-card>
            </el-collapse-item>
            <!-- LAN -->
            <el-collapse-item
              :title="$t('device.lanSettings')"
              v-if="drawerProps.row?.supports?.network?.supports.includes('lan') && deviceConfig.network?.lan"
              name="lan"
            >
              <el-card>
                <el-form-item :label="t('device.lanIp')" prop="network.lan.ipaddr" label-position="left" label-width="160px">
                  <el-input v-model="deviceConfig.network.lan.ipaddr" :placeholder="t('device.lanIpTip')" clearable></el-input>
                </el-form-item>
                <el-form-item
                  :label="t('device.lanNetmask')"
                  prop="network.lan.netmask"
                  label-position="left"
                  label-width="160px"
                >
                  <el-input
                    v-model="deviceConfig.network.lan.netmask"
                    :placeholder="t('device.lanNetmaskTip')"
                    clearable
                  ></el-input>
                </el-form-item>
              </el-card>
            </el-collapse-item>
            <!-- WAN -->
            <el-collapse-item
              :title="$t('device.wanSettings')"
              v-if="
                drawerProps.row?.supports?.network?.supports.includes('wan') &&
                deviceConfig.network?.wan &&
                deviceConfig.network.wan.length > 0
              "
              name="wan"
            >
              <el-card>
                <el-form-item :label="t('device.proto')" prop="network.wan.0.proto" label-position="left" label-width="160px">
                  <el-select v-model="deviceConfig.network.wan[0].proto" :placeholder="t('common.pleaseSelect')" clearable>
                    <el-option :label="t('device.dhcp')" value="dhcp"></el-option>
                    <el-option :label="t('device.static')" value="static"></el-option>
                    <el-option :label="t('device.pppoe')" value="pppoe"></el-option>
                  </el-select>
                </el-form-item>
                <!-- PPPoE 配置 -->
                <template v-if="deviceConfig.network.wan[0].proto === 'pppoe'">
                  <el-form-item
                    :label="t('device.wanUsername')"
                    prop="network.wan.0.username"
                    label-position="left"
                    label-width="160px"
                  >
                    <el-input
                      v-model="deviceConfig.network.wan[0].username"
                      :placeholder="t('device.wanUsernamePlaceholder')"
                      clearable
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    :label="t('device.wanPassword')"
                    prop="network.wan.0.password"
                    label-position="left"
                    label-width="160px"
                  >
                    <el-input
                      type="password"
                      v-model="deviceConfig.network.wan[0].password"
                      :placeholder="t('device.wanPasswordPlaceholder')"
                      show-password
                      clearable
                    ></el-input>
                  </el-form-item>
                </template>

                <!-- Static 配置 -->
                <template v-if="deviceConfig.network.wan[0].proto === 'static'">
                  <el-form-item :label="t('device.wanIp')" prop="network.wan.0.ipaddr" label-position="left" label-width="160px">
                    <el-input
                      v-model="deviceConfig.network.wan[0].ipaddr"
                      :placeholder="t('device.wanIpPlaceholder')"
                      clearable
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    :label="t('device.wanNetmask')"
                    prop="network.wan.0.netmask"
                    label-position="left"
                    label-width="160px"
                  >
                    <el-input
                      v-model="deviceConfig.network.wan[0].netmask"
                      :placeholder="t('device.wanNetmaskPlaceholder')"
                      clearable
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    :label="t('device.wanGateway')"
                    prop="network.wan.0.gawa"
                    label-position="left"
                    label-width="160px"
                  >
                    <el-input
                      v-model="deviceConfig.network.wan[0].gawa"
                      :placeholder="t('device.wanGatewayPlaceholder')"
                      clearable
                    ></el-input>
                  </el-form-item>
                  <el-form-item :label="t('device.dns1')" prop="network.wan.0.dns1" label-position="left" label-width="160px">
                    <el-input
                      v-model="deviceConfig.network.wan[0].dns1"
                      :placeholder="t('device.dns1Placeholder')"
                      clearable
                    ></el-input>
                  </el-form-item>
                  <el-form-item :label="t('device.dns2')" prop="network.wan.0.dns2" label-position="left" label-width="160px">
                    <el-input
                      v-model="deviceConfig.network.wan[0].dns2"
                      :placeholder="t('device.dns2Placeholder')"
                      clearable
                    ></el-input>
                  </el-form-item>
                </template>
              </el-card>
            </el-collapse-item>
            <!-- DNS -->
            <el-collapse-item
              :title="$t('device.dnsSettings')"
              v-if="drawerProps.row?.supports?.network?.supports.includes('dhcp') && deviceConfig.network?.dhcp"
              name="dns"
            >
              <el-card class="box-card">
                <el-form-item :label="t('device.dnsEnabled')" prop="ip" label-position="left" label-width="160px">
                  <el-select v-model="deviceConfig.network.dhcp.dnsenabled" :placeholder="t('device.dhcpTip')">
                    <el-option :label="t('device.dnsModify')" :value="1"></el-option>
                    <el-option :label="t('device.dnsAuto')" :value="0"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item
                  :label="t('device.dns1')"
                  prop="network.dhcp.dns1"
                  label-position="left"
                  label-width="160px"
                  v-if="deviceConfig.network.dhcp && deviceConfig.network.dhcp.dnsenabled === 1"
                >
                  <el-input
                    v-model="deviceConfig.network.dhcp.dns1"
                    :placeholder="$t('device.dns1Placeholder')"
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item
                  :label="t('device.dns2')"
                  prop="network.dhcp.dns2"
                  label-position="left"
                  label-width="160px"
                  v-if="deviceConfig.network.dhcp && deviceConfig.network.dhcp.dnsenabled === 1"
                >
                  <el-input
                    v-model="deviceConfig.network.dhcp.dns2"
                    :placeholder="$t('device.dns2Placeholder')"
                    clearable
                  ></el-input>
                </el-form-item>
              </el-card>
            </el-collapse-item>
          </el-collapse>
        </el-tab-pane>
        <el-tab-pane :label="t('device.securitySettings')" name="third">
          <el-collapse v-model="securityActiveNames">
            <el-collapse-item :title="$t('device.managePassword')" name="managePassword">
              <el-card shadow="always" :style="{ marginTop: '10px', marginBottom: '10px' }" class="box-card">
                <el-main>
                  <el-form-item :label="$t('device.oldPassword')" label-position="left" label-width="160px" prop="password">
                    <el-input type="password" v-model="password" show-password clearable />
                  </el-form-item>
                  <el-form-item
                    :label="$t('device.newPassword')"
                    label-position="left"
                    label-width="160px"
                    prop="system.sysPassword"
                  >
                    <el-input type="password" v-model="deviceConfig.system.sysPassword" show-password clearable />
                  </el-form-item>
                  <el-form-item
                    :label="$t('device.confirmPassword')"
                    label-position="left"
                    label-width="160px"
                    prop="system.reSysPassword"
                  >
                    <el-input type="password" v-model="deviceConfig.system.reSysPassword" show-password clearable />
                  </el-form-item>
                </el-main>
              </el-card>
            </el-collapse-item>
            <el-collapse-item
              :title="$t('device.securityManager')"
              v-if="drawerProps.row?.supports?.network?.supports.includes('brSafe') && deviceConfig.network?.brSafe"
              name="securityManager"
            >
              <el-card shadow="always" :style="{ marginTop: '10px', marginBottom: '10px' }" class="box-card">
                <el-main>
                  <el-form-item :label="$t('device.status')" label-position="left" label-width="160px">
                    <el-text>{{ getModeText(deviceStatus.network?.brSafe?.mode ?? 0) }}</el-text>
                  </el-form-item>

                  <el-form-item
                    :label="$t('device.lockMode')"
                    label-position="left"
                    v-if="deviceConfig.network?.brSafe"
                    label-width="160px"
                  >
                    <el-select
                      v-model="deviceConfig.network.brSafe.mode"
                      :placeholder="$t('device.timeLockingPlaceholder')"
                      clearable
                    >
                      <el-option :label="$t('device.unlocking')" :value="0"></el-option>
                      <el-option :label="$t('device.timeLocking')" :value="1"></el-option>
                      <el-option :label="$t('device.alwaysLock')" :value="2"></el-option>
                    </el-select>
                  </el-form-item>
                  <!--                  {{ deviceConfig.network.brSafe.time }}-->
                  <template v-if="deviceConfig && deviceConfig.network && deviceConfig.network.brSafe">
                    <el-form-item
                      :label="$t('device.timeLocking')"
                      v-if="deviceConfig?.network?.brSafe?.mode === 1"
                      label-position="left"
                      label-width="160px"
                    >
                      <el-select v-model="deviceConfig.network.brSafe.time" :placeholder="$t('device.timeLockingPlaceholder')">
                        <el-option :label="$t('device.oneMinute')" :value="60"></el-option>
                        <el-option :label="$t('device.twoMinutes')" :value="120"></el-option>
                        <el-option :label="$t('device.fiveMinutes')" :value="300"></el-option>
                        <el-option :label="$t('device.tenMinutes')" :value="600"></el-option>
                        <el-option :label="$t('device.thirtyMinutes')" :value="1800"></el-option>
                        <el-option :label="$t('device.oneHour')" :value="3600"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-main>
              </el-card>
            </el-collapse-item>
          </el-collapse>
        </el-tab-pane>
        <el-tab-pane :label="t('device.portSettings')" name="fourth" v-if="shouldShowPortSettings">
          <div v-if="portSettingsLoading" style="padding: 16px">
            <el-skeleton :rows="8" animated />
          </div>
          <div v-else>
            <el-divider v-if="deviceConfig.system.swPort && drawerProps.row?.deviceType === 'switch'"></el-divider>
            <el-card
              v-if="
                drawerProps.row?.deviceType === 'switch' && deviceConfig.system.swPort && deviceConfig.system.swPort.length > 0
              "
            >
              <div class="sw-port-container">
                <!-- 端口数 <= 5 时，单行显示 -->
                <ul v-if="deviceConfig.system.swPort.length <= 5" class="sw-port-list single-row">
                  <li
                    v-for="(item, index) in deviceConfig.system.swPort"
                    :key="index"
                    :class="['sw-port-tag', { 'selected-port': isRowSelected(item) }]"
                    @click="toggleRowSelection(item)"
                  >
                    <el-image :src="item.link ? portActiveIcon : portDeactiveIcon" class="port-icon-img" alt="Port Icon" />
                    <div class="port-name-container">
                      <el-input
                        v-if="item.isEditing"
                        v-model="item.describe"
                        size="small"
                        ref="portNameInput"
                        @blur="handleDescribeConfirm(item)"
                        @keyup.enter="handleDescribeConfirm(item)"
                        @keyup.esc="handleDescribeCancel(item)"
                        maxlength="32"
                        show-word-limit
                        class="port-name-input"
                      ></el-input>
                      <div v-else class="port-name-display" @click.stop="handleDescribeEdit(item)">
                        <el-text class="port-name-text" :title="item.describe || item.name">{{
                          item.describe || item.name
                        }}</el-text>
                        <el-icon class="edit-icon"><Edit /></el-icon>
                      </div>
                    </div>
                  </li>
                </ul>

                <!-- 端口数 > 5 时，两行显示：GE1 GE3 GE5 / GE2 GE4 GE6 -->
                <div v-else class="sw-port-multi-row">
                  <!-- 第一行：奇数索引端口 -->
                  <ul class="sw-port-row">
                    <li
                      v-for="(item, index) in deviceConfig.system.swPort.filter((_, i) => i % 2 === 0)"
                      :key="'odd-' + index"
                      :class="['sw-port-tag', { 'selected-port': isRowSelected(item) }]"
                      @click="toggleRowSelection(item)"
                    >
                      <el-image :src="item.link ? portActiveIcon : portDeactiveIcon" class="port-icon-img" alt="Port Icon" />
                      <div class="port-name-container">
                        <el-input
                          v-if="item.isEditing"
                          v-model="item.describe"
                          size="small"
                          ref="portNameInput"
                          @blur="handleDescribeConfirm(item)"
                          @keyup.enter="handleDescribeConfirm(item)"
                          @keyup.esc="handleDescribeCancel(item)"
                          maxlength="32"
                          show-word-limit
                          class="port-name-input"
                        ></el-input>
                        <div v-else class="port-name-display" @click.stop="handleDescribeEdit(item)">
                          <el-text class="port-name-text" :title="item.describe || item.name">{{
                            item.describe || item.name
                          }}</el-text>
                          <el-icon class="edit-icon"><Edit /></el-icon>
                        </div>
                      </div>
                    </li>
                  </ul>

                  <!-- 第二行：偶数索引端口 -->
                  <ul class="sw-port-row">
                    <li
                      v-for="(item, index) in deviceConfig.system.swPort.filter((_, i) => i % 2 === 1)"
                      :key="'even-' + index"
                      :class="['sw-port-tag', { 'selected-port': isRowSelected(item) }]"
                      @click="toggleRowSelection(item)"
                    >
                      <el-image :src="item.link ? portActiveIcon : portDeactiveIcon" class="port-icon-img" alt="Port Icon" />
                      <div class="port-name-container">
                        <el-input
                          v-if="item.isEditing"
                          v-model="item.describe"
                          size="small"
                          ref="portNameInput"
                          @blur="handleDescribeConfirm(item)"
                          @keyup.enter="handleDescribeConfirm(item)"
                          @keyup.esc="handleDescribeCancel(item)"
                          maxlength="32"
                          show-word-limit
                          class="port-name-input"
                        ></el-input>
                        <div v-else class="port-name-display" @click.stop="handleDescribeEdit(item)">
                          <el-text class="port-name-text" :title="item.describe || item.name">{{
                            item.describe || item.name
                          }}</el-text>
                          <el-icon class="edit-icon"><Edit /></el-icon>
                        </div>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
              <el-button link :icon="showPortExample ? 'ArrowUp' : 'ArrowDown'" @click="togglePortExample">{{
                showPortExample ? $t("common.CollapseDiagram") : $t("common.ExpandDiagram")
              }}</el-button>
              <ul class="sw-port-list" v-if="showPortExample">
                <li v-for="(item, index) in portStates" :key="index" class="sw-port-example">
                  <el-image :src="item.icon" class="port-icon" :alt="item.text" />
                  <span class="port-text">{{ item.text }}</span>
                </li>
              </ul>
            </el-card>
            <el-divider></el-divider>
            <el-button size="default" type="primary" @click="openPortDialog">{{ $t("device.configuration") }}</el-button>
            <el-table
              ref="portTableRef"
              :data="deviceConfig.system.swPort"
              style="width: 100%"
              @selection-change="handleSelectionChange"
              :selection="selectedRows"
            >
              <el-table-column type="selection" width="55"> </el-table-column>
              <el-table-column :label="t('device.portName')">
                <template #default="scope">
                  <el-input
                    v-if="scope.row.isEditing"
                    v-model="scope.row.describe"
                    ref="tablePortNameInput"
                    @blur="handleDescribeConfirm(scope.row)"
                    @keyup.enter="handleDescribeConfirm(scope.row)"
                    @keyup.esc="handleDescribeCancel(scope.row)"
                    maxlength="32"
                    show-word-limit
                  >
                  </el-input>
                  <div v-else class="edit-cell" @click="handleDescribeEdit(scope.row)">
                    {{ scope.row.describe || scope.row.name }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="portenable" :label="t('device.portStatus')">
                <template #default="scope">
                  <span>{{ scope.row.portenable === 1 ? t("device.open") : t("device.close") }}</span>
                </template>
              </el-table-column>
              <el-table-column :label="t('device.portPowerLimit')" width="180px">
                <template #default="scope">
                  <span>{{ deviceConfig.system?.swPoe[scope.$index]?.power ?? "--" }}/</span>
                  {{
                    deviceConfig.system.swPoe[scope.$index]?.powerout === 0
                      ? "af (15.4w)"
                      : deviceConfig.system.swPoe[scope.$index]?.powerout === 1
                        ? "at (30w)"
                        : "--"
                  }}
                </template>
              </el-table-column>
              <el-table-column prop="power" :label="t('device.portRate')">
                <template #default="scope">
                  {{ scope.row.link === 1 ? formatSpeedDuplex(scope.row.speed_duplex) : formatAutoneg(scope.row.autoneg) }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane :label="t('device.systemSettings')" name="fifth">
          <el-card v-if="deviceConfig.system?.led && deviceConfig.system.led.mode" style="margin-bottom: 16px">
            <template #header>
              {{ t("device.ledConfiguration") }}
            </template>
            <el-form-item :label="t('device.ledMode')" label-width="160px" label-position="left">
              <el-select v-model="deviceConfig.system.led.mode" :placeholder="t('common.pleaseSelect')">
                <el-option :label="t('device.on')" :value="'on'"></el-option>
                <el-option :label="t('device.off')" :value="'off'"></el-option>
                <el-option :label="t('device.timerOff')" :value="'timer'"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              v-if="deviceConfig.system.led.mode === 'timer'"
              :label="t('device.ledTimeRange')"
              label-width="160px"
              label-position="left"
              prop="system.led.endTime"
              :rules="[
                {
                  validator: (rule, value, callback) => {
                    if (
                      deviceConfig.system.led.mode === 'timer' &&
                      deviceConfig.system.led.beginTime &&
                      value &&
                      deviceConfig.system.led.beginTime === value
                    ) {
                      callback(new Error(t('device.ledTimeSameError')));
                    } else {
                      callback();
                    }
                  },
                  trigger: ['blur', 'change']
                }
              ]"
            >
              <el-space :size="12">
                <el-time-picker
                  v-model="deviceConfig.system.led.beginTime"
                  :placeholder="t('device.beginTimePlaceholder')"
                  format="HH:mm"
                  value-format="HH:mm"
                  :clearable="false"
                  style="width: 200px"
                />
                <span>-</span>
                <el-time-picker
                  v-model="deviceConfig.system.led.endTime"
                  :placeholder="t('device.endTimePlaceholder')"
                  format="HH:mm"
                  value-format="HH:mm"
                  :clearable="false"
                  style="width: 200px"
                />
              </el-space>
            </el-form-item>
          </el-card>
          <el-card style="margin-bottom: 16px">
            <template #header>
              {{ t("common.settings") }}
            </template>
            <el-form-item :label="t('device.reboot')" label-position="left" label-width="160px">
              <el-button type="primary" @click="handleReboot()">
                {{ t("device.reboot") }}
              </el-button>
            </el-form-item>
            <el-form-item :label="t('device.upgrade')" label-position="left" label-width="160px">
              <el-button type="primary" @click="handleUpgrade()">
                {{ t("device.upgrade") }}
              </el-button>
            </el-form-item>
            <el-progress :percentage="downloadingPer" v-if="progressShow"></el-progress>
            <el-form-item
              v-if="
                deviceConfig &&
                deviceConfig.system &&
                deviceConfig.system.reboot &&
                deviceConfig.system.reboot.enabled !== undefined
              "
              :label="$t('device.timeRestart')"
              label-position="left"
              label-width="160px"
            >
              <el-switch v-model="deviceConfig.system.reboot.enabled" :active-value="1" :inactive-value="0" />
            </el-form-item>
            <el-form-item
              v-if="deviceConfig?.system?.reboot && deviceConfig?.system?.reboot?.enabled !== 0"
              :label="$t('device.timeRestartWeek')"
              label-position="left"
              label-width="160px"
            >
              <el-select v-model="deviceConfig.system.reboot.week" :placeholder="$t('device.timeRestartWeekPlaceholder')">
                <el-option :label="$t('device.everyday')" :value="'*'" />
                <el-option :label="$t('device.sunday')" :value="'0'" />
                <el-option :label="$t('device.monday')" :value="'1'" />
                <el-option :label="$t('device.tuesday')" :value="'2'" />
                <el-option :label="$t('device.wednesday')" :value="'3'" />
                <el-option :label="$t('device.thursday')" :value="'4'" />
                <el-option :label="$t('device.friday')" :value="'5'" />
                <el-option :label="$t('device.saturday')" :value="'6'" />
              </el-select>
            </el-form-item>
            <el-form-item
              v-if="deviceConfig?.system?.reboot && deviceConfig?.system?.reboot?.enabled !== 0"
              :label="$t('device.timeRestartTime')"
              label-position="left"
              label-width="160px"
            >
              <el-time-picker
                v-model="deviceConfig.system.reboot.time"
                :placeholder="$t('device.timeRestartTimePlaceholder')"
                format="HH:mm"
                value-format="HH:mm"
                :clearable="false"
              />
            </el-form-item>
            <el-form-item
              v-if="
                deviceConfig && deviceConfig.system && deviceConfig.system.reboot && deviceConfig?.system?.reboot?.enabled !== 0
              "
              :label="$t('device.timeRestartRate')"
              label-position="left"
              label-width="160px"
            >
              <el-switch
                v-if="deviceConfig?.system?.reboot"
                v-model="deviceConfig.system.reboot.rateDelay"
                size="small"
                :active-value="1"
                :inactive-value="0"
              />
            </el-form-item>
          </el-card>
        </el-tab-pane>
        <el-tab-pane
          :label="t('device.deviceStatistics')"
          name="sixth"
          v-if="drawerProps.row?.supports?.system?.supports.includes('statistics')"
        >
          <D3LineChart :data="chartData" :width="chartWidth" :height="chartHeight" />
        </el-tab-pane>
      </el-tabs>
    </el-form>
    <template #footer>
      <el-button @click="closeDialog()">{{ $t("common.cancel") }}</el-button>
      <el-button v-show="!drawerProps.isView" type="primary" :disabled="!hasConfigChanged" @click="() => handleSubmit()">
        {{ $t("common.confirm") }}
      </el-button>
    </template>
  </el-drawer>
  <el-dialog v-model="portDialogVisible" :title="t('device.portSettings')" width="700px" draggable>
    <el-text style="margin: 10px; font-size: 16px; font-weight: bolder; text-align: center">
      {{ $t("device.configApplyTip") }}
    </el-text>
    {{ selectedRowNames }}
    <el-card v-if="deviceConfig.system.swPort && deviceConfig.system.swPort.length > 0">
      <el-tabs tab-position="left" style="height: 300px" class="demo-tabs" v-model="dialogTabActive">
        <el-tab-pane :label="t('device.portSettings')" name="first">
          <el-form ref="portForm" :model="swPort" label-width="160px" :hide-required-asterisk="true">
            <el-form-item :label="t('device.portSwitch')">
              <el-switch v-model="swPort.portenable" :active-value="1" :inactive-value="0" />
            </el-form-item>
            <el-form-item :label="t('device.portExtend')">
              <el-switch v-model="swPort.extend" :active-value="1" :inactive-value="0" />
            </el-form-item>
            <el-form-item :label="t('device.adaptive')" v-if="swPort.extend === 0">
              <el-switch v-model="swPort.autoneg" :active-value="1" :inactive-value="0" />
            </el-form-item>
            <el-form-item :label="t('device.flowControl')" v-if="swPort.autoneg == 0 && swPort.extend === 0">
              <el-switch v-model="swPort.flwctrl" :inactive-value="0" :active-value="1" />
            </el-form-item>
            <el-form-item :label="t('device.speedDuplex')" v-if="swPort.autoneg == 0 && swPort.extend === 0">
              <el-select v-model="swPort.speed_duplex" :placeholder="t('common.pleaseSelect')">
                <el-option
                  v-for="speedOption in speedDuplexOptions"
                  :key="speedOption.value"
                  :label="formatSpeedDuplex(speedOption.label)"
                  :value="speedOption.value"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <!-- POE配置 -->
        <el-tab-pane :label="t('device.PoeSettings')" name="second">
          <el-form ref="poeForm" :model="swPoe" label-width="100px" :hide-required-asterisk="true">
            <el-form-item :label="t('device.poeSwitch')">
              <el-switch v-model="swPoe.poeenable" :active-value="1" :inactive-value="0" />
            </el-form-item>
            <el-form-item :label="t('device.poeClass')">
              <el-text>{{ swPoe.poeclass }}</el-text>
            </el-form-item>
            <el-form-item :label="t('device.portPowerLimit')">
              <el-select v-model="swPoe.powerout" :placeholder="t('common.pleaseSelect')">
                <el-option v-for="(label, value) in poePowerOptions" :key="value" :label="label" :value="Number(value)" />
              </el-select>
            </el-form-item>
            <el-form-item :label="t('device.portPower')">
              <el-text>{{ (swPoe.power || 0) + " W" }}</el-text>
            </el-form-item>
            <el-form-item :label="t('device.poeWatchDog')">
              <el-switch v-model="swPoe.poewd" :active-value="1" :inactive-value="0" />
            </el-form-item>
            <el-form-item :label="t('device.PoeWatchDogTime')" v-if="swPoe.poewd === 1">
              <el-input-number v-model="swPoe.poetime" />
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane :label="t('device.VlanSettings')" name="third">
          <el-form ref="vlanForm" :model="swVlan" :rules="vlanRules" label-width="100px" :hide-required-asterisk="true">
            <el-form-item :label="t('device.portVlan')" prop="vlanmode">
              <el-select v-model="swVlan.vlanmode" :placeholder="t('common.pleaseSelect')">
                <el-option v-for="(label, value) in vlanModeOptions" :key="value" :label="label" :value="Number(value)" />
              </el-select>
            </el-form-item>
            <el-form-item label="VLAN" prop="pvid">
              <el-input-number v-model="swVlan.pvid" :placeholder="t('device.pvidPlaceholder')" :min="1" :max="4094" />
            </el-form-item>
            <el-form-item label="Permit VLAN" prop="permit" v-if="swVlan.vlanmode == 1 || swVlan.vlanmode == 2">
              <el-input v-model="permitVlanString" @input="updatePermitVlan" :placeholder="t('device.pvidPlaceholder')" />
            </el-form-item>
            <el-form-item label="Untag VLAN" prop="untag" v-if="swVlan.vlanmode == 2">
              <el-input v-model="untagVlanString" @input="updateUntagVlan" :placeholder="t('device.pvidPlaceholder')" />
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <!-- 风暴抑制 -->
        <el-tab-pane :label="t('device.StormSetting')" name="fourth">
          <el-form ref="stormForm" :model="swStorm" label-width="100px" :hide-required-asterisk="true">
            <el-form-item :label="t('device.trafficType')">
              <el-checkbox-group v-model="selectedTrafficTypes" @change="updateTrafficType">
                <el-checkbox
                  v-for="trafficOption in trafficTypeOptionsArray"
                  :key="trafficOption.value"
                  :value="trafficOption.value"
                >
                  {{ trafficOption.description }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item :label="$t('common.unicast')" v-if="selectedTrafficTypes.includes(4)">
              <el-tree-select
                v-model="swStorm.rate3"
                :data="rateOptions"
                :props="{
                  label: 'label',
                  value: 'value',
                  children: 'children',
                  checkStrictly: true,
                  disabled: data =>
                    data.value === 'common' ||
                    data.value === 'custom' ||
                    (typeof data.value === 'string' && data.value.startsWith('range-')),
                  isLeaf: data => !data.children || data.children.length === 0,
                  hasChildren: data =>
                    data.value === 'custom' || (typeof data.value === 'string' && data.value.startsWith('range-'))
                }"
                check-strictly
                node-key="value"
                :expand-on-click-node="true"
                @node-click="handleNodeClick"
              />
            </el-form-item>

            <el-form-item :label="$t('common.multicast')" v-if="selectedTrafficTypes.includes(1)">
              <el-tree-select
                v-model="swStorm.rate1"
                :data="rateOptions"
                :props="{
                  label: 'label',
                  value: 'value',
                  children: 'children',
                  checkStrictly: true,
                  disabled: data =>
                    data.value === 'common' ||
                    data.value === 'custom' ||
                    (typeof data.value === 'string' && data.value.startsWith('range-')),
                  isLeaf: data => !data.children || data.children.length === 0,
                  hasChildren: data =>
                    data.value === 'custom' || (typeof data.value === 'string' && data.value.startsWith('range-'))
                }"
                check-strictly
                node-key="value"
                :expand-on-click-node="true"
                @node-click="handleNodeClick"
              />
            </el-form-item>

            <el-form-item :label="$t('common.broadcast')" v-if="selectedTrafficTypes.includes(2)">
              <el-tree-select
                v-model="swStorm.rate2"
                :data="rateOptions"
                :props="{
                  label: 'label',
                  value: 'value',
                  children: 'children',
                  checkStrictly: true,
                  disabled: data =>
                    data.value === 'common' ||
                    data.value === 'custom' ||
                    (typeof data.value === 'string' && data.value.startsWith('range-')),
                  isLeaf: data => !data.children || data.children.length === 0,
                  hasChildren: data =>
                    data.value === 'custom' || (typeof data.value === 'string' && data.value.startsWith('range-'))
                }"
                check-strictly
                node-key="value"
                :expand-on-click-node="true"
                @node-click="handleNodeClick"
              />
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane :label="t('device.IsolationControl')" name="fifth">
          <el-form ref="isolationForm" :model="swPoe" label-width="100px" :hide-required-asterisk="true">
            <el-card v-if="deviceConfig.system.swPort && deviceConfig.system.swPort.length > 0">
              <div class="sw-port-container">
                <!-- 端口数 <= 5 时，单行显示 -->
                <ul v-if="deviceConfig.system.swPort.length <= 5" class="sw-port-list single-row">
                  <li
                    v-for="item in deviceConfig.system.swPort"
                    :key="item.port"
                    :class="['sw-port-tag', { 'selected-port': isPortSelected(item) }]"
                    @click="togglePortSelection(item)"
                  >
                    <el-image :src="item.link ? portActiveIcon : portDeactiveIcon" class="port-icon-img" alt="Port Icon" />
                    <div class="port-name-container">
                      <div class="port-name-display">
                        <el-text class="port-name-text" :title="item.name">{{ item.name }}</el-text>
                      </div>
                    </div>
                  </li>
                </ul>

                <!-- 端口数 > 5 时，两行显示 -->
                <div v-else class="sw-port-multi-row">
                  <!-- 第一行：奇数索引端口 -->
                  <ul class="sw-port-row">
                    <li
                      v-for="item in deviceConfig.system.swPort.filter((_, i) => i % 2 === 0)"
                      :key="'odd-' + item.port"
                      :class="['sw-port-tag', { 'selected-port': isPortSelected(item) }]"
                      @click="togglePortSelection(item)"
                    >
                      <el-image :src="item.link ? portActiveIcon : portDeactiveIcon" class="port-icon-img" alt="Port Icon" />
                      <div class="port-name-container">
                        <div class="port-name-display">
                          <el-text class="port-name-text" :title="item.name">{{ item.name }}</el-text>
                        </div>
                      </div>
                    </li>
                  </ul>

                  <!-- 第二行：偶数索引端口 -->
                  <ul class="sw-port-row">
                    <li
                      v-for="item in deviceConfig.system.swPort.filter((_, i) => i % 2 === 1)"
                      :key="'even-' + item.port"
                      :class="['sw-port-tag', { 'selected-port': isPortSelected(item) }]"
                      @click="togglePortSelection(item)"
                    >
                      <el-image :src="item.link ? portActiveIcon : portDeactiveIcon" class="port-icon-img" alt="Port Icon" />
                      <div class="port-name-container">
                        <div class="port-name-display">
                          <el-text class="port-name-text" :title="item.name">{{ item.name }}</el-text>
                        </div>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
              <el-button link @click="isolateAll">{{ t("common.selectAll") }}</el-button>
              <el-divider></el-divider>
              <el-text>
                {{ t("common.selectedPorts") }}:
                <!-- 如果 isolateRows 为空，显示 swIsolate，否则显示 isolateRows 的内容 -->
                <span v-if="isolateRows.length === 0">
                  <!-- 根据 swIsolate 数组的数字，匹配 deviceConfig.system.swPort 的 port 字段，显示对应的 name 字段 -->
                  {{ getPortNames(swIsolate).join(", ") }}
                </span>
                <span v-else>
                  {{ isolateRows.map(item => item.name).join(", ") }}
                </span>
              </el-text>
            </el-card>
          </el-form>
        </el-tab-pane>
        <el-tab-pane :label="t('device.QosSettings')" name="sixth">
          <el-form ref="qosForm" :model="swQos" label-width="100px" :hide-required-asterisk="true">
            <el-form-item :label="t('device.qosTip')">
              <el-select v-model="swQos.qos" :placeholder="t('common.pleaseSelect')">
                <!-- 动态生成 0-7 的选项 -->
                <el-option v-for="value in 8" :key="value" :label="`QoS ${value - 1}`" :value="value - 1" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-card>
    <template #footer>
      <el-button @click="portDialogVisible = false">{{ $t("common.cancel") }}</el-button>
      <el-button type="primary" :disabled="!hasDialogConfigChanged" @click="() => handleSubmit(true)">{{
        $t("common.confirm")
      }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="DeviceConfigDrawer">
import { computed, inject, onMounted, ref, watch, nextTick } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import DeviceIcon from "./DeviceIcon.vue";
import { useI18n } from "vue-i18n";
import { Project } from "@/api/interface/project";
import { Check, Edit, InfoFilled, Close } from "@element-plus/icons-vue";
import DeviceInfo from "./DeviceInfo.vue";
import {
  activeName,
  closeDialog,
  deviceConfig,
  deviceNameChanged,
  deviceStatus,
  deviceWeekStatistic,
  downloadingPer,
  drawerProps,
  drawerVisible,
  editName,
  encryptGuest,
  encryptionGuestMethod,
  encryptionRadio0Method,
  encryptionRadio1Method,
  encryptRadio0,
  encryptRadio1,
  formatAutoneg,
  generatePortData,
  getDeviceStatistics,
  getPortNames,
  getPortStates,
  handleReboot,
  handleSelectionChange,
  handleStartIpChange,
  handleUpgrade,
  isolateAll,
  isolateRows,
  isPortSelected,
  isRowSelected,
  loadDeviceConfig,
  loadDeviceStatus,
  poePowerOptions,
  portDialogVisible,
  portTableRef,
  preloadRateOptions,
  progressShow,
  rateOptions,
  saveDeviceName,
  selectedRowNames,
  selectedRows,
  selectedTrafficTypes,
  showPortDialog,
  showPortExample,
  speedDuplexOptions,
  startIpPrefix,
  statusLabel,
  statusTagType,
  swIsolate,
  swPoe,
  swPort,
  swQos,
  swStorm,
  swVlan,
  togglePortExample,
  togglePortSelection,
  toggleRowSelection,
  trafficTypeOptionsArray,
  updateTrafficType,
  vlanModeOptions,
  // 添加导入缺失的变量
  manualEncryptRadio0,
  manualEncryptRadio1,
  manualEncryptGuest,
  originalKey0,
  originalKey1,
  originalKeyGuest,
  // 新增：导入按需加载功能
  loadDeviceConfigFields,
  loadDeviceStatusFields,
  loadPortRelatedData,
  loadedTabs, // 导入 loadedTabs
  clearLoadedTabs // 导入 clearLoadedTabs
} from "@/api/interface/deviceConfigDrawer";
import D3LineChart from "@/components/D3LineChart/index.vue";
import { pushDeviceConfigJwe } from "@/api/modules/project";
import { useUserStore } from "@/stores/modules/user";
import { formatSpeedDuplex, getModeText } from "@/api/interface/device/formatter";
import { stringifyWithoutEmpty } from "@/utils";
// ECOption import removed - using D3 instead
import { getDiff } from "@/utils/diff";
import { getDefaultDeviceConfig, getDefaultDeviceStatus } from "@/api/interface/deviceConfigDrawer";
import { cloneDeep } from "lodash";
import portActiveIcon from "@/assets/images/port_active_icon.png";
import portDeactiveIcon from "@/assets/images/port_deactive_icon.png";

// 保存原始数据的副本，用于比较是否有变化
const originalData = ref(null);
const { t } = useI18n();

const baseGuestRateOptions = [
  { label: t("device.unlimitedSpeed"), value: 0 },
  { label: "8Mbps", value: 8 },
  { label: "16Mbps", value: 16 },
  { label: "32Mbps", value: 32 }
];
const guestRateOptions = ref([...baseGuestRateOptions]);

const portStates = getPortStates();

// 根据v-if条件动态确定默认展开的面板
const activeNames = ref<string[]>([]); // Change from computed to ref

const dialogTabActive = ref<"first" | "second" | "third" | "fourth" | "fifth" | "sixth">("first"); // dialog tabs默认选中第一个

const password = ref("");

const onDrawerClose = () => {
  selectedRows.value = []; // 清空选中项
  deviceNameChanged.value = false; // 重置变量
  editName.value = false;
  activeName.value = "first";
  clearLoadedTabs(); // 清空已加载的标签页状态
};

// 处理标签页切换
const handleTabChange = (name: string) => {
  loadTabData(name);
};

// 字符串形式的 VLAN 值
const permitVlanString = ref("");
const untagVlanString = ref("");

// 初始化字符串值
watch(
  () => swVlan.value.permit,
  newVal => {
    if (newVal) {
      permitVlanString.value = newVal.join(",");
    } else {
      permitVlanString.value = "";
    }
  },
  { immediate: true }
);

watch(
  () => swVlan.value.untag,
  newVal => {
    if (newVal) {
      untagVlanString.value = newVal.join(",");
    } else {
      untagVlanString.value = "";
    }
  },
  { immediate: true }
);

// 更新 VLAN 数组
const updatePermitVlan = () => {
  if (!permitVlanString.value) {
    swVlan.value.permit = [];
    return;
  }

  swVlan.value.permit = permitVlanString.value
    .split(",")
    .map(item => {
      const num = parseInt(item.trim(), 10);
      return isNaN(num) ? 0 : num;
    })
    .filter(num => num > 0 && num <= 4094);
};

const updateUntagVlan = () => {
  if (!untagVlanString.value) {
    swVlan.value.untag = [];
    return;
  }

  const values = untagVlanString.value
    .split(",")
    .map(item => {
      const num = parseInt(item.trim(), 10);
      return isNaN(num) ? 0 : num;
    })
    .filter(num => num > 0 && num <= 4094);

  swVlan.value.untag = values;
};

// D3图表数据
const chartData = ref<Array<{ date: string; rxByte: number; txByte: number }>>([]);
const chartWidth = ref(640);
const chartHeight = ref(400);

// 异步更新图表数据
const updateChartData = () => {
  // 从deviceWeekStatistic中获取数据
  if (deviceWeekStatistic.value?.statistics) {
    // 转换数据格式为D3需要的格式
    chartData.value = deviceWeekStatistic.value.statistics.map(item => ({
      date: item.date,
      rxByte: item.rxByte,
      txByte: item.txByte
    }));
  }
};

// 在需要显示对话框时调用 showPortDialog
const openPortDialog = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning(t("device.selectPortTip"));
    return;
  }
  showPortDialog(); // 调用外部函数来显示 Dialog

  // 弹出层打开后，加载当前活跃标签页的数据
  await loadDialogTabData(dialogTabActive.value);
};

// 端口配置标签页的加载状态
const portSettingsLoading = ref(false);

// 根据标签页按需加载数据
const loadTabData = async (tabName: string) => {
  if (loadedTabs.value.has(tabName)) {
    console.log(`标签页 ${tabName} 数据已加载，跳过重复加载`);
    return true;
  }

  try {
    console.log(`开始按需加载标签页 ${tabName} 的数据...`);

    switch (tabName) {
      case "first": // 设备信息
        // 设备信息需要状态数据，但只请求 supports 中存在的字段
        const firstTabFields = ["lan", "wan", "led", "workmode", "internet"];
        const availableFields = [];

        // 检查哪些字段在 supports 中存在
        const supports = drawerProps.value.row.supports;
        if (supports) {
          const allSupportedFields = [
            ...(supports.system?.supports || []),
            ...(supports.network?.supports || []),
            ...(supports.wireless?.supports || [])
          ];

          firstTabFields.forEach(field => {
            if (allSupportedFields.includes(field)) {
              availableFields.push(field);
            }
          });
        }

        console.log(`第一个标签页支持的字段:`, availableFields);

        if (availableFields.length > 0) {
          await loadDeviceStatusFields(availableFields);
        } else {
          console.log(`第一个标签页没有可用的状态字段`);
        }
        break;

      case "second": // 网络设置
        // 网络相关数据 - 只加载网络设置标签页实际需要的字段
        const networkFields = ["wan", "lan", "dhcp", "brAp", "brSafe"];
        const networkConfigFields = [];
        const networkStatusFields = [];

        // 根据 supports 过滤可用字段
        const networkSupports = drawerProps.value.row.supports;
        if (networkSupports?.network?.supports) {
          networkFields.forEach(field => {
            if (networkSupports.network.supports.includes(field)) {
              networkConfigFields.push(field);
            }
          });
        }

        // 无线相关字段
        const wirelessFields = ["wifiTime", "radio0", "radio1", "guest"];
        if (networkSupports?.wireless?.supports) {
          wirelessFields.forEach(field => {
            if (networkSupports.wireless.supports.includes(field)) {
              networkConfigFields.push(field);
            }
          });
        }

        console.log(`网络设置标签页需要加载的字段:`, networkConfigFields);
        if (networkConfigFields.length > 0) {
          await loadDeviceConfigFields(networkConfigFields);
        }

        // 加载网络状态数据（如果需要）
        if (networkStatusFields.length > 0) {
          await loadDeviceStatusFields(networkStatusFields);
        }
        break;

      case "third": // 安全设置
        // 安全相关数据 - 只加载安全设置需要的字段
        const securityConfigFields = [];
        const securityStatusFields = [];

        // 检查支持的安全相关字段
        const securitySupports = drawerProps.value.row.supports;
        if (securitySupports?.network?.supports?.includes("brSafe")) {
          securityStatusFields.push("brSafe"); // 安全管理状态
        }

        console.log(`安全设置标签页需要加载的配置字段:`, securityConfigFields);
        console.log(`安全设置标签页需要加载的状态字段:`, securityStatusFields);

        if (securityConfigFields.length > 0) {
          await loadDeviceConfigFields(securityConfigFields);
        }
        if (securityStatusFields.length > 0) {
          await loadDeviceStatusFields(securityStatusFields);
        }
        break;

      case "fourth": // 端口设置 (交换机)
        if (drawerProps.value.row?.deviceType === "switch") {
          // 检查是否已经加载了端口相关数据
          if (!loadedTabs.value.has("port_related")) {
            portSettingsLoading.value = true; // 开始加载，显示Loading
            try {
              await loadPortRelatedData();
              loadedTabs.value.add("port_related");
            } catch (error) {
              console.error("加载端口相关数据失败:", error);
              // 即使失败也标记为已加载，避免重复尝试
              loadedTabs.value.add("port_related");
            } finally {
              portSettingsLoading.value = false; // 结束加载，隐藏Loading
            }
          } else {
            console.log("端口相关数据已加载，跳过重复加载");
            portSettingsLoading.value = false; // 结束加载，隐藏Loading
          }
        }
        break;

      case "fifth": // 系统设置
        // 系统设置相关数据 - 只加载系统设置需要的字段
        const systemConfigFields = [];
        const systemStatusFields = [];

        // 检查支持的系统相关字段
        const systemSupports = drawerProps.value.row.supports;

        // LED 配置
        if (systemSupports?.system?.supports?.includes("led")) {
          systemConfigFields.push("led");
        }

        // 重启配置
        if (systemSupports?.system?.supports?.includes("reboot")) {
          systemConfigFields.push("reboot");
        }

        console.log(`系统设置标签页需要加载的配置字段:`, systemConfigFields);
        console.log(`系统设置标签页需要加载的状态字段:`, systemStatusFields);

        if (systemConfigFields.length > 0) {
          await loadDeviceConfigFields(systemConfigFields);
        }
        if (systemStatusFields.length > 0) {
          await loadDeviceStatusFields(systemStatusFields);
        }
        break;

      case "sixth": // 设备统计
        // 统计数据已在初始化时加载
        break;

      default:
        console.log(`未知标签页: ${tabName}`);
        return false;
    }

    loadedTabs.value.add(tabName);
    console.log(`标签页 ${tabName} 数据加载完成`);
    return true;
  } catch (error) {
    console.error(`标签页 ${tabName} 数据加载失败:`, error);
    // 即使失败也标记为已加载，避免重复尝试
    loadedTabs.value.add(tabName);
    return false;
  }
};

// 初始加载必要的基础数据
// 修复：确保配置(cmd:10)和状态(cmd:4)请求使用相同的字段集合
// 问题：之前配置请求["deviceName", "led"]，状态请求["userList"]，字段不一致导致分段行为不同
// 解决：两者都使用完整的 supports 字段集合，确保包含需要分段的字段(swPoe, swStorm, swPort, swVlan)
const loadInitialData = async () => {
  try {
    console.log("🚀 Vue组件: 开始按需加载基础数据...");

    // 只加载必要的基础字段，避免一次性加载所有数据
    const basicConfigFields = ["deviceName", "led"]; // 基础配置字段
    const basicStatusFields = ["userList"]; // 基础状态字段

    console.log("🔄 开始加载基础设备配置字段:", basicConfigFields);
    try {
      await loadDeviceConfigFields(basicConfigFields);
      console.log("✅ 基础设备配置加载完成");
    } catch (error) {
      console.error("❌ 基础设备配置加载失败:", error);
      return false;
    }

    console.log("🔄 开始加载基础设备状态字段:", basicStatusFields);
    try {
      await loadDeviceStatusFields(basicStatusFields);
      console.log("✅ 基础设备状态加载完成");
    } catch (error) {
      console.error("❌ 基础设备状态加载失败:", error);
      return false;
    }

    // 标记基础数据已加载
    loadedTabs.value.add("basic");
    console.log("✅ Vue组件: 基础数据按需加载完成");
    return true;
  } catch (error) {
    console.error("Failed to load initial data:", error);
    return false;
  }
};

// 兼容原有的合并加载函数
const handleDrawerOpen = async () => {
  return await loadInitialData();
};

// 接收父组件传过来的参数
const acceptParams = async (params: any) => {
  try {
    drawerVisible.value = true;
    drawerProps.value = params;
    // 增加日志输出
    console.log("【WiFi设置检查】设备supports数据: ", JSON.stringify(params.row?.supports, null, 2));

    // 重置编辑状态
    editName.value = false;
    deviceNameChanged.value = false;
    password.value = "";
    activeName.value = "first";
    activeNames.value = [];
    dialogTabActive.value = "first";

    // 重置加密状态相关变量
    encryptRadio0.value = false;
    encryptRadio1.value = false;
    encryptGuest.value = false;
    manualEncryptRadio0.value = false;
    manualEncryptRadio1.value = false;
    manualEncryptGuest.value = false;
    originalKey0.value = "";
    originalKey1.value = "";
    originalKeyGuest.value = "";

    // 重置所有数据到初始状态
    const newDeviceConfig = getDefaultDeviceConfig(params.row?.supports);
    Object.keys(deviceConfig).forEach(key => delete (deviceConfig as any)[key]);
    Object.assign(deviceConfig, newDeviceConfig);

    const newDeviceStatus = getDefaultDeviceStatus(params.row?.supports);
    Object.keys(deviceStatus).forEach(key => delete (deviceStatus as any)[key]);
    Object.assign(deviceStatus, newDeviceStatus);

    if (deviceWeekStatistic) {
      deviceWeekStatistic.value = null;
    }

    clearLoadedTabs(); // 清空之前加载的标签页状态
    if (params?.row?.deviceId) {
      try {
        // 先加载设备统计数据
        await getDeviceStatistics({ deviceId: params.row.deviceId });
        updateChartData();

        // 加载基础数据
        const loadSuccess = await handleDrawerOpen();
        if (!loadSuccess) {
          ElMessage.error(t("common.requestTimeout"));
          return false;
        }

        // 加载当前活跃标签页的数据
        await loadTabData(activeName.value);

        // 设置设备名称 - 确保即使为空也会更新
        deviceConfig.deviceName = params.row?.deviceName !== undefined ? params.row.deviceName : "";

        // 确保配置数据被正确加载
        if (deviceConfig.wireless && drawerProps.value.row?.supports?.wireless) {
          // 深度合并配置数据，保留所有字段包括 key
          if (deviceConfig.wireless.radio0) {
            deviceConfig.wireless.radio0 = { ...deviceConfig.wireless.radio0, ...deviceConfig.wireless.radio0 };
          }
          if (deviceConfig.wireless.radio1) {
            deviceConfig.wireless.radio1 = { ...deviceConfig.wireless.radio1, ...deviceConfig.wireless.radio1 };
          }
          if (deviceConfig.wireless.guest) {
            deviceConfig.wireless.guest = { ...deviceConfig.wireless.guest, ...deviceConfig.wireless.guest };
          }
        }

        // 等待数据加载完成后再初始化加密方式
        await nextTick();

        // 初始化折叠面板展开状态 - 延迟到数据加载完成后
        setTimeout(() => {
          if (activeNames.value.length === 0) {
            initializeActiveNames();
          }
        }, 100);

        // 初始化加密方式和原始key值
        if (deviceConfig.wireless?.radio0 && drawerProps.value.row?.supports?.wireless) {
          const hasKey = !!deviceConfig.wireless.radio0.key;
          encryptRadio0.value = hasKey;
          if (hasKey) {
            originalKey0.value = deviceConfig.wireless.radio0.key;
          }
        }
        if (deviceConfig.wireless?.radio1 && drawerProps.value.row?.supports?.wireless) {
          const hasKey = !!deviceConfig.wireless.radio1.key;
          encryptRadio1.value = hasKey;
          if (hasKey) {
            originalKey1.value = deviceConfig.wireless.radio1.key;
          }
        }
        if (deviceConfig.wireless?.guest && drawerProps.value.row?.supports?.wireless) {
          const hasKey = !!deviceConfig.wireless.guest.key;
          encryptGuest.value = hasKey;
          if (hasKey) {
            originalKeyGuest.value = deviceConfig.wireless.guest.key;
          }
        }

        // 保存原始数据的深拷贝
        originalData.value = cloneDeep(deviceConfig);

        return true;
      } catch (error) {
        console.error("Failed to load device data:", error);
        ElMessage.error(t("common.requestTimeout"));
        return false;
      }
    }
    return true;
  } catch (error) {
    console.error("Failed to initialize drawer:", error);
    ElMessage.error(t("common.requestTimeout"));
    return false;
  }
};

// 通用的提交配置函数
const submitConfig = async (configData: any) => {
  try {
    // 提交前移除 reSysPassword 字段
    if (configData.system && configData.system.reSysPassword !== undefined) {
      delete configData.system.reSysPassword;
    }
    // 组装参数，password 作为旧密码放最外层
    const params: Project.ReqConfigParams = {
      cmd: 6,
      deviceId: drawerProps.value.row.deviceId,
      userId: useUserStore().userInfo.userId,
      data: {
        ...configData,
        group_id: (drawerProps.value.row as any).groupId || (drawerProps.value.row as any).group_id
      }
    };
    // 如果有密码变更，password 需要提交
    if (password.value) {
      params.password = password.value;
    }

    console.log("提交数据:", stringifyWithoutEmpty(params));
    const response = await pushDeviceConfigJwe(params);
    console.log("API 响应数据:", response);

    if (!response || response.code !== "200") {
      throw new Error(response?.msg || t("common.submitFailed"));
    }

    ElMessage.success(t("common.submitSuccess"));

    // 提交成功后重新加载数据 - 串行执行避免并发请求
    await loadDeviceConfig();
    await loadDeviceStatus();

    // 如果是端口相关的配置提交，需要重新加载端口数据以同步显示
    if (
      configData?.system?.swPort ||
      configData?.system?.swPoe ||
      configData?.system?.swStorm ||
      configData?.system?.swQos ||
      configData?.network?.swVlan ||
      configData?.network?.swIsolate
    ) {
      // 重新加载端口相关数据
      try {
        await loadPortRelatedData();
        console.log("端口配置提交成功，已同步更新端口数据");
      } catch (error) {
        console.error("重新加载端口数据失败:", error);
      }
    }

    // 重新初始化加密状态
    if (deviceConfig.wireless?.radio0) {
      const hasKey = !!deviceConfig.wireless.radio0.key;
      encryptRadio0.value = hasKey;
      manualEncryptRadio0.value = false;
      if (hasKey) {
        originalKey0.value = deviceConfig.wireless.radio0.key;
      }
    }
    if (deviceConfig.wireless?.radio1) {
      const hasKey = !!deviceConfig.wireless.radio1.key;
      encryptRadio1.value = hasKey;
      manualEncryptRadio1.value = false;
      if (hasKey) {
        originalKey1.value = deviceConfig.wireless.radio1.key;
      }
    }
    if (deviceConfig.wireless?.guest) {
      const hasKey = !!deviceConfig.wireless.guest.key;
      encryptGuest.value = hasKey;
      manualEncryptGuest.value = false;
      if (hasKey) {
        originalKeyGuest.value = deviceConfig.wireless.guest.key;
      }
    }

    // 更新原始数据
    originalData.value = cloneDeep(deviceConfig);

    // 更新父组件的表格数据
    if (drawerProps.value.getTableList) {
      await drawerProps.value.getTableList();
    }

    // 关闭抽屉
    drawerVisible.value = false;
    return true;
  } catch (error) {
    console.error("Failed to submit config:", error);
    ElMessage.error(error instanceof Error ? error.message : t("common.submitFailed"));
    return false;
  }
};

// 修改描述的函数
const modifyDescribe = async (row: any) => {
  console.log("修改描述:", stringifyWithoutEmpty(row));

  // 验证描述长度（最大32个字节）
  if (row.describe && new Blob([row.describe]).size > 32) {
    ElMessage.error("端口描述最大长度为32个字节");
    throw new Error("端口描述长度超限");
  }

  // 构造提交数据，只包含必要的字段
  const portData = {
    name: row.name,
    port: row.port,
    describe: row.describe || row.name, // 如果描述为空，使用端口名称
    portenable: row.portenable,
    autoneg: row.autoneg,
    extend: row.extend,
    speed_duplex: row.speed_duplex,
    flwctrl: row.flwctrl
  };

  console.log("提交端口描述数据:", portData);
  await submitConfig({ system: { swPort: [portData] } });
};

// 提交数据（新增/编辑）
const ruleFormRef = ref<FormInstance>();
const qosForm = ref<FormInstance>(); // 添加qosForm引用
const vlanForm = ref<FormInstance>(); // 添加vlanForm引用
const portForm = ref<FormInstance>(); // 添加portForm引用
const poeForm = ref<FormInstance>(); // 添加poeForm引用
const stormForm = ref<FormInstance>(); // 添加stormForm引用
const isolationForm = ref<FormInstance>(); // 添加isolationForm引用
const handleSubmit = (isDialog = false) => {
  // 如果是对话框提交，使用对应的表单引用进行验证
  let formRef: typeof ruleFormRef;
  if (isDialog) {
    if (dialogTabActive.value === "first") {
      formRef = portForm; // 端口设置
    } else if (dialogTabActive.value === "second") {
      formRef = poeForm; // POE设置
    } else if (dialogTabActive.value === "third") {
      formRef = vlanForm; // VLAN设置
    } else if (dialogTabActive.value === "fourth") {
      formRef = stormForm; // 风暴抑制设置
    } else if (dialogTabActive.value === "fifth") {
      formRef = isolationForm; // 隔离控制设置
    } else if (dialogTabActive.value === "sixth") {
      formRef = qosForm; // QoS设置
    } else {
      formRef = ruleFormRef;
    }
  } else {
    formRef = ruleFormRef;
  }

  if (!formRef.value) {
    // 如果表单引用不存在，直接继续处理
    handleSubmitAfterValidation(isDialog);
    return;
  }

  formRef.value
    .validate()
    .then(async () => {
      handleSubmitAfterValidation(isDialog);
    })
    .catch(() => {
      // 验证失败
      console.log("表单验证失败");
    });
};

// 提取验证后的提交逻辑到单独的函数
const handleSubmitAfterValidation = async (isDialog = false) => {
  try {
    // SSID 必填校验（修正版）
    const hasWirelessSupport = drawerProps.value.row?.supports?.wireless;
    const needCheckRadio0 =
      hasWirelessSupport && deviceConfig.wireless?.radio0 && Object.keys(deviceConfig.wireless.radio0).length > 0;
    const needCheckRadio1 =
      hasWirelessSupport && deviceConfig.wireless?.radio1 && Object.keys(deviceConfig.wireless.radio1).length > 0;
    const needCheckGuest =
      hasWirelessSupport && deviceConfig.wireless?.guest && Object.keys(deviceConfig.wireless.guest).length > 0;
    // 只有当设备支持网桥AP功能时才检查brAp的SSID
    const needCheckBrAp =
      drawerProps.value.row?.supports?.network?.supports.includes("brAp") &&
      deviceConfig.network?.brAp &&
      Object.keys(deviceConfig.network.brAp).length > 0;

    if (
      (needCheckRadio0 && deviceConfig.wireless.radio0.disabled === 0 && !deviceConfig.wireless.radio0.ssid) ||
      (needCheckRadio1 && deviceConfig.wireless.radio1.disabled === 0 && !deviceConfig.wireless.radio1.ssid) ||
      (needCheckGuest && deviceConfig.wireless.guest.disabled === 0 && !deviceConfig.wireless.guest.ssid) ||
      (needCheckBrAp && !deviceConfig.network.brAp.ssid)
    ) {
      ElMessage.warning(t("device.ssidRequired") || "SSID不能为空");
      return;
    }
    let configData: any = {};

    // LED定时模式校验
    if (deviceConfig.system?.led?.mode === "timer" && (!deviceConfig.system.led.beginTime || !deviceConfig.system.led.endTime)) {
      ElMessage.warning(t("device.ledTimeRequiredTip") || "请填写LED定时的开始和结束时间");
      return;
    }

    // 获取配置差异
    const diff = getDiff(originalData.value, deviceConfig);

    if (isDialog) {
      // 对话框提交逻辑 - 不需要检查 diff，因为 hasDialogConfigChanged 已经确保了有变化
      console.log("handleSubmit: 对话框提交");
    } else {
      // 主抽屉提交逻辑 - 需要检查 diff
      if (!diff) {
        ElMessage.warning(t("common.noConfigChanges"));
        return;
      }
    }

    if (isDialog) {
      // 对话框提交逻辑
      let configData = null;

      // 如果没有选中端口，直接使用当前标签页的数据
      if (!selectedRows.value || selectedRows.value.length === 0) {
        console.log("handleSubmit: 没有选中端口，使用当前标签页数据");
        switch (dialogTabActive.value) {
          case "first":
            configData = { system: { swPort: [swPort.value] } };
            break;
          case "second":
            configData = { system: { swPoe: [swPoe.value] } };
            break;
          case "third":
            configData = { network: { swVlan: [swVlan.value] } };
            break;
          case "fourth":
            configData = { system: { swStorm: [swStorm.value] } };
            break;
          case "fifth":
            configData = { network: { swIsolate: [swIsolate.value] } };
            break;
          case "sixth":
            configData = { system: { swQos: [swQos.value] } };
            break;
        }
      } else {
        // 有选中端口时，使用 generatePortData
        switch (dialogTabActive.value) {
          case "first":
            configData = { system: { swPort: generatePortData(swPort.value) } };
            break;
          case "second":
            configData = { system: { swPoe: generatePortData(swPoe.value) } };
            break;
          case "third":
            configData = { network: { swVlan: generatePortData(swVlan.value) } };
            break;
          case "fourth":
            configData = { system: { swStorm: generatePortData(swStorm.value) } };
            break;
          case "fifth":
            configData = { network: { swIsolate: generatePortData(swIsolate.value) } };
            break;
          case "sixth":
            configData = { system: { swQos: generatePortData(swQos.value) } };
            break;
        }
      }

      // 提交对话框配置
      console.log("handleSubmit: 准备提交的配置数据:", configData);
      const success = await submitConfig(configData);
      if (success) {
        portDialogVisible.value = false;
      }
      return;
    } else {
      // 主抽屉提交逻辑
      // 先拷贝 diff，后续可能补字段
      configData = JSON.parse(JSON.stringify(diff));

      // 检查 network.dhcp.dnsenabled/dns1/dns2 是否有变化
      // 先确保 configData.network 和 configData.network.dhcp 存在
      if (!configData.network) configData.network = {};
      if (!configData.network.dhcp) configData.network.dhcp = {};
      if (
        diff?.network?.dhcp &&
        ("dnsenabled" in diff.network.dhcp || "dns1" in diff.network.dhcp || "dns2" in diff.network.dhcp)
      ) {
        // 补全没变的字段
        ["dnsenabled", "dns1", "dns2"].forEach(key => {
          if (!(key in configData.network.dhcp)) {
            configData.network.dhcp[key] = deviceConfig.network.dhcp[key];
          }
        });
      }
      // 保证 start 和 limit 为数字类型
      if (configData.network?.dhcp) {
        if (configData.network.dhcp.start !== undefined) {
          configData.network.dhcp.start = Number(configData.network.dhcp.start);
        }
        if (configData.network.dhcp.limit !== undefined) {
          configData.network.dhcp.limit = Number(configData.network.dhcp.limit);
        }
      }
      // WIFI定时开关相关：如果 enabled/week/beginTime/endTime 任意一个有变化，则三个字段都一起提交
      if (diff?.wireless?.wifiTime) {
        const wifiTime = (deviceConfig.wireless?.wifiTime as any) || {};
        configData.wireless = configData.wireless || {};
        configData.wireless.wifiTime = {
          enabled: wifiTime.enabled,
          week: wifiTime.week,
          beginTime: wifiTime.beginTime,
          endTime: wifiTime.endTime
        };
      }
      // 只有 LED 配置有变化且为定时模式时，才补充 beginTime 和 endTime
      if (diff?.system?.led && deviceConfig.system?.led?.mode === "timer") {
        configData.system = configData.system || {};
        configData.system.led = configData.system.led || {};
        configData.system.led.beginTime = deviceConfig.system.led.beginTime;
        configData.system.led.endTime = deviceConfig.system.led.endTime;
      }
    }

    const success = await submitConfig(configData);
    if (success) {
      if (isDialog) {
        portDialogVisible.value = false;
      }
      // 关闭抽屉并清理数据
      drawerVisible.value = false;
      onDrawerClose();

      // 如果需要更新父组件表格数据
      if (drawerProps.value.getTableList) {
        await drawerProps.value.getTableList();
      }
    }
  } catch (error) {
    console.log(error);
    ElMessage.error({ message: error?.message || t("common.requestTimeout"), duration: 3000 });
    return false;
  }
};

// 存储设备类型列表
const deviceTypes = inject("deviceTypes", ref<Project.ResConfigList[]>([]));

// 将rules改为computed属性，动态生成SSID校验规则
const rules = computed(() => {
  const baseRules: any = {
    deviceId: [{ required: false, message: t("device.deviceIdPlaceholder") }],
    deviceModel: [{ required: false, message: t("device.modelPlaceholder") }],
    deviceType: [{ required: false, message: t("device.typePlaceholder") }],
    mac: [{ required: false, message: t("device.macPlaceholder") }],
    ip: [{ required: false, message: t("device.ipPlaceholder") }],
    // 起始IP 必填且正整数
    "network.dhcp.start": [
      {
        validator: (rule, value, callback) => {
          const val = value === undefined || value === null ? "" : value.toString().trim();
          if (val === "") {
            callback(new Error(t("common.startIpRequired")));
          } else if (!/^[1-9]\d*$/.test(val)) {
            callback(new Error(t("device.startIpPositiveInt")));
          } else if (Number(val) < 2 || Number(val) > 254) {
            callback(new Error(t("common.startIpRange")));
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ],
    // 分配IP数量 必填且正整数
    "network.dhcp.limit": [
      {
        validator: (rule, value, callback) => {
          const val = value === undefined || value === null ? "" : value.toString().trim();
          if (val === "") {
            callback(new Error(t("device.limitRequired")));
          } else if (!/^[1-9]\d*$/.test(val)) {
            callback(new Error(t("device.limitPositiveInt")));
          } else if (Number(val) > 2000) {
            callback(new Error(t("common.limitMax2000")));
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ],
    // LAN IP地址 必填且为合法IPv4
    "network.lan.ipaddr": [
      {
        required: true,
        message: t("device.lanIpTip"),
        trigger: "blur"
      },
      {
        validator: (rule, value, callback) => {
          // 简单IPv4校验
          const ipv4 = /^(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)){3}$/;
          if (value && !ipv4.test(value)) {
            callback(new Error(t("device.invalidIpFormat")));
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ],
    // LAN子网掩码 必填且为合法子网掩码
    "network.lan.netmask": [
      {
        required: true,
        message: t("device.lanNetmaskTip"),
        trigger: "blur"
      },
      {
        validator: (rule, value, callback) => {
          // 严格子网掩码校验
          const mask =
            /^(255\.(255|254|252|248|240|224|192|128|0)\.(255|254|252|248|240|224|192|128|0)\.(255|254|252|248|240|224|192|128|0))$/;
          if (value && !mask.test(value)) {
            callback(new Error(t("device.invalidMaskFormat")));
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ],
    // DNS1 必须为合法IPv4（自定义模式下）
    "network.dhcp.dns1": [
      {
        required: true,
        message: t("device.dns1Placeholder"),
        trigger: "blur"
      },
      {
        validator: (rule, value, callback) => {
          // 只有自定义模式下才校验
          if (deviceConfig.network?.dhcp?.dnsenabled === 1) {
            const ipv4 = /^(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)){3}$/;
            if (!value || !ipv4.test(value)) {
              callback(new Error(t("device.invalidIpFormat")));
              return;
            }
          }
          callback();
        },
        trigger: "blur"
      }
    ],
    // DNS2 必须为合法IPv4（自定义模式下）
    "network.dhcp.dns2": [
      {
        validator: (rule, value, callback) => {
          if (deviceConfig.network?.dhcp?.dnsenabled === 1 && value) {
            const ipv4 = /^(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)){3}$/;
            if (!ipv4.test(value)) {
              callback(new Error(t("device.invalidIpFormat")));
              return;
            }
          }
          callback();
        },
        trigger: "blur"
      }
    ],
    // WAN PPPoE 用户名校验
    "network.wan.0.username": [
      {
        validator: (rule, value, callback) => {
          if (deviceConfig.network?.wan?.[0]?.proto === "pppoe") {
            if (!value) {
              callback(new Error(t("device.wanUsernamePlaceholder")));
              return;
            }
          }
          callback();
        },
        trigger: "blur"
      }
    ],
    // WAN PPPoE 密码校验
    "network.wan.0.password": [
      {
        validator: (rule, value, callback) => {
          if (deviceConfig.network?.wan?.[0]?.proto === "pppoe") {
            if (!value) {
              callback(new Error(t("device.wanPasswordPlaceholder")));
              return;
            }
          }
          callback();
        },
        trigger: "blur"
      }
    ],
    // WAN Static IP校验
    "network.wan.0.ipaddr": [
      {
        validator: (rule, value, callback) => {
          if (deviceConfig.network?.wan?.[0]?.proto === "static") {
            if (!value) {
              callback(new Error(t("device.wanIpPlaceholder")));
              return;
            }
            const ipv4 = /^(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)){3}$/;
            if (!ipv4.test(value)) {
              callback(new Error(t("device.invalidIpFormat")));
              return;
            }
          }
          callback();
        },
        trigger: "blur"
      }
    ],
    // WAN Static 子网掩码校验
    "network.wan.0.netmask": [
      {
        validator: (rule, value, callback) => {
          if (deviceConfig.network?.wan?.[0]?.proto === "static") {
            if (!value) {
              callback(new Error(t("device.wanNetmaskPlaceholder")));
              return;
            }
            const mask =
              /^(255\.(255|254|252|248|240|224|192|128|0)\.(255|254|252|248|240|224|192|128|0)\.(255|254|252|248|240|224|192|128|0))$/;
            if (!mask.test(value)) {
              callback(new Error(t("device.invalidMaskFormat")));
              return;
            }
          }
          callback();
        },
        trigger: "blur"
      }
    ],
    // WAN Static 网关校验
    "network.wan.0.gawa": [
      {
        validator: (rule, value, callback) => {
          if (deviceConfig.network?.wan?.[0]?.proto === "static") {
            if (!value) {
              callback(new Error(t("device.wanGatewayPlaceholder")));
              return;
            }
            const ipv4 = /^(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)){3}$/;
            if (!ipv4.test(value)) {
              callback(new Error(t("device.invalidIpFormat")));
              return;
            }
          }
          callback();
        },
        trigger: "blur"
      }
    ],
    // WAN Static DNS1校验
    "network.wan.0.dns1": [
      {
        validator: (rule, value, callback) => {
          if (deviceConfig.network?.wan?.[0]?.proto === "static") {
            if (!value) {
              callback(new Error(t("device.dns1Placeholder")));
              return;
            }
            const ipv4 = /^(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)){3}$/;
            if (!ipv4.test(value)) {
              callback(new Error(t("device.invalidIpFormat")));
              return;
            }
          }
          callback();
        },
        trigger: "blur"
      }
    ],
    // WAN Static DNS2校验（可选）
    "network.wan.0.dns2": [
      {
        validator: (rule, value, callback) => {
          if (deviceConfig.network?.wan?.[0]?.proto === "static" && value) {
            const ipv4 = /^(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)){3}$/;
            if (!ipv4.test(value)) {
              callback(new Error(t("device.invalidIpFormat")));
              return;
            }
          }
          callback();
        },
        trigger: "blur"
      }
    ],
    deviceName: [
      {
        validator: (rule, value, callback) => {
          // 只在编辑名称模式下进行验证
          if (editName.value) {
            if (!value) {
              callback(new Error(t("device.deviceNamePlaceholder")));
            } else if (value.length > 32) {
              callback(new Error(t("device.nameMaxLength")));
            } else {
              callback();
            }
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ]
  };
  // 动态挂载SSID相关校验
  if (deviceConfig.wireless?.radio0) {
    baseRules["wireless.radio0.ssid"] = [
      {
        validator: (rule, value, callback) => {
          if (deviceConfig.wireless?.radio0?.disabled === 0 && !value) {
            callback(new Error(t("device.ssidRequired")));
          } else if (value && value.length > 32) {
            callback(new Error(t("device.ssidMaxLength")));
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ];
    // 无线密码长度校验
    baseRules["wireless.radio0.key"] = [
      {
        validator: (rule, value, callback) => {
          if (encryptRadio0.value) {
            if (!value) {
              callback(new Error(t("device.passwordRequired")));
              return;
            }
            if (value.length < 8 || value.length > 64) {
              callback(new Error(t("device.key") + t("device.keyLengthTip")));
              return;
            }
            if (!/^[\w\.-_~`!@#$%^&*()+\?><,\|\\\[\]\/;':" ]+$/.test(value)) {
              callback(new Error(t("device.keyRuleAscii")));
              return;
            }
          }
          callback();
        },
        trigger: ["blur", "change"]
      }
    ];
  }
  if (deviceConfig.wireless?.radio1) {
    baseRules["wireless.radio1.ssid"] = [
      {
        validator: (rule, value, callback) => {
          if (deviceConfig.wireless?.radio1?.disabled === 0 && !value) {
            callback(new Error(t("device.ssidRequired")));
          } else if (value && value.length > 32) {
            callback(new Error(t("device.ssidMaxLength")));
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ];
    // 无线密码长度校验
    baseRules["wireless.radio1.key"] = [
      {
        validator: (rule, value, callback) => {
          if (encryptRadio1.value) {
            if (!value) {
              callback(new Error(t("device.passwordRequired")));
              return;
            }
            if (value.length < 8 || value.length > 64) {
              callback(new Error(t("device.keyLengthTip")));
              return;
            }
            if (!/^[\w\.-_~`!@#$%^&*()+\?><,\|\\\[\]\/;':" ]+$/.test(value)) {
              callback(new Error(t("device.keyRuleAscii")));
              return;
            }
          }
          callback();
        },
        trigger: ["blur", "change"]
      }
    ];
  }
  if (deviceConfig.wireless?.guest) {
    baseRules["wireless.guest.ssid"] = [
      {
        validator: (rule, value, callback) => {
          if (deviceConfig.wireless?.guest?.disabled === 0 && !value) {
            callback(new Error(t("device.ssidRequired")));
          } else if (value && value.length > 32) {
            callback(new Error(t("device.ssidMaxLength")));
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ];
    // 无线密码长度校验
    baseRules["wireless.guest.key"] = [
      {
        validator: (rule, value, callback) => {
          if (encryptGuest.value) {
            if (!value) {
              callback(new Error(t("device.passwordRequired")));
              return;
            }
            if (value.length < 8 || value.length > 64) {
              callback(new Error(t("device.keyLengthTip")));
              return;
            }
            if (!/^[\w\.-_~`!@#$%^&*()+\?><,\|\\\[\]\/;':" ]+$/.test(value)) {
              callback(new Error(t("device.keyRuleAscii")));
              return;
            }
          }
          callback();
        },
        trigger: ["blur", "change"]
      }
    ];
    baseRules["wireless.guest.rate"] = [
      {
        validator: (rule, value, callback) => {
          if (value === null || value === undefined || value === "") {
            callback();
            return;
          }
          const num = Number(value);
          if (isNaN(num) || !Number.isInteger(num) || num < 0 || num > 1000) {
            callback(new Error(t("device.rateRangeTip")));
          } else {
            callback();
          }
        },
        trigger: ["change", "blur"]
      }
    ];
  }
  if (deviceConfig.network?.brAp) {
    baseRules["network.brAp.ssid"] = [
      {
        validator: (rule, value, callback) => {
          if (deviceConfig.network?.brAp && !deviceConfig.network.brAp.ssid) {
            callback(new Error(t("device.ssidRequired")));
          } else if (value && value.length > 32) {
            callback(new Error(t("device.ssidMaxLength")));
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ];
    // 网桥WIFI密码长度校验
    baseRules["network.brAp.key"] = [
      {
        validator: (rule, value, callback) => {
          if (!value) {
            callback(new Error(t("device.passwordRequired")));
            return;
          }
          if (value.length < 8 || value.length > 64) {
            callback(new Error(t("device.keyLengthTip")));
            return;
          }
          if (!/^[\w\.-_~`!@#$%^&*()+\?><,\|\\\[\]\/;':" ]+$/.test(value)) {
            callback(new Error(t("device.keyRuleAscii")));
            return;
          }
          callback();
        },
        trigger: ["blur", "change"]
      }
    ];
  }
  // 新增密码一致性和必填校验
  baseRules["system.reSysPassword"] = [
    {
      validator: (rule, value, callback) => {
        // 如果 password 有值，sysPassword 和 reSysPassword 必填且一致
        if (password.value) {
          if (!deviceConfig.system?.sysPassword) {
            callback(new Error(t("device.newPassword") + t("common.pleaseInput")));
            return;
          }
          if (!value) {
            callback(new Error(t("device.confirmPassword") + t("common.pleaseInput")));
            return;
          }
          if (deviceConfig.system?.sysPassword !== value) {
            callback(new Error(t("user.passwordNotMatch") || "两次输入的密码不一致"));
            return;
          }
        }
        callback();
      },
      trigger: "blur"
    }
  ];
  baseRules["system.sysPassword"] = [
    {
      validator: (rule, value, callback) => {
        if (password.value) {
          if (!value) {
            callback(new Error(t("device.newPassword") + t("common.pleaseInput")));
            return;
          }
          if (!deviceConfig.system?.reSysPassword) {
            callback(new Error(t("device.confirmPassword") + t("common.pleaseInput")));
            return;
          }
          if (value !== deviceConfig.system?.reSysPassword) {
            callback(new Error(t("user.passwordNotMatch") || "两次输入的密码不一致"));
            return;
          }
        }
        callback();
      },
      trigger: "blur"
    }
  ];
  // 新增：如果新密码和确认密码有任意一个不为空，则旧密码也必须填写
  baseRules["password"] = [
    {
      validator: (rule, value, callback) => {
        if ((deviceConfig.system?.sysPassword || deviceConfig.system?.reSysPassword) && !password.value) {
          callback(new Error(t("common.pleaseInput") + t("device.oldPassword")));
          return;
        }
        callback();
      },
      trigger: ["blur", "change"]
    }
  ];

  // WiFi定时开关时间验证
  baseRules["wireless.wifiTime.endTime"] = [
    {
      validator: (rule, value, callback) => {
        if (deviceConfig.wireless?.wifiTime?.enabled === 1) {
          if (!value || value === "") {
            callback(new Error(t("device.startupTime") + t("common.pleaseInput")));
            return;
          }
        }
        callback();
      },
      trigger: ["blur", "change"]
    }
  ];

  baseRules["wireless.wifiTime.beginTime"] = [
    {
      validator: (rule, value, callback) => {
        if (deviceConfig.wireless?.wifiTime?.enabled === 1) {
          if (!value || value === "") {
            callback(new Error(t("device.closingTime") + t("common.pleaseInput")));
            return;
          }
        }
        callback();
      },
      trigger: ["blur", "change"]
    }
  ];

  // WiFi定时时间验证
  baseRules["wireless.wifiTime.week"] = [
    {
      validator: (rule, value, callback) => {
        if (deviceConfig.wireless?.wifiTime?.enabled === 1 && (value === null || value === undefined || value === "")) {
          callback(new Error(t("device.timingTime") + t("common.pleaseInput")));
          return;
        }
        callback();
      },
      trigger: ["blur", "change"]
    }
  ];

  return baseRules;
});

// VLAN表单验证规则
const vlanRules = computed(() => {
  const { t } = useI18n();
  return {
    vlanmode: [
      {
        required: true,
        message: t("device.portVlan") + t("common.pleaseSelect"),
        trigger: "change"
      }
    ],
    pvid: [
      {
        required: true,
        message: "VLAN ID" + t("common.pleaseInput"),
        trigger: "blur"
      },
      {
        validator: (rule: any, value: any, callback: any) => {
          if (value !== null && value !== undefined) {
            const num = Number(value);
            if (isNaN(num) || !Number.isInteger(num) || num < 1 || num > 4094) {
              callback(new Error("VLAN ID必须是1-4094之间的整数"));
            } else {
              callback();
            }
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ],
    permit: [
      {
        validator: (rule: any, value: any, callback: any) => {
          // 只在trunk或hybrid模式下验证
          if (swVlan.value.vlanmode === 1 || swVlan.value.vlanmode === 2) {
            if (!permitVlanString.value || permitVlanString.value.trim() === "") {
              callback(new Error("Permit VLAN" + t("common.pleaseInput")));
            } else {
              // 验证VLAN ID格式
              const vlanIds = permitVlanString.value.split(",").map(id => id.trim());
              for (const id of vlanIds) {
                const num = parseInt(id, 10);
                if (isNaN(num) || num < 1 || num > 4094) {
                  callback(new Error("Permit VLAN ID必须是1-4094之间的整数，多个ID用逗号分隔"));
                  return;
                }
              }
              callback();
            }
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ],
    untag: [
      {
        validator: (rule: any, value: any, callback: any) => {
          // 只在hybrid模式下验证
          if (swVlan.value.vlanmode === 2) {
            if (untagVlanString.value && untagVlanString.value.trim() !== "") {
              // 验证VLAN ID格式
              const vlanIds = untagVlanString.value.split(",").map(id => id.trim());
              for (const id of vlanIds) {
                const num = parseInt(id, 10);
                if (isNaN(num) || num < 1 || num > 4094) {
                  callback(new Error("Untag VLAN ID必须是1-4094之间的整数，多个ID用逗号分隔"));
                  return;
                }
              }
            }
            callback();
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ]
  };
});

// chartRef removed - using D3 instead
// 监听 deviceWeekStatistic 数据变化并更新报表配置
watch(
  () => deviceWeekStatistic.value,
  newVal => {
    if (newVal && newVal.statistics) {
      updateChartData();
    }
  }
);

// 计算属性：检测配置是否有变化
const hasConfigChanged = computed(() => {
  // 如果原始数据不存在或者当前配置不存在，则返回 false
  if (!originalData.value || !deviceConfig) return false;

  try {
    // 深度比较当前配置与原始配置
    // 使用 JSON 字符串比较，忽略空格和缩进的差异
    const currentConfig = JSON.stringify(deviceConfig, null, 0);
    const originalConfig = JSON.stringify(originalData.value, null, 0);

    // 比较字符串是否相等
    const hasChanged = currentConfig !== originalConfig;

    // 添加日志以便于调试
    const diff = getDiff(originalData.value, deviceConfig);
    console.log("【hasConfigChanged】originalData:", originalData.value);
    console.log("【hasConfigChanged】deviceConfig:", deviceConfig);
    console.log("【hasConfigChanged】diff:", diff);
    console.log("【hasConfigChanged】hasChanged:", hasChanged);

    return hasChanged;
  } catch (error) {
    console.error("比较配置时出错:", error);
    return false; // 出错时返回 false，确保提交按钮不可用
  }
});

// 弹出层标签页按需加载数据
const loadDialogTabData = async (tabName: string) => {
  const tabKey = `dialog_${tabName}`;
  if (loadedTabs.value.has(tabKey)) {
    console.log(`弹出层标签页 ${tabName} 数据已加载，跳过重复加载`);
    return true;
  }

  try {
    console.log(`开始按需加载弹出层标签页 ${tabName} 的数据...`);

    switch (tabName) {
      case "first": // 端口设置
        // 端口设置数据通常在端口相关数据中已包含
        if (!loadedTabs.value.has("port_related")) {
          try {
            await loadPortRelatedData();
            loadedTabs.value.add("port_related");
          } catch (error) {
            console.error("加载端口相关数据失败:", error);
            loadedTabs.value.add("port_related");
          }
        }
        break;

      case "second": // POE设置
        // 检查是否已经加载了 POE 数据
        if (!deviceConfig.system?.swPoe || deviceConfig.system.swPoe.length === 0) {
          try {
            await loadDeviceConfigFields(["swPoe"]);
          } catch (error) {
            console.error("加载 POE 数据失败:", error);
          }
        } else {
          console.log("POE 数据已存在，跳过加载");
        }
        break;

      case "third": // VLAN配置
        // 检查是否已经加载了 VLAN 数据
        if (!deviceConfig.network?.swVlan || deviceConfig.network.swVlan.length === 0) {
          try {
            await loadDeviceConfigFields(["swVlan"]);
          } catch (error) {
            console.error("加载 VLAN 数据失败:", error);
          }
        } else {
          console.log("VLAN 数据已存在，跳过加载");
        }
        break;

      case "fourth": // 风暴抑制
        // 检查是否已经加载了风暴抑制数据
        if (!deviceConfig.system?.swStorm || deviceConfig.system.swStorm.length === 0) {
          try {
            await loadDeviceConfigFields(["swStorm"]);
          } catch (error) {
            console.error("加载风暴抑制数据失败:", error);
          }
        } else {
          console.log("风暴抑制数据已存在，跳过加载");
        }
        break;

      case "fifth": // 隔离控制
        // 检查是否已经加载了隔离控制数据
        if (!deviceConfig.network?.swIsolate || deviceConfig.network.swIsolate.length === 0) {
          try {
            await loadDeviceConfigFields(["swIsolate"]);
          } catch (error) {
            console.error("加载隔离控制数据失败:", error);
          }
        } else {
          console.log("隔离控制数据已存在，跳过加载");
        }
        break;

      case "sixth": // QoS设置
        // 检查是否已经加载了 QoS 数据
        if (!deviceConfig.system?.swQos || deviceConfig.system.swQos.length === 0) {
          try {
            await loadDeviceConfigFields(["swQos"]);
          } catch (error) {
            console.error("加载 QoS 数据失败:", error);
          }
        } else {
          console.log("QoS 数据已存在，跳过加载");
        }
        break;

      default:
        console.log(`未知弹出层标签页: ${tabName}`);
        return false;
    }

    loadedTabs.value.add(tabKey);
    console.log(`弹出层标签页 ${tabName} 数据加载完成`);
    return true;
  } catch (error) {
    console.error(`弹出层标签页 ${tabName} 数据加载失败:`, error);
    return false;
  }
};

// 监听主抽屉标签页切换，按需加载数据
watch(activeName, async (newTab, oldTab) => {
  if (newTab !== oldTab && drawerVisible.value) {
    console.log(`主抽屉标签页从 ${oldTab} 切换到 ${newTab}`);
    await loadTabData(newTab);
  }
});

// 监听弹出层标签页切换，按需加载数据
watch(dialogTabActive, async (newTab, oldTab) => {
  if (newTab !== oldTab && portDialogVisible.value) {
    console.log(`弹出层标签页从 ${oldTab} 切换到 ${newTab}`);
    await loadDialogTabData(newTab);
  }
});

// 监听弹出层显示状态变化
watch(portDialogVisible, newVal => {
  if (!newVal) {
    // 弹出层关闭时，清理弹出层相关的标签页缓存
    const dialogTabKeys = Array.from(loadedTabs.value).filter(key => key.startsWith("dialog_"));
    dialogTabKeys.forEach(key => loadedTabs.value.delete(key));
    console.log("弹出层关闭，已清理弹出层标签页缓存");
  }
});

// 监听抽屉关闭事件，清除原始数据
watch(drawerVisible, newVal => {
  if (!newVal) {
    // 当抽屉关闭时，清除原始数据和加密状态
    originalData.value = null;
    Object.assign(deviceConfig, getDefaultDeviceConfig(drawerProps.value.row?.supports));
    Object.assign(deviceStatus, getDefaultDeviceStatus(drawerProps.value.row?.supports));
    if (deviceWeekStatistic) {
      deviceWeekStatistic.value = null;
    }
    guestRateOptions.value = [...baseGuestRateOptions];

    // 重置加密状态
    encryptRadio0.value = false;
    encryptRadio1.value = false;
    encryptGuest.value = false;
    manualEncryptRadio0.value = false;
    manualEncryptRadio1.value = false;
    manualEncryptGuest.value = false;
    originalKey0.value = "";
    originalKey1.value = "";
    originalKeyGuest.value = "";

    // 重置已加载的标签页状态
    loadedTabs.value.clear();
  }
});

watch(
  () => deviceConfig.wireless?.guest?.rate,
  (newRate: number | null | undefined, oldRate: number | null | undefined) => {
    // 移除旧的自定义选项
    if (oldRate !== null && oldRate !== undefined && !baseGuestRateOptions.some(opt => opt.value === oldRate)) {
      const index = guestRateOptions.value.findIndex(opt => opt.value === oldRate);
      if (index !== -1) {
        guestRateOptions.value.splice(index, 1);
      }
    }
    // 添加新的自定义选项
    if (newRate !== null && newRate !== undefined && !baseGuestRateOptions.some(opt => opt.value === newRate)) {
      if (!guestRateOptions.value.some(opt => opt.value === newRate)) {
        guestRateOptions.value.push({ label: `${newRate}Mbps`, value: newRate });
      }
    }
  },
  { immediate: true }
);

// 将handleNodeClick函数修改为处理范围节点展开
const handleNodeClick = data => {
  console.log("点击的节点:", data);

  // 如果是"更多选择..."节点
  if (data.value === "custom") {
    console.log("点击了更多选择...节点");

    // 如果节点没有子节点或者子节点为空数组，加载子节点
    if (!data.children || data.children.length === 0) {
      // 生成速率范围选项
      const ranges = [
        { min: 64, max: 1024, step: 64 },
        { min: 1024, max: 10240, step: 512 },
        { min: 10240, max: 102400, step: 2048 },
        { min: 102400, max: 1024000, step: 10240 }
      ];

      const rangeOptions = ranges.map(range => ({
        label: `${range.min} - ${range.max}`,
        value: `range-${range.min}-${range.max}`,
        children: [], // 使用空数组而不是null，确保显示展开图标
        isLeaf: false,
        hasChildren: true,
        range
      }));

      // 更新节点的子节点
      data.children = rangeOptions;

      // 强制刷新树
      treeKey.value++;
    }
  }

  // 如果是范围节点
  if (data.value && typeof data.value === "string" && data.value.startsWith("range-")) {
    console.log("点击了范围节点:", data.value);

    // 如果节点没有子节点或者子节点为空数组，加载子节点
    if (!data.children || data.children.length === 0) {
      // 从range-min-max格式中提取min和max
      const parts = data.value.split("-");
      if (parts.length === 3) {
        const min = parseInt(parts[1], 10);
        const max = parseInt(parts[2], 10);

        // 根据范围决定步长
        let step = 64;
        if (min >= 1024 && min < 10240) {
          step = 512;
        } else if (min >= 10240 && min < 102400) {
          step = 2048;
        } else if (min >= 102400) {
          step = 10240;
        }

        // 生成范围内的所有速率选项
        const values = [];
        for (let value = min; value <= max; value += step) {
          values.push({
            label: `${value}`,
            value: value,
            isLeaf: true
          });
        }

        // 更新节点的子节点
        data.children = values;

        // 强制刷新树
        treeKey.value++;
      }
    }
  }
};

// 添加一个变量来强制刷新树
const treeKey = ref(0);

// 初始化数据
onMounted(async () => {
  try {
    // 加载速率选项
    console.log("开始加载速率选项...");
    rateOptions.value = await preloadRateOptions();

    // 确保"更多选择..."节点正确配置
    const customNode = rateOptions.value.find(node => node.value === "custom");
    if (customNode) {
      // 确保节点有children属性（即使是空数组），这样会显示展开图标
      customNode.children = [];
      customNode.isLeaf = false;
      customNode.hasChildren = true;
    }

    // 设置默认值
    if (!swStorm.value.rate1) swStorm.value.rate1 = 64;
    if (!swStorm.value.rate2) swStorm.value.rate2 = 64;
    if (!swStorm.value.rate3) swStorm.value.rate3 = 64;

    console.log("组件挂载后的rateOptions状态:", rateOptions.value);
  } catch (error) {
    console.error("初始化速率选项时出错:", error);
  }
});

const securityActiveNames = ref(["managePassword"]); // 默认展开管理密码面板

// 处理描述编辑
const handleDescribeEdit = (row: any) => {
  console.log("开始编辑端口描述:", row);
  // 保存原始值，用于取消时恢复
  row.originalDescribe = row.describe;
  row.isEditing = true;

  // 使用 nextTick 确保输入框渲染后再聚焦
  nextTick(() => {
    const inputs = document.querySelectorAll('input[maxlength="32"]');
    const lastInput = inputs[inputs.length - 1] as HTMLInputElement;
    if (lastInput) {
      lastInput.focus();
      lastInput.select();
    }
  });
};

// 处理描述取消
const handleDescribeCancel = (row: any) => {
  console.log("取消端口描述编辑:", row);
  // 恢复原始值
  if (row.originalDescribe !== undefined) {
    row.describe = row.originalDescribe;
    delete row.originalDescribe;
  }
  row.isEditing = false;
};

// 处理描述确认
const handleDescribeConfirm = async (row: any) => {
  console.log("确认端口描述修改:", row);
  row.isEditing = false;

  try {
    await modifyDescribe(row);
    console.log("端口描述修改成功");
    // 清理原始值
    delete row.originalDescribe;
  } catch (error) {
    console.error("端口描述修改失败:", error);
    ElMessage.error("端口描述修改失败");
    // 如果修改失败，恢复原始值
    if (row.originalDescribe !== undefined) {
      row.describe = row.originalDescribe;
      delete row.originalDescribe;
    }
  }
};

// 获取 originalData 里当前 tab 的原始数据
function getOriginalDialogData() {
  if (!originalData.value) return {};
  switch (dialogTabActive.value) {
    case "first":
      return { system: { swPort: originalData.value.system?.swPort } };
    case "second":
      return { system: { swPoe: originalData.value.system?.swPoe } };
    case "third":
      return { network: { swVlan: originalData.value.network?.swVlan } };
    case "fourth":
      return { system: { swStorm: originalData.value.system?.swStorm } };
    case "fifth":
      return { network: { swIsolate: originalData.value.network?.swIsolate } };
    case "sixth":
      return { system: { swQos: originalData.value.system?.swQos } };
    default:
      return {};
  }
}

const hasDialogConfigChanged = computed(() => {
  // 如果对话框没有打开，返回 false
  if (!portDialogVisible.value) {
    return false;
  }

  if (!originalData.value || !deviceConfig) {
    // 静默返回 false，避免频繁的控制台输出
    return false;
  }

  let dialogData = null;
  let currentData = null;

  switch (dialogTabActive.value) {
    case "first":
      currentData = swPort.value;
      dialogData = { system: { swPort: generatePortData(currentData) } };
      break;
    case "second":
      currentData = swPoe.value;
      dialogData = { system: { swPoe: generatePortData(currentData) } };
      break;
    case "third":
      currentData = swVlan.value;
      dialogData = { network: { swVlan: generatePortData(currentData) } };
      break;
    case "fourth":
      currentData = swStorm.value;
      dialogData = { system: { swStorm: generatePortData(currentData) } };
      break;
    case "fifth":
      currentData = swIsolate.value;
      dialogData = { network: { swIsolate: generatePortData(currentData) } };
      break;
    case "sixth":
      currentData = swQos.value;
      dialogData = { system: { swQos: generatePortData(currentData) } };
      break;
    default:
      // 静默返回，避免频繁的控制台输出
      return false;
  }

  // 如果没有选中端口，直接比较当前数据与原始数据
  if (!selectedRows.value || selectedRows.value.length === 0) {
    let originalDataForTab = null;
    switch (dialogTabActive.value) {
      case "first":
        originalDataForTab = originalData.value.system?.swPort;
        break;
      case "second":
        originalDataForTab = originalData.value.system?.swPoe;
        break;
      case "third":
        originalDataForTab = originalData.value.network?.swVlan;
        break;
      case "fourth":
        originalDataForTab = originalData.value.system?.swStorm;
        break;
      case "fifth":
        originalDataForTab = originalData.value.network?.swIsolate;
        break;
      case "sixth":
        originalDataForTab = originalData.value.system?.swQos;
        break;
    }

    const currentDataStr = JSON.stringify(currentData);
    const originalDataStr = JSON.stringify(originalDataForTab);
    const hasChanged = currentDataStr !== originalDataStr;

    // 减少调试输出，只在有变化时输出
    if (hasChanged) {
      console.log("hasDialogConfigChanged: 检测到配置变化", {
        dialogTabActive: dialogTabActive.value,
        hasChanged
      });
    }

    return hasChanged;
  }

  const dialogDataStr = JSON.stringify(dialogData);
  const originalDialogDataStr = JSON.stringify(getOriginalDialogData());
  const hasChanged = dialogDataStr !== originalDialogDataStr;

  // 减少调试输出，只在有变化时输出
  if (hasChanged) {
    console.log("hasDialogConfigChanged: 检测到配置变化", {
      dialogTabActive: dialogTabActive.value,
      hasChanged
    });
  }

  return hasChanged;
});

defineExpose({
  acceptParams
});

// 添加处理密码清空的函数
const handlePasswordClear = (type: "radio0" | "radio1" | "guest") => {
  if (!deviceConfig.wireless?.[type]) return;
  deviceConfig.wireless[type].key = "";
};

// Add function to initialize activeNames
const initializeActiveNames = () => {
  // 如果已经有展开的面板，不要重置
  if (activeNames.value.length > 0) {
    console.log(`activeNames 已有展开项，跳过重新初始化:`, activeNames.value);
    return;
  }

  const names: string[] = [];

  // 检查WiFi设置面板
  if (drawerProps.value?.row?.supports?.wireless) {
    names.push("wifi");
  }

  // 检查DHCP设置面板
  if (drawerProps.value?.row?.supports?.network?.supports?.includes("dhcp") && deviceConfig.network?.dhcp) {
    names.push("dhcp");
  }

  // 检查网桥WiFi设置面板
  if (deviceConfig.network?.brAp && Object.keys(deviceConfig.network.brAp).length > 0) {
    names.push("brAp");
  }

  // 检查LAN设置面板
  if (drawerProps.value?.row?.supports?.network?.supports?.includes("lan") && deviceConfig.network?.lan) {
    names.push("lan");
  }

  // 检查WAN设置面板
  if (drawerProps.value?.row?.supports?.network?.supports?.includes("wan") && deviceConfig.network?.wan) {
    names.push("wan");
  }

  // 检查DNS设置面板
  if (drawerProps.value?.row?.supports?.network?.supports?.includes("dhcp") && deviceConfig.network?.dhcp) {
    names.push("dns");
  }

  // 修改：默认展开所有面板，而不是只展开第一个
  if (names.length > 0) {
    activeNames.value = names;
    console.log(`初始化 activeNames (全部展开):`, activeNames.value);
  }
};

// Watch for changes in relevant properties and update activeNames
watch(
  [
    () => drawerProps.value?.row?.supports?.wireless,
    () => drawerProps.value?.row?.supports?.network?.supports,
    () => deviceConfig.network?.dhcp,
    () => deviceConfig.network?.brAp
  ],
  () => {
    // 只在抽屉显示时才初始化，避免在数据加载过程中重复重置
    if (drawerVisible.value) {
      initializeActiveNames();
    }
  },
  { immediate: false } // 改为 false，避免立即执行
);

// 添加 watch 来监听加密方式的变化
watch(
  [encryptionRadio0Method, encryptionRadio1Method, encryptionGuestMethod],
  ([radio0, radio1, guest]) => {
    // 只在初始化时设置一次加密状态
    if (!manualEncryptRadio0.value) {
      encryptRadio0.value = radio0;
      manualEncryptRadio0.value = true;
    }
    if (!manualEncryptRadio1.value) {
      encryptRadio1.value = radio1;
      manualEncryptRadio1.value = true;
    }
    if (!manualEncryptGuest.value) {
      encryptGuest.value = guest;
      manualEncryptGuest.value = true;
    }
  },
  { immediate: true }
);

// 修改设备名称的显示逻辑
const displayDeviceName = computed(() => {
  // 处理 deviceName 可能是对象的情况
  if (deviceConfig.deviceName) {
    if (typeof deviceConfig.deviceName === "string") {
      return deviceConfig.deviceName;
    } else if (typeof deviceConfig.deviceName === "object") {
      // 如果是对象，尝试提取字符串值
      console.warn("设备名称是对象类型:", deviceConfig.deviceName);
      // 尝试从对象中提取值
      const deviceNameObj = deviceConfig.deviceName as any;
      if (deviceNameObj.value !== undefined) {
        return deviceNameObj.value;
      } else if (deviceNameObj.name !== undefined) {
        return deviceNameObj.name;
      } else if (deviceNameObj.text !== undefined) {
        return deviceNameObj.text;
      } else {
        // 如果无法提取，返回默认值
        return t("device.unnamed");
      }
    }
  }
  return t("device.unnamed");
});

// 添加取消编辑的临时变量和函数
const tempDeviceName = ref("");

// 修改编辑设备名称的函数，增加保存原始值
const editDeviceName = () => {
  // 处理设备名称可能是对象的情况
  let deviceNameValue = deviceConfig.deviceName;
  if (deviceConfig.deviceName && typeof deviceConfig.deviceName === "object") {
    const deviceNameObj = deviceConfig.deviceName as any;
    deviceNameValue = deviceNameObj.value || deviceNameObj.name || deviceNameObj.text || "";
  }
  tempDeviceName.value = deviceNameValue;
  editName.value = true;
};

// 添加取消编辑的函数
const cancelEdit = () => {
  // 确保恢复的是字符串值
  deviceConfig.deviceName = tempDeviceName.value;
  editName.value = false;
  deviceNameChanged.value = false;
  // 清除校验错误信息
  if (ruleFormRef.value) {
    ruleFormRef.value.clearValidate("deviceName");
  }
};

const handleGuestRateChange = (value: string | number) => {
  // 只有在设备支持无线功能时才处理
  if (!drawerProps.value.row?.supports?.wireless || !deviceConfig.wireless?.guest) {
    return;
  }

  if (value === "" || value === null || value === undefined) {
    deviceConfig.wireless.guest.rate = null;
    return;
  }
  const numValue = Number(value);
  if (!isNaN(numValue) && Number.isInteger(numValue) && numValue >= 0 && numValue <= 1000) {
    deviceConfig.wireless.guest.rate = numValue;
  }
};

// 响应式drawer宽度
const drawerSize = computed(() => {
  if (window.innerWidth <= 320) return "100vw";
  if (window.innerWidth <= 480) return "98vw";
  if (window.innerWidth <= 768) return "90vw";
  if (window.innerWidth <= 1024) return "80vw";
  return "680px";
});

// 响应式图表尺寸
watch(
  () => [window.innerWidth],
  () => {
    chartWidth.value = window.innerWidth <= 480 ? window.innerWidth - 60 : 640;
    chartHeight.value = window.innerWidth <= 480 ? 300 : 400;
  },
  { immediate: true }
);

// 计算端口设置标签页是否应该显示
const shouldShowPortSettings = computed(() => {
  const deviceType = drawerProps.value?.row?.deviceType;
  const supports = drawerProps.value?.row?.supports;
  const hasSwPoe = supports?.system?.supports?.includes("swPoe");
  const isSwitch = deviceType === "switch";
  const shouldShow = isSwitch && hasSwPoe;

  console.log("🔍 端口设置标签页显示条件检查:");
  console.log("- deviceType:", deviceType);
  console.log("- isSwitch:", isSwitch);
  console.log("- supports:", supports);
  console.log("- system.supports:", supports?.system?.supports);
  console.log("- hasSwPoe:", hasSwPoe);
  console.log("- shouldShow:", shouldShow);

  return shouldShow;
});
</script>

<style lang="scss" scoped>
.device-img {
  width: 80px;
  height: 80px;
  margin-bottom: 10px;
  object-fit: contain; /* 确保图片比例适配 */
}

/* 端口容器 */
.sw-port-container {
  width: 100%;
  overflow: auto hidden; /* 水平滚动 */
}
.sw-port-list {
  display: flex;
  min-width: fit-content; /* 确保内容不被压缩 */
  padding: 0;
  margin: 0;
  list-style: none;
}

/* 单行显示（端口数 <= 5） */
.sw-port-list.single-row {
  flex-flow: row nowrap;
  gap: 10px;
  justify-content: flex-start;
}

/* 多行显示容器 */
.sw-port-multi-row {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  overflow-x: auto;
}

/* 每一行的端口列表 */
.sw-port-row {
  display: flex;
  flex-direction: row;
  gap: 8px;
  min-width: fit-content;
  padding: 0;
  margin: 0;
  list-style: none;
}
.sw-port-tag {
  box-sizing: border-box;
  display: flex;
  flex-shrink: 0; /* 防止压缩 */
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 60px; /* 增加宽度以容纳更长的名称 */
  min-height: 65px; /* 最小高度 */
  padding: 4px;
  margin: 1px;
  background-color: #f9f9f9;
  border: 1px solid transparent;
  border-radius: 5px;
  transition: all 0.3s ease;
}
.sw-port-list li {
  text-align: center; /* 文字居中 */
}
.sw-port-example {
  display: flex;
  flex-shrink: 0; /* 防止内容缩小导致换行 */
  align-items: center;
  justify-content: flex-start;
  margin-right: 15px; /* 给每个项增加右边距，确保之间有间隔 */
  white-space: nowrap; /* 确保文字不换行 */
}
.port-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px; /* 图标与文字之间的间距 */
}
.port-text {
  line-height: 20px; /* 保证文字与图标垂直居中 */
  white-space: nowrap; /* 确保文字不换行 */
}

/* 特殊样式 */
.selected-port {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff; /* 设置边框宽度为 1px */
  border-radius: 5px; /* 可选，增加圆角效果 */
}
.edit-cell {
  min-height: 32px;
  padding: 5px;
  line-height: 32px;
  cursor: pointer;
  &:hover {
    background-color: #f5f7fa;
  }
}

@media screen and (width <= 768px) {
  :deep(.el-drawer) {
    .el-drawer__header {
      padding: 12px 16px;
      font-size: 16px;
      border-bottom: 1px solid #ebeef5;
    }
    .el-drawer__body {
      padding: 12px;
    }
    .el-form {
      .el-form-item {
        margin-bottom: 16px;
        .el-form-item__label {
          width: 100% !important;
          padding: 0 0 6px !important;
          font-size: 14px;
          font-weight: 500;
          line-height: 1.3;
          text-align: left !important;
        }
        .el-form-item__content {
          margin-left: 0 !important;
          font-size: 14px;
          .el-input {
            .el-input__inner {
              padding: 10px 12px;
              font-size: 14px;
            }
          }
          .el-select {
            width: 100%;
            .el-input__inner {
              padding: 10px 12px;
              font-size: 14px;
            }
          }
          .el-text {
            font-size: 14px;
            line-height: 1.4;
          }
        }
      }
    }
    .el-tabs {
      .el-tabs__item {
        min-width: auto;
        padding: 0 12px;
        font-size: 14px;
      }
      .el-tabs__content {
        padding: 12px 0;
      }
    }
    .el-card {
      margin: 10px 0 !important;
      .el-card__body {
        padding: 12px;
      }
      .el-card__header {
        padding: 12px;
        font-size: 14px;
        font-weight: 500;
      }
    }
    .el-collapse {
      .el-collapse-item__header {
        padding: 0 12px;
        font-size: 14px;
        font-weight: 500;
      }
      .el-collapse-item__content {
        padding: 12px;
      }
    }

    // 端口图标优化
    .sw-port-tag {
      width: 45px;
      height: 60px;
      padding: 4px;
      .el-image {
        width: 30px !important;
        height: 30px !important;
      }
      .el-text {
        font-size: 11px;
      }
    }
    .sw-port-list {
      gap: 6px;
    }

    // 设备图标
    .device-img {
      width: 60px;
      height: 60px;
    }

    // 按钮优化
    .el-drawer__footer {
      padding: 12px 16px;
      text-align: center;
      border-top: 1px solid #ebeef5;
      .el-button {
        min-width: 80px;
        padding: 8px 20px;
        font-size: 14px;
        &:first-child {
          margin-right: 12px;
        }
      }
    }

    // 表格优化
    .el-table {
      font-size: 13px;
      .el-table__header th {
        padding: 8px 0;
        font-size: 13px;
      }
      .el-table__body td {
        padding: 8px 0;
        font-size: 13px;
      }
    }
  }
}

@media screen and (width <= 480px) {
  :deep(.el-drawer) {
    .el-drawer__header {
      padding: 10px 12px;
      font-size: 14px;
    }
    .el-drawer__body {
      padding: 8px;
    }
    .el-form {
      .el-form-item {
        margin-bottom: 12px;
        .el-form-item__label {
          width: 100% !important;
          padding: 0 0 4px !important;
          font-size: 12px;
          font-weight: 500;
          line-height: 1.2;
          text-align: left !important;
        }
        .el-form-item__content {
          margin-left: 0 !important;
          font-size: 12px;
          .el-input {
            .el-input__inner {
              padding: 8px 10px;
              font-size: 12px;
            }
          }
          .el-select {
            width: 100%;
            .el-input__inner {
              padding: 8px 10px;
              font-size: 12px;
            }
          }
          .el-text {
            font-size: 12px;
            line-height: 1.3;
          }
        }
      }
    }
    .el-tabs {
      .el-tabs__item {
        min-width: auto;
        padding: 0 6px;
        font-size: 11px;
      }
      .el-tabs__content {
        padding: 8px 0;
      }
    }
    .el-card {
      padding: 6px !important;
      margin: 6px 0 !important;
      .el-card__body {
        padding: 6px;
      }
      .el-card__header {
        padding: 8px;
        font-size: 12px;
      }
    }
    .el-collapse {
      .el-collapse-item__header {
        padding: 0 8px;
        font-size: 12px;
      }
      .el-collapse-item__content {
        padding: 8px;
      }
    }

    // 端口图标优化
    .sw-port-tag {
      width: 38px;
      height: 48px;
      padding: 2px;
      .el-image {
        width: 24px !important;
        height: 24px !important;
      }
      .el-text {
        font-size: 9px;
        line-height: 1.1;
      }
    }
    .sw-port-list {
      gap: 3px;
    }

    // 设备图标
    .device-img {
      width: 50px;
      height: 50px;
    }

    // 按钮优化
    .el-drawer__footer {
      padding: 8px 12px;
      .el-button {
        min-width: 60px;
        padding: 6px 12px;
        font-size: 12px;
      }
    }

    // 表格优化
    .el-table {
      font-size: 11px;
      .el-table__header th {
        padding: 6px 0;
        font-size: 11px;
      }
      .el-table__body td {
        padding: 6px 0;
        font-size: 11px;
      }
    }
  }
}

@media screen and (width <= 320px) {
  :deep(.el-drawer) {
    .el-drawer__header {
      padding: 8px 10px;
      font-size: 13px;
    }
    .el-drawer__body {
      padding: 6px;
    }
    .el-form {
      .el-form-item {
        margin-bottom: 10px;
        .el-form-item__label {
          padding: 0 0 3px !important;
          font-size: 11px;
        }
        .el-form-item__content {
          font-size: 11px;
          .el-input {
            .el-input__inner {
              padding: 6px 8px;
              font-size: 11px;
            }
          }
          .el-select {
            .el-input__inner {
              padding: 6px 8px;
              font-size: 11px;
            }
          }
          .el-text {
            font-size: 11px;
          }
        }
      }
    }
    .el-tabs {
      .el-tabs__item {
        padding: 0 4px;
        font-size: 10px;
      }
      .el-tabs__content {
        padding: 6px 0;
      }
    }
    .el-card {
      padding: 4px !important;
      margin: 4px 0 !important;
      .el-card__body {
        padding: 4px;
      }
      .el-card__header {
        padding: 6px;
        font-size: 11px;
      }
    }

    // 设备图标
    .device-img {
      width: 40px;
      height: 40px;
    }

    // 按钮优化
    .el-drawer__footer {
      padding: 6px 8px;
      .el-button {
        min-width: 50px;
        padding: 4px 8px;
        font-size: 11px;
      }
    }
  }
}

/* 端口图标样式 */
.port-icon-img {
  flex-shrink: 0; /* 防止图标被压缩 */
  width: 35px !important;
  height: 35px !important;
  margin-bottom: 2px;
}

/* 端口名称容器 */
.port-name-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 20px; /* 确保有足够的高度 */
  padding: 0 2px;
  overflow: hidden; /* 防止内容溢出 */
  .port-name-input {
    width: 100% !important;
    font-size: 10px;
    :deep(.el-input__inner) {
      padding: 2px 4px;
      font-size: 10px;
      text-align: center;
    }
  }
  .port-name-display {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    cursor: pointer;
    .port-name-text {
      display: -webkit-box;
      max-width: 100%;
      overflow: hidden;
      font-size: 10px !important;
      line-height: 1.2;
      text-align: center;
      -webkit-line-clamp: 2; /* 最多显示2行 */
      word-break: break-all; /* 允许长单词换行 */
      -webkit-box-orient: vertical;
    }
    .edit-icon {
      position: absolute;
      top: -2px;
      right: -2px;
      padding: 1px;
      font-size: 8px;
      visibility: hidden; // 默认隐藏
      background: rgb(255 255 255 / 80%);
      border-radius: 2px;
      opacity: 0;
      transition: opacity 0.2s;
    }
    &:hover .edit-icon {
      visibility: visible; // 悬浮时显示
      opacity: 1;
    }
  }
}

/* 端口标签悬停和选中效果 */
.sw-port-tag:hover {
  background-color: #e6f7ff;
  border-color: #91d5ff;
  transform: translateY(-1px);
}
.sw-port-tag.selected-port {
  color: white;
  background-color: #1890ff;
  border-color: #1890ff;
  .port-name-text {
    color: white !important;
  }
  .edit-icon {
    color: white;
    background: rgb(255 255 255 / 20%);
  }
}
</style>
