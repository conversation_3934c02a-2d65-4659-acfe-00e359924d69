<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>端口布局测试</title>
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        .test-container {
            padding: 20px;
            margin-bottom: 30px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
        }
        .test-title {
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: bold;
            color: #333333;
        }
        
        /* 端口容器 */
        .sw-port-container {
            width: 100%;
            overflow: auto hidden;
        }
        .sw-port-list {
            display: flex;
            min-width: fit-content;
            padding: 0;
            margin: 0;
            list-style: none;
        }

        /* 单行显示（端口数 <= 5） */
        .sw-port-list.single-row {
            flex-flow: row nowrap;
            gap: 10px;
            justify-content: flex-start;
        }

        /* 多行显示容器 */
        .sw-port-multi-row {
            display: flex;
            flex-direction: column;
            gap: 8px;
            width: 100%;
            overflow-x: auto;
        }

        /* 每一行的端口列表 */
        .sw-port-row {
            display: flex;
            flex-direction: row;
            gap: 8px;
            min-width: fit-content;
            padding: 0;
            margin: 0;
            list-style: none;
        }
        .sw-port-tag {
            box-sizing: border-box;
            display: flex;
            flex-shrink: 0;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
            width: 60px;
            min-height: 65px;
            padding: 4px;
            margin: 1px;
            cursor: pointer;
            background-color: #f9f9f9;
            border: 1px solid transparent;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        /* 端口图标样式 */
        .port-icon-img {
            flex-shrink: 0;
            width: 35px !important;
            height: 35px !important;
            margin-bottom: 2px;
            background-color: #4CAF50;
            border-radius: 4px;
        }
        .port-icon-img.inactive {
            background-color: #cccccc;
        }

        /* 端口名称容器 */
        .port-name-container {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            min-height: 20px;
            padding: 0 2px;
            overflow: hidden;
        }
        .port-name-display {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            cursor: pointer;
        }
        .port-name-text {
            display: -webkit-box;
            max-width: 100%;
            overflow: hidden;
            font-size: 10px !important;
            line-height: 1.2;
            text-align: center;
            -webkit-line-clamp: 2;
            word-break: break-all;
            -webkit-box-orient: vertical;
        }

        /* 端口标签悬停和选中效果 */
        .sw-port-tag:hover {
            background-color: #e6f7ff;
            border-color: #91d5ff;
            transform: translateY(-1px);
        }
        .sw-port-tag.selected-port {
            color: white;
            background-color: #1890ff;
            border-color: #1890ff;
        }
        .sw-port-tag.selected-port .port-name-text {
            color: white !important;
        }
    </style>
</head>
<body>
    <h1>端口布局测试</h1>
    
    <!-- 测试场景1：端口数 <= 5，单行显示 -->
    <div class="test-container">
        <div class="test-title">场景1：端口数 ≤ 5，单行显示</div>
        <div class="sw-port-container">
            <ul class="sw-port-list single-row">
                <li class="sw-port-tag">
                    <div class="port-icon-img"></div>
                    <div class="port-name-container">
                        <div class="port-name-display">
                            <span class="port-name-text">GE1</span>
                        </div>
                    </div>
                </li>
                <li class="sw-port-tag">
                    <div class="port-icon-img"></div>
                    <div class="port-name-container">
                        <div class="port-name-display">
                            <span class="port-name-text">GE2</span>
                        </div>
                    </div>
                </li>
                <li class="sw-port-tag selected-port">
                    <div class="port-icon-img"></div>
                    <div class="port-name-container">
                        <div class="port-name-display">
                            <span class="port-name-text">GE3</span>
                        </div>
                    </div>
                </li>
                <li class="sw-port-tag">
                    <div class="port-icon-img inactive"></div>
                    <div class="port-name-container">
                        <div class="port-name-display">
                            <span class="port-name-text">GE4</span>
                        </div>
                    </div>
                </li>
                <li class="sw-port-tag">
                    <div class="port-icon-img"></div>
                    <div class="port-name-container">
                        <div class="port-name-display">
                            <span class="port-name-text">Very Long Port Name</span>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
    </div>

    <!-- 测试场景2：端口数 > 5，多行显示 -->
    <div class="test-container">
        <div class="test-title">场景2：端口数 > 5，多行显示（GE1 GE3 GE5 GE7 / GE2 GE4 GE6 GE8）</div>
        <div class="sw-port-container">
            <div class="sw-port-multi-row">
                <!-- 第一行：奇数索引端口 (GE1, GE3, GE5, GE7) -->
                <ul class="sw-port-row">
                    <li class="sw-port-tag">
                        <div class="port-icon-img"></div>
                        <div class="port-name-container">
                            <div class="port-name-display">
                                <span class="port-name-text">GE1</span>
                            </div>
                        </div>
                    </li>
                    <li class="sw-port-tag selected-port">
                        <div class="port-icon-img"></div>
                        <div class="port-name-container">
                            <div class="port-name-display">
                                <span class="port-name-text">GE3</span>
                            </div>
                        </div>
                    </li>
                    <li class="sw-port-tag">
                        <div class="port-icon-img"></div>
                        <div class="port-name-container">
                            <div class="port-name-display">
                                <span class="port-name-text">GE5</span>
                            </div>
                        </div>
                    </li>
                    <li class="sw-port-tag">
                        <div class="port-icon-img"></div>
                        <div class="port-name-container">
                            <div class="port-name-display">
                                <span class="port-name-text">Very Long Port Name</span>
                            </div>
                        </div>
                    </li>
                </ul>

                <!-- 第二行：偶数索引端口 (GE2, GE4, GE6, GE8) -->
                <ul class="sw-port-row">
                    <li class="sw-port-tag">
                        <div class="port-icon-img"></div>
                        <div class="port-name-container">
                            <div class="port-name-display">
                                <span class="port-name-text">GE2</span>
                            </div>
                        </div>
                    </li>
                    <li class="sw-port-tag">
                        <div class="port-icon-img inactive"></div>
                        <div class="port-name-container">
                            <div class="port-name-display">
                                <span class="port-name-text">GE4</span>
                            </div>
                        </div>
                    </li>
                    <li class="sw-port-tag">
                        <div class="port-icon-img"></div>
                        <div class="port-name-container">
                            <div class="port-name-display">
                                <span class="port-name-text">GE6</span>
                            </div>
                        </div>
                    </li>
                    <li class="sw-port-tag">
                        <div class="port-icon-img"></div>
                        <div class="port-name-container">
                            <div class="port-name-display">
                                <span class="port-name-text">GE8</span>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 添加点击事件来测试选中效果
        document.querySelectorAll('.sw-port-tag').forEach(tag => {
            tag.addEventListener('click', function() {
                // 移除其他选中状态
                document.querySelectorAll('.sw-port-tag').forEach(t => t.classList.remove('selected-port'));
                // 添加选中状态
                this.classList.add('selected-port');
            });
        });
    </script>
</body>
</html>
