<template>
  <el-card :style="{ marginTop: '10px', marginBottom: '10px' }">
    <template #header>
      <div class="card-header">
        <span>{{ $t("device.deviceInfo") }}</span>
      </div>
    </template>
    <el-form-item :label="$t('device.deviceId')" prop="deviceId" label-position="left" label-width="160px">
      <el-text>{{ props.deviceId }}</el-text>
    </el-form-item>
    <el-form-item :label="$t('device.model')" prop="deviceModel" label-position="left" label-width="160px">
      <el-text>{{ props.deviceModel }}</el-text>
    </el-form-item>
    <el-form-item :label="$t('device.type')" prop="deviceType" label-position="left" label-width="160px">
      <span>{{ deviceTypeLabel }}</span>
    </el-form-item>
    <el-form-item
      :label="$t('device.workmode')"
      prop="workmode"
      label-position="left"
      v-if="props.network && props.network.workmode"
      label-width="160px"
    >
      <span>
        {{ props.network.workmode }}
      </span>
    </el-form-item>
    <el-form-item :label="$t('device.bootTime')" label-position="left" label-width="160px">
      <el-text>{{ formatBootTime(props.bootTime) }}</el-text>
    </el-form-item>
    <el-form-item :label="$t('device.mac')" prop="mac" label-position="left" label-width="160px">
      <el-text>{{ props.mac }}</el-text>
    </el-form-item>
    <el-form-item :label="$t('device.ip')" prop="ip" label-position="left" label-width="160px">
      <el-text>{{ props.ipaddr }}</el-text>
    </el-form-item>
    <el-form-item
      :label="t('device.currentStatus')"
      prop="status"
      label-position="left"
      v-if="props.network?.internet"
      label-width="160px"
    >
      <el-text :style="{ color: statusTagType === 'danger' ? 'red' : 'green' }">
        {{ props.network?.internet === 1 ? $t("device.connected") : $t("device.disconnected") }}
      </el-text>
    </el-form-item>
  </el-card>
  <el-card :style="{ marginTop: '10px', marginBottom: '10px' }">
    <template #header>
      <div class="card-header">
        <span>{{ $t("device.lanInfo") }}</span>
      </div>
    </template>
    <el-form-item :label="$t('device.lanIp')" prop="lanIp" label-position="left" label-width="160px">
      <el-text>{{ props.network?.lan?.ipaddr }}</el-text>
    </el-form-item>
    <el-form-item :label="$t('device.lanNetmask')" prop="lanNetmask" label-position="left" label-width="160px">
      <el-text>{{ props.network?.lan?.netmask }}</el-text>
    </el-form-item>
  </el-card>

  <div v-if="Array.isArray(props.network?.wan)">
    <el-card v-for="(item, index) in props.network.wan" :key="index">
      <template #header>
        <div class="card-header">
          <span>{{ $t("device.wanInfo") + (index + 1) }}</span>
        </div>
      </template>
      <el-form-item :label="$t('device.proto')" prop="lan1Ip" label-position="left" label-width="160px">
        <el-text>
          {{
            item.proto === "dhcp"
              ? $t("device.dhcp")
              : item.proto === "static"
                ? $t("device.static")
                : item.proto === "pppoe"
                  ? $t("device.pppoe")
                  : item.proto
          }}
        </el-text>
      </el-form-item>
      <el-form-item :label="$t('device.wanIp')" prop="lan1Ip" label-position="left" label-width="160px">
        <el-text>{{ item.ipaddr }}</el-text>
      </el-form-item>
      <el-form-item :label="$t('device.netmask')" prop="netmask" label-position="left" label-width="160px">
        <el-text>{{ item.netmask }}</el-text>
      </el-form-item>
      <el-form-item :label="$t('device.gawa')" prop="gawa" label-position="left" label-width="160px">
        <el-text>{{ item.gawa }}</el-text>
      </el-form-item>
      <el-form-item :label="$t('device.dns1')" prop="dns1" label-position="left" label-width="160px">
        <el-text>{{ item.dns1 }}</el-text>
      </el-form-item>
      <el-form-item :label="$t('device.dns2')" prop="dns2" label-position="left" label-width="160px">
        <el-text>{{ item.dns2 }}</el-text>
      </el-form-item>
    </el-card>
  </div>
  <el-card :style="{ marginTop: '10px', marginBottom: '10px' }" v-if="props.system && props.system.led">
    <template #header>
      <div class="card-header">
        <span>{{ $t("device.ledConfiguration") }}</span>
      </div>
    </template>
    <el-form-item :label="$t('device.lightingMode')" prop="lan1Ip" label-position="left" label-width="160px">
      <el-text>
        {{ ledModeLabel }}
      </el-text>
    </el-form-item>
    <el-form-item
      v-if="props.system?.led?.mode === 'timer'"
      :label="$t('device.beginTime')"
      prop="lan1Ip"
      label-position="left"
      label-width="160px"
    >
      <el-text>{{ props.system.led.beginTime }}</el-text>
    </el-form-item>
    <el-form-item
      v-if="props.system?.led?.mode === 'timer'"
      :label="$t('device.endTime')"
      prop="lan1Ip"
      label-position="left"
      label-width="160px"
    >
      <el-text>{{ props.system.led.endTime }}</el-text>
    </el-form-item>
    <el-form-item :label="t('device.currentStatus')" prop="lan1Ip" label-position="left" label-width="160px">
      <el-text>
        {{ props.system?.led?.status === "on" ? $t("device.on") : $t("device.off") }}
      </el-text>
    </el-form-item>
  </el-card>
</template>

<script setup lang="ts" name="DeviceInfo">
import { computed, defineProps } from "vue";
import { useI18n } from "vue-i18n";
import { useGlobalStore } from "@/stores/modules/global";
import { formatBootTime } from "@/api/interface/bridgeClientDrawer";
// 获取浏览器语言
const globalStore = useGlobalStore();
const isChinese = computed(() => globalStore.language === "zh"); // 使用全局语言

const { t } = useI18n();

const props = defineProps({
  deviceId: String,
  deviceModel: String,
  deviceType: String,
  deviceTypes: Array,
  bootTime: Number,
  mac: String,
  ipaddr: String,
  statusTagType: String,
  statusLabel: String,
  network: Object,
  system: Object
});

// LED模式显示标签
const ledModeLabel = computed(() => {
  const mode = props.system?.led?.mode;
  if (mode === "on") return t("device.on");
  if (mode === "off") return t("device.off");
  if (mode === "timer") return t("device.timer");
  return mode || "";
});

// 设备类型显示标签
const deviceTypeLabel = computed(() => {
  const list = props.deviceTypes || [];
  const found = list.find((item: any) => item?.configCode === props.deviceType);
  if (!found) return props.deviceType || "";
  return isChinese.value ? found["configDesc"] : found["attribute"] || found["configDesc"];
});
</script>
