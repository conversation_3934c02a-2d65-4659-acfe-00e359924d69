// 获取项目设备列表
import { useUserStore } from "@/stores/modules/user";
import type { Project } from "@/api/interface/project";
import { deviceStatusEnum, getDeviceConfigJwe, pushDeviceConfigJwe } from "@/api/modules/project";
import { useGlobalStore } from "@/stores/modules/global";
import { computed } from "vue";
import { ApDrawerParams, BridgeClientDrawerParams, ExpandedRowData, TerminalDrawerParams } from "@/api/interface/terminal";

export const getDeviceUserList = (params: { deviceId: string }) => {
  const reqParams: Project.ReqConfigParams = {
    cmd: 4,
    deviceId: params.deviceId,
    userId: useUserStore().userInfo.userId,
    data: {
      system: ["userList"]
    }
  };

  return getDeviceConfigJwe(reqParams);
};

export const getDeviceUserListStatus = (params: { deviceId: string }) => {
  const reqParams: Project.ReqConfigParams = {
    cmd: 10,
    deviceId: params.deviceId,
    userId: useUserStore().userInfo.userId,
    data: {
      system: ["userList"]
    }
  };

  return getDeviceConfigJwe(reqParams);
};

export const getDeviceApList = (params: { deviceId: string }) => {
  const reqParams: Project.ReqConfigParams = {
    cmd: 4,
    deviceId: params.deviceId,
    userId: useUserStore().userInfo.userId,
    data: {
      system: ["apList"]
    }
  };

  return getDeviceConfigJwe(reqParams);
};

export const getDeviceApListStatus = (params: { deviceId: string }) => {
  const reqParams: Project.ReqConfigParams = {
    cmd: 10,
    deviceId: params.deviceId,
    userId: useUserStore().userInfo.userId,
    data: {
      system: ["apList"]
    }
  };

  return getDeviceConfigJwe(reqParams);
};

export const formatBytes = (row: any, column: any): string => {
  // 获取当前列的值，注意使用 column.property 来动态访问
  const bytes = row[column.property];

  if (bytes === null || bytes === undefined) {
    return "--"; // 如果数据为空或不存在，返回--
  }

  // 定义单位数组
  const units = ["B", "KB", "MB", "GB", "TB", "PB"];

  let unitIndex = 0; // 初始单位为字节（B）
  let value = bytes; // 存储转换后的值

  // 根据字节数递归除以1024，直到大于1为止
  while (value >= 1024 && unitIndex < units.length - 1) {
    value /= 1024;
    unitIndex++;
  }

  // 返回格式化后的值，保留两位小数并附加单位
  return `${value.toFixed(2)} ${units[unitIndex]}`;
};

// 格式化 bootTime
export const formatBootTime = (bootTimeInSeconds: number): string => {
  // 转换为年月日时分秒
  const years = Math.floor(bootTimeInSeconds / (365 * 24 * 60 * 60));
  const months = Math.floor((bootTimeInSeconds % (365 * 24 * 60 * 60)) / (30 * 24 * 60 * 60));
  const days = Math.floor((bootTimeInSeconds % (30 * 24 * 60 * 60)) / (24 * 60 * 60));
  const hours = Math.floor((bootTimeInSeconds % (24 * 60 * 60)) / 3600);
  const minutes = Math.floor((bootTimeInSeconds % 3600) / 60);
  const seconds = bootTimeInSeconds % 60;

  // 获取语言环境（假设你使用 Vuex 或其他方式来管理语言）
  const lang = useUserStore().lang; // 例如从 Vuex 中获取当前语言

  // 以数组的形式存储各部分
  const timeParts: string[] = [];

  if (years > 0) {
    timeParts.push(`${years} ${lang === "zh-CN" ? "年" : "years"}`);
  }
  if (months > 0) {
    timeParts.push(`${months} ${lang === "zh-CN" ? "月" : "months"}`);
  }
  if (days > 0) {
    timeParts.push(`${days} ${lang === "zh-CN" ? "天" : "days"}`);
  }
  if (hours > 0) {
    timeParts.push(`${hours} ${lang === "zh-CN" ? "小时" : "hours"}`);
  }
  if (minutes > 0) {
    timeParts.push(`${minutes} ${lang === "zh-CN" ? "分钟" : "minutes"}`);
  }
  if (seconds > 0 || timeParts.length === 0) {
    timeParts.push(`${seconds} ${lang === "zh-CN" ? "秒" : "seconds"}`);
  }

  // 拼接时间部分，使用空格分隔
  return timeParts.join(" ");
};

export const formatOnlineStatus = (row: any) => {
  const statusItem = deviceStatusEnum.value.find(item => item.value !== row.online);
  return statusItem ? statusItem.label : ""; // 返回对应的标签
};

// 格式化时间函数
export const formatTime = (bootTimeInSeconds: number): string => {
  const globalStore = useGlobalStore();
  const isChinese = computed(() => globalStore.language === "zh");

  const timeParts = [];
  const timeUnits = [
    { value: 365 * 24 * 60 * 60, labelZh: "年", labelEn: "yr" },
    { value: 30 * 24 * 60 * 60, labelZh: "月", labelEn: "mo" },
    { value: 24 * 60 * 60, labelZh: "天", labelEn: "d" },
    { value: 3600, labelZh: "小时", labelEn: "h" },
    { value: 60, labelZh: "分钟", labelEn: "min" },
    { value: 1, labelZh: "秒", labelEn: "s" }
  ];

  let remainingTime = bootTimeInSeconds || 0;
  timeUnits.forEach(unit => {
    const value = Math.floor(remainingTime / unit.value);
    if (value > 0) {
      timeParts.push(`${value} ${isChinese.value ? unit.labelZh : unit.labelEn}`);
      remainingTime %= unit.value;
    }
  });

  // 如果没有任何时间部分，显示0秒
  if (timeParts.length === 0) {
    timeParts.push(`0 ${isChinese.value ? "秒" : "s"}`);
  }

  return timeParts.join(" ");
};

export const loadApListData = async (row: any) => {
  try {
    // 使用异步请求获取数据
    const resultData = await getDeviceApList({ deviceId: row.deviceId });
    const resultStatusData = await getDeviceApListStatus({ deviceId: row.deviceId });

    // 初始化合并后的 apList
    let mergedApList: any[] = [];

    // 处理第一个数据源
    if (resultData && Array.isArray(resultData.data?.system.apList)) {
      mergedApList = [...resultData.data.system.apList];
    }

    // 合并第二个数据源
    if (resultStatusData && Array.isArray(resultStatusData.data?.system.apList)) {
      resultStatusData.data?.system.apList.forEach((item: any) => {
        const existingItem = mergedApList.find(ap => ap.mac === item.mac);
        if (existingItem) {
          // 合并对象属性
          Object.assign(existingItem, item);
        } else {
          // 新增不存在的对象
          mergedApList.push(item);
        }
      });
    }

    // 更新 row 的 apList
    row.apList = mergedApList;
  } catch (err) {
    console.error("加载设备终端列表失败:", err);
  }
};

export const loadUserListData = async (row: any) => {
  try {
    // 使用异步请求获取数据
    const resultData = await getDeviceUserList({ deviceId: row.deviceId });
    const resultStatusData = await getDeviceUserListStatus({ deviceId: row.deviceId });

    // 初始化合并后的 userList
    let mergedUserList: any[] = [];

    // 处理第一个数据源
    if (resultData && Array.isArray(resultData.data.system.userList)) {
      mergedUserList = [...resultData.data.system.userList];
    }

    // 合并第二个数据源
    if (resultStatusData && Array.isArray(resultStatusData.data.system.userList)) {
      resultStatusData.data.system.userList.forEach((item: any) => {
        const existingItem = mergedUserList.find(user => user.macaddr === item.macaddr);
        if (existingItem) {
          // 合并对象属性
          Object.assign(existingItem, item);
        } else {
          // 新增不存在的对象
          mergedUserList.push(item);
        }
      });
    }

    // 更新 row 的 userList
    row.userList = mergedUserList;
    console.log("row.userList:", row.userList);
  } catch (err) {
    console.error("加载设备终端列表失败:", err);
  }
};

// 加载在线终端数据
export const loadRowData = async (row: any, type: any) => {
  if (type === "userList") {
    await loadUserListData(row);
  } else if (type === "apList") {
    await loadApListData(row);
  } else {
    console.error("未知的加载类型:", type);
  }
};

// 处理展开/收起行的变化
export const handleExpandChange = (row: any, expanded: boolean, expandedRowData: ExpandedRowData, dataType?: string) => {
  expandedRowData.userList = [];
  expandedRowData.apList = [];
  let type: string | undefined;

  if (dataType && dataType.trim() !== "") {
    // 如果传入了dataType参数，直接使用该参数
    type = dataType;
  } else if (row && row.supports) {
    // 如果没有传入dataType参数，则根据row.supports判断
    if (JSON.parse(row.supports).system.supports.includes("userList")) {
      type = "userList";
    } else if (JSON.parse(row.supports).system.supports.includes("apList")) {
      type = "apList";
    }
  }

  console.log("type: %s, expanded: %s", type, expanded);
  if (expanded) {
    if (row && row.status === 0) {
      return loadRowData(row, type).then(() => {
        // 更新响应式数据
        if (type === "userList") {
          expandedRowData.userList = Array.isArray(row.userList) ? row.userList : [];
        } else if (type === "apList") {
          expandedRowData.apList = Array.isArray(row.apList) ? row.apList : [];
        }
      });
    }
  }
  return Promise.resolve();
};

// 处理加载列表
export const handleLoadList = (
  row: any,
  type: string,
  expandedRows: any[],
  lastClickType: string,
  userListVisible: boolean,
  apListVisible: boolean,
  t: Function
) => {
  if (row.status === 1) {
    return {
      success: false,
      message: t("device.manageOfflineDeviceTip"),
      expandedRows,
      userListVisible,
      apListVisible,
      lastClickType
    };
  }

  console.log(
    `load${type.charAt(0).toUpperCase() + type.slice(1)}: %s expandedRows: %s`,
    JSON.stringify(row),
    JSON.stringify(expandedRows)
  );

  let newExpandedRows = [...expandedRows];
  let newUserListVisible = userListVisible;
  let newApListVisible = apListVisible;
  let newLastClickType = type;

  if (expandedRows.includes(row.id)) {
    if (lastClickType === type) {
      // 如果当前行已展开且是同一按钮重复点击，则收起
      newExpandedRows = [];
      if (type === "userList") {
        newUserListVisible = false;
      } else if (type === "apList") {
        newApListVisible = false;
      }
    } else {
      // 如果当前行已展开但点击了不同按钮，则切换视图
      if (type === "userList") {
        newUserListVisible = true;
        newApListVisible = false;
      } else if (type === "apList") {
        newUserListVisible = false;
        newApListVisible = true;
      }
    }
  } else {
    // 收起其他行，展开新行，并切换到指定视图
    newExpandedRows = [row.id];
    if (type === "userList") {
      newUserListVisible = true;
      newApListVisible = false;
    } else if (type === "apList") {
      newUserListVisible = false;
      newApListVisible = true;
    }
  }

  return {
    success: true,
    expandedRows: newExpandedRows,
    userListVisible: newUserListVisible,
    apListVisible: newApListVisible,
    lastClickType: newLastClickType
  };
};

export const saveTerminalConfig = async (params: { scope: { deviceId: string; row: any }; row: any }) => {
  try {
    console.log("保存终端配置参数:", JSON.stringify(params));

    // 确保 row 对象存在
    if (!params.row) {
      params.row = {};
    }

    // 处理 week 属性
    if (params.row.week) {
      // 如果 week 是数组，则将其转换为字符串
      if (Array.isArray(params.row.week)) {
        params.row.week = params.row.week.join(" ");
      }
    }

    const reqParams: Project.ReqConfigParams = {
      cmd: 6,
      deviceId: params.scope.deviceId,
      userId: useUserStore().userInfo.userId,
      data: {
        system: {
          userList: [params.row]
        }
      }
    };
    console.log("请求参数:", JSON.stringify(reqParams));
    const response = await pushDeviceConfigJwe(reqParams);
    if (response.code === "200") {
      console.log("保存成功");
    } else {
      console.error("保存失败:", response.msg);
    }
    return response;
  } catch (err) {
    console.error("保存终端配置时发生错误:", err);
    throw err;
  }
};

// 保存AP配置
export const saveApConfig = async (params: { scope: { deviceId: string; row: any }; row: any }) => {
  try {
    console.log("保存AP配置参数:", JSON.stringify(params));

    // 确保 row 对象存在
    if (!params.row) {
      params.row = {};
    }

    const reqParams: Project.ReqConfigParams = {
      cmd: 6,
      deviceId: params.scope.deviceId,
      userId: useUserStore().userInfo.userId,
      data: {
        system: {
          apList: [params.row]
        }
      }
    };
    console.log("请求参数:", JSON.stringify(reqParams));
    const response = await pushDeviceConfigJwe(reqParams);
    if (response.code === "200") {
      console.log("保存成功");
    } else {
      console.error("保存失败:", response.msg);
    }
    return response;
  } catch (err) {
    console.error("保存AP配置时发生错误:", err);
    throw err;
  }
};

// 打开终端管理抽屉
export const createTerminalDrawerParams = (
  title: string,
  scope: any,
  row: Partial<Project.ReqProjectParams> = {},
  getTableList?: () => void
): TerminalDrawerParams => {
  return {
    title,
    isView: title.includes("查看") || title.includes("view"),
    row: { ...row },
    api: saveTerminalConfig,
    scope: { ...scope.row },
    getTableList
  };
};

// 打开桥接客户端抽屉
export const createBridgeClientDrawerParams = (
  title: string,
  row: Partial<Project.ReqProjectParams> = {},
  editApi?: (params: any) => Promise<any>,
  getTableList?: () => void
): BridgeClientDrawerParams => {
  return {
    title,
    isView: title.includes("查看") || title.includes("view"),
    row: { ...row },
    api: title.includes("编辑") || title.includes("edit") ? editApi : undefined,
    getTableList
  };
};

// 打开AP管理抽屉
export const createApDrawerParams = (
  title: string,
  row: Partial<Project.ReqProjectParams> = {},
  parentRow: Partial<Project.ReqProjectParams> = {},
  isView: boolean = false,
  getTableList?: () => void
): ApDrawerParams => {
  return {
    title,
    isView,
    row: { ...row },
    parentRow: { ...parentRow },
    api: saveApConfig,
    getTableList
  };
};

// 处理抽屉关闭事件
export const handleDrawerClose = async (
  expandedRows: any[],
  expandedRowData: ExpandedRowData,
  userListVisible: boolean,
  apListVisible: boolean,
  getTableData?: any
) => {
  expandedRowData.userList = [];
  expandedRowData.apList = [];

  if (expandedRows.length > 0 && getTableData) {
    const expandedRow = getTableData().find((row: any) => row.id === expandedRows[0]);
    if (expandedRow) {
      try {
        const currentUserListVisible = userListVisible;
        const currentApListVisible = apListVisible;

        if (currentUserListVisible) {
          await loadUserListData(expandedRow);
          expandedRowData.userList = Array.isArray(expandedRow.userList) ? expandedRow.userList : [];
        } else if (currentApListVisible) {
          await loadApListData(expandedRow);
          expandedRowData.apList = Array.isArray(expandedRow.apList) ? expandedRow.apList : [];
        }

        return {
          userListVisible: currentUserListVisible,
          apListVisible: currentApListVisible,
          expandedRowData
        };
      } catch (error) {
        console.error("加载数据失败:", error);
        return {
          userListVisible: false,
          apListVisible: false,
          expandedRows: [],
          expandedRowData
        };
      }
    }
  }

  return {
    expandedRowData
  };
};
