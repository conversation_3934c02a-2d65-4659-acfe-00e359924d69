<template>
  <el-dialog
    v-model="drawerVisible"
    :destroy-on-close="true"
    :width="dialogWidth"
    :title="`${drawerProps.title}  ${$t('project.project')}`"
  >
    <el-form
      ref="ruleFormRef"
      label-width="120px"
      label-suffix=" :"
      :rules="rules"
      :disabled="drawerProps.isView"
      :model="drawerProps.row"
      hide-required-asterisk
      :label-position="labelPosition"
    >
      <el-form-item :label="$t('project.projectNameLabel')" prop="groupName">
        <el-input v-model="drawerProps.row.groupName" :placeholder="$t('project.projectNamePlaceholder')" clearable></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="drawerVisible = false">{{ $t("common.cancel") }}</el-button>
      <el-button v-show="!drawerProps.isView" type="primary" @click="handleSubmit">{{ $t("common.confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="ProjectDrawer">
import { ref, reactive, computed } from "vue";
import { User } from "@/api/interface";
import { ElMessage, FormInstance } from "element-plus";
import { useI18n } from "vue-i18n";
// 定义 emit 事件
const emit = defineEmits(["closeDrawer", "submitSuccess", "refreshTreeFilter"]);

const { t } = useI18n();

const rules = reactive({
  groupName: [{ required: true, max: 50, message: t("project.projectNameMaxLength"), trigger: "blur" }]
});

interface ProjectData extends Partial<User.ResUserList> {
  groupName?: string;
}

interface DrawerProps {
  title: string;
  isView: boolean;
  row: ProjectData;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const drawerVisible = ref(false);
const drawerProps = ref<DrawerProps>({
  isView: false,
  title: "",
  row: {}
});

// 响应式对话框宽度
const dialogWidth = computed(() => {
  return window.innerWidth <= 480 ? "95vw" : "450px";
});

// 响应式标签位置
const labelPosition = computed(() => {
  return window.innerWidth <= 480 ? "top" : "left";
});

// 接收父组件传过来的参数
const acceptParams = (params: DrawerProps) => {
  drawerProps.value = params;
  drawerVisible.value = true;
};

// 提交数据（新增/编辑）
const ruleFormRef = ref<FormInstance>();
const handleSubmit = () => {
  ruleFormRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      const response = await drawerProps.value.api!(drawerProps.value.row);
      if (response && response.code === "200") {
        ElMessage.success({
          message: `${drawerProps.value.title}成功！`
        });
        // 调用刷新父组件的 TreeFilter
        emit("refreshTreeFilter");
        drawerProps.value.getTableList && drawerProps.value.getTableList();
        drawerVisible.value = false;
      } else {
        ElMessage.error({
          message: response?.msg || `${drawerProps.value.title}失败！`
        });
      }
    } catch (error) {
      console.error(error);
      ElMessage.error({
        message: `${drawerProps.value.title}失败！`
      });
    }
  });
};

defineExpose({
  acceptParams
});
</script>

<style scoped lang="scss">
@media screen and (width <= 480px) {
  :deep(.el-dialog) {
    .el-dialog__header {
      padding: 12px 16px;
      .el-dialog__title {
        font-size: 14px;
        line-height: 1.3;
      }
    }
    .el-dialog__body {
      padding: 12px 16px;
    }
    .el-dialog__footer {
      padding: 8px 16px;
      text-align: center;
      .el-button {
        width: 80px;
        padding: 8px 16px;
        font-size: 13px;
        &:first-child {
          margin-right: 12px;
        }
      }
    }
    .el-form {
      .el-form-item {
        margin-bottom: 16px;
        .el-form-item__label {
          width: 100% !important;
          padding: 0 0 6px !important;
          margin-bottom: 0;
          font-size: 13px;
          line-height: 1.3;
          text-align: left !important;
        }
        .el-form-item__content {
          margin-left: 0 !important;
          .el-input {
            font-size: 14px;
            .el-input__inner {
              padding: 8px 12px;
              font-size: 14px;
            }
          }
        }
      }
    }
  }
}

@media screen and (width <= 320px) {
  :deep(.el-dialog) {
    .el-dialog__header {
      padding: 10px 12px;
      .el-dialog__title {
        font-size: 13px;
      }
    }
    .el-dialog__body {
      padding: 8px 12px;
    }
    .el-dialog__footer {
      padding: 6px 12px;
      .el-button {
        width: 70px;
        padding: 6px 12px;
        font-size: 12px;
      }
    }
  }
}
</style>
