# DeviceConfigDrawer 重构问题修复总结

## 修复的问题

### 1. WAN组件不显示问题
**问题原因**: WANSettings.vue组件的显示条件错误
- **修复前**: `v-if="deviceSupports?.wan"`
- **修复后**: `v-if="deviceSupports?.network?.supports?.includes('wan')"`

**数据访问路径修复**:
- **修复前**: `deviceConfig.wan?.proto`
- **修复后**: `deviceConfig.network?.wan?.[0]?.proto`

### 2. 国际化缺失问题
**缺失的翻译键**:
- `device.wpa2`: "WPA2加密"
- `device.none`: "不加密"  
- `device.clearPassword`: "清空密码"
- `device.connectionType`: "连接类型"
- `device.connectionTypePlaceholder`: "请选择连接类型"
- `device.ipAddress`: "IP地址"
- `device.ipAddressPlaceholder`: "请输入IP地址"
- `device.netmaskPlaceholder`: "请输入子网掩码"
- `device.gatewayPlaceholder`: "请输入网关地址"
- `device.gateway`: "网关"
- `device.username`: "用户名"
- `device.usernamePlaceholder`: "请输入用户名"
- `device.passwordPlaceholder`: "请输入密码"
- `device.mtu`: "MTU"
- `device.mtuPlaceholder`: "请输入MTU值"
- `device.primaryDns`: "首选DNS"
- `device.primaryDnsPlaceholder`: "请输入首选DNS"
- `device.secondaryDns`: "备选DNS"
- `device.secondaryDnsPlaceholder`: "请输入备选DNS"

### 3. 其他组件类似问题修复

#### LANSettings.vue
- **显示条件**: `deviceSupports?.lan` → `deviceSupports?.network?.supports?.includes('lan')`
- **数据路径**: `deviceConfig.lan?.ipaddr` → `deviceConfig.network?.lan?.ipaddr`

#### DHCPSettings.vue  
- **显示条件**: `deviceSupports?.dhcp` → `deviceSupports?.network?.supports?.includes('dhcp')`
- **数据路径**: `deviceConfig.dhcp?.enabled` → `deviceConfig.network?.dhcp?.enabled`

#### DNSSettings.vue
- **显示条件**: `deviceSupports?.dns` → `deviceSupports?.network?.supports?.includes('dns')`
- **数据路径**: `deviceConfig.dns?.enabled` → `deviceConfig.network?.dns?.enabled`

#### BridgeWiFiSettings.vue
- **显示条件**: `deviceSupports?.bridge_wifi` → `deviceSupports?.network?.supports?.includes('brAp')`
- **数据路径**: `deviceConfig.bridge_wifi?.enabled` → `deviceConfig.network?.brAp?.enabled`

### 4. 事件处理修复
**WAN网关字段名修复**:
- **修复前**: `handleWanUpdate('gateway', $event)`
- **修复后**: `handleWanUpdate('gawa', $event)`

## 修复的文件列表

1. `src/languages/modules/zh.ts` - 添加缺失的国际化翻译
2. `src/views/project/components/DeviceConfigDrawer/WANSettings.vue` - 修复显示条件和数据访问路径
3. `src/views/project/components/DeviceConfigDrawer/LANSettings.vue` - 修复显示条件和数据访问路径
4. `src/views/project/components/DeviceConfigDrawer/DHCPSettings.vue` - 修复显示条件和数据访问路径
5. `src/views/project/components/DeviceConfigDrawer/DNSSettings.vue` - 修复显示条件和数据访问路径
6. `src/views/project/components/DeviceConfigDrawer/BridgeWiFiSettings.vue` - 修复显示条件和数据访问路径
7. `src/views/project/components/DeviceConfigDrawer.vue` - 修复WAN网关字段名

## 数据结构说明

### 正确的设备支持结构
```typescript
deviceSupports = {
  wireless: {
    supports: ["radio0", "radio1", "guest", "wifiTime"]
  },
  network: {
    supports: ["wan", "lan", "dhcp", "dns", "brAp", "mtu", "stp", "igmp_snooping"]
  },
  system: {
    supports: ["led", "reboot", "swPort", "swPoe"]
  }
}
```

### 正确的设备配置结构
```typescript
deviceConfig = {
  network: {
    wan: [{ proto: "dhcp", ipaddr: "", netmask: "", gawa: "", username: "", password: "", dns1: "", dns2: "" }],
    lan: { ipaddr: "", netmask: "", gateway: "", mtu: 1500, stp: 0, igmp_snooping: 0 },
    dhcp: { enabled: 1, start: "", end: "", leasetime: "24h", gateway: "", netmask: "", dns1: "", dns2: "" },
    dns: { enabled: 1, server1: "", server2: "", cache: 1 },
    brAp: { enabled: 1, ssid: "", key: "", encryption: "wpa2", channel: 0, mode: 1, txpower: 100, hidden: 0, isolation: 0 }
  },
  wireless: {
    radio0: { disabled: 0, ssid: "", key: "", hidden: 0, channel: 0, txpower: 100 },
    radio1: { disabled: 0, ssid: "", key: "", hidden: 0, channel: 0, txpower: 100 },
    guest: { disabled: 0, ssid: "", key: "", hidden: 0, rate: 0, wifiTime: 0 },
    wifiTime: { enabled: 0, week: "", beginTime: "", endTime: "" }
  },
  system: {
    led: { mode: "on", beginTime: "", endTime: "" },
    reboot: { enabled: 0, week: "", time: "", rateDelay: 0 }
  }
}
```

## 验证步骤

1. 检查WAN设置是否正常显示
2. 检查国际化翻译是否正确显示
3. 检查所有网络设置组件是否正常工作
4. 检查数据绑定是否正确
5. 检查事件处理是否正常

## 注意事项

1. WAN数据是数组格式，需要访问第一个元素 `wan[0]`
2. 网关字段在WAN中使用 `gawa` 而不是 `gateway`
3. 桥接WiFi使用 `brAp` 字段而不是 `bridge_wifi`
4. 设备支持检查需要使用正确的层级结构
