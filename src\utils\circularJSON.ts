/**
 * 安全地将对象转换为JSON字符串，处理循环引用
 * @param obj 要转换的对象
 * @param replacer 替换函数或数组
 * @param space 缩进空格数
 * @returns JSON字符串
 */
export function safeStringify(obj: any, replacer?: string[] | null, space?: number): string {
  const seen = new WeakSet();
  return JSON.stringify(
    obj,
    (key, value) => {
      // 如果是对象且不是null
      if (typeof value === "object" && value !== null) {
        // 如果是循环引用
        if (seen.has(value)) {
          // 如果replacer是数组且包含当前key，则返回"[Circular]"
          if (Array.isArray(replacer) && replacer.includes(key)) {
            return "[Circular]";
          }
          // 否则跳过该属性
          return undefined;
        }
        // 将对象添加到已见集合中
        seen.add(value);
      }
      return value;
    },
    space
  );
}
