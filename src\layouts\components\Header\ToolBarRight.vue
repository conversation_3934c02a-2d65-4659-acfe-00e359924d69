<template>
  <div class="tool-bar-ri">
    <div class="header-icon">
      <template v-if="screenWidth > 600">
        <AssemblySize id="assemblySize" />
        <Language id="language" />
        <SearchMenu id="searchMenu" />
        <DarkMode id="darkMode" />
        <ThemeSetting id="themeSetting" />
        <Message id="message" />
        <Fullscreen id="fullscreen" />
      </template>
      <template v-else-if="screenWidth > 400">
        <SearchMenu id="searchMenu" />
        <Message id="message" />
        <el-dropdown trigger="click">
          <i class="iconfont icon-gengduo toolBar-icon"></i>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item><AssemblySize id="assemblySize" /></el-dropdown-item>
              <el-dropdown-item><Language id="language" /></el-dropdown-item>
              <el-dropdown-item><DarkMode id="darkMode" /></el-dropdown-item>
              <el-dropdown-item><ThemeSetting id="themeSetting" /></el-dropdown-item>
              <el-dropdown-item><Fullscreen id="fullscreen" /></el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>
      <template v-else>
        <SearchMenu id="searchMenu" />
        <el-dropdown trigger="click">
          <i class="iconfont icon-gengduo toolBar-icon"></i>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item><Message id="message" /></el-dropdown-item>
              <el-dropdown-item><AssemblySize id="assemblySize" /></el-dropdown-item>
              <el-dropdown-item><Language id="language" /></el-dropdown-item>
              <el-dropdown-item><DarkMode id="darkMode" /></el-dropdown-item>
              <el-dropdown-item><ThemeSetting id="themeSetting" /></el-dropdown-item>
              <el-dropdown-item><Fullscreen id="fullscreen" /></el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>
    </div>
    <span class="username" v-if="screenWidth > 480">{{ username }}</span>
    <Avatar />
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from "vue";
import { useUserStore } from "@/stores/modules/user";
import AssemblySize from "./components/AssemblySize.vue";
import Language from "./components/Language.vue";
import SearchMenu from "./components/SearchMenu.vue";
import DarkMode from "./components/DarkMode.vue";
import ThemeSetting from "./components/ThemeSetting.vue";
import Message from "./components/Message.vue";
import Fullscreen from "./components/Fullscreen.vue";
import Avatar from "./components/Avatar.vue";

const userStore = useUserStore();
const username = computed(() => userStore.userInfo.name);

const screenWidth = ref(window.innerWidth);
const handleResize = () => {
  screenWidth.value = window.innerWidth;
};
onMounted(() => {
  window.addEventListener("resize", handleResize);
});
onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
});
</script>

<style scoped lang="scss">
.tool-bar-ri {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-right: 25px;
  .header-icon {
    display: flex;
    align-items: center;
    & > * {
      margin-left: 21px;
      color: var(--el-header-text-color);
    }
  }
  .username {
    margin: 0 20px;
    font-size: 15px;
    font-weight: bold;
    color: var(--el-header-text-color);
  }
}

// 统一的工具栏图标hover效果
:deep(.toolBar-icon) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  font-size: 18px;
  color: var(--el-header-text-color);
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  &:hover {
    color: var(--el-color-primary);
    background-color: var(--el-color-primary-light-9);
    box-shadow: 0 4px 12px rgb(64 158 255 / 20%);
    transform: scale(1.1);
  }
  &:active {
    transform: scale(0.95);
  }
}
</style>
