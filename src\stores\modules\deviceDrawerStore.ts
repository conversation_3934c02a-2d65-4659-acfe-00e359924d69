import { defineStore } from "pinia";
import { ref } from "vue";

export const useDeviceDrawerStore = defineStore("deviceDrawer", () => {
  const loadedTabs = ref(new Set<string>());

  const addLoadedTab = (tabName: string) => {
    loadedTabs.value.add(tabName);
  };

  const hasLoadedTab = (tabName: string) => {
    return loadedTabs.value.has(tabName);
  };

  const clearLoadedTabs = () => {
    loadedTabs.value.clear();
  };

  return {
    loadedTabs,
    addLoadedTab,
    hasLoadedTab,
    clearLoadedTabs
  };
});
