import { ref, nextTick } from "vue";
import { getDeviceConfigJwe, pushDeviceConfigJwe } from "@/api/modules/project";
import { useUserStore } from "@/stores/modules/user";
import { Project } from "@/api/interface/project";
import { ElMessage } from "element-plus";
import { deleteRnetDevice, saveRnetDevice } from "@/api/modules/rnet";
import * as echarts from "echarts";

const linkStyleSuccess = {
  opacity: 0.9,
  width: 3,
  color: "#00C6FF",
  // 设置弧度（正值表示向外弧线，负值表示反向弧线）
  curveness: 0,
  emphasis: { focus: "adjacency", edgeSymbolSize: [0, 20] }
};

const linkStyleProcess = {
  opacity: 0.9,
  width: 3,
  color: "#FFBF00",
  // 设置弧度（正值表示向外弧线，负值表示反向弧线）
  curveness: 0,
  emphasis: { focus: "adjacency", edgeSymbolSize: [0, 20] }
};

const linkStyleFailed = {
  opacity: 0.9,
  width: 3,
  color: "#FF0000",
  // 设置弧度（正值表示向外弧线，负值表示反向弧线）
  curveness: 0,
  emphasis: { focus: "adjacency", edgeSymbolSize: [0, 20] }
};

export const getLineStyle = (status: string) => {
  switch (status) {
    case "SUCCESS":
      return linkStyleSuccess;
    case "CONNECTING":
      return linkStyleProcess;
    case "FAIL":
      return linkStyleFailed;
    default:
      return linkStyleSuccess;
  }
};

/**
 * 更新图表的坐标轴范围，保证图表和效果共用同一坐标系
 */
export function updateAxisRange(chartInstance: echarts.ECharts): Promise<void> {
  return new Promise<void>(resolve => {
    if (!chartInstance) {
      console.warn("图表实例为空，无法更新坐标轴范围");
      resolve();
      return;
    }

    try {
      // 防御性检查图表实例是否有效
      try {
        const dom = chartInstance.getDom();
        if (!dom) {
          console.warn("Chart DOM element not found in updateAxisRange");
          resolve();
          return;
        }
      } catch (e) {
        console.warn("Chart instance has been disposed in updateAxisRange");
        resolve();
        return;
      }

      const width = chartInstance.getWidth();
      const height = chartInstance.getHeight();

      // 使用 nextTick 确保不在主进程中调用 setOption
      nextTick(() => {
        try {
          chartInstance.setOption(
            {
              xAxis: { min: 0, max: width, show: false },
              yAxis: { min: 0, max: height, show: false }
            },
            { silent: true, lazyUpdate: true }
          );
          console.log(`Updated axis range to ${width}x${height}`);
        } catch (error) {
          console.error("Error in setOption during updateAxisRange:", error);
        }
        resolve();
      });
    } catch (error) {
      console.error("Error in updateAxisRange:", error);
      resolve();
    }
  });
}

// updateLinesEffect：利用 graph 系列的 getItemLayout 返回的像素坐标采样圆弧路径
// 根据 link.direction（"normal"或"reverse"）决定采样顺序
export const sampleArc = (start, end, center, segments = 50) => {
  let angle1 = Math.atan2(start[1] - center[1], start[0] - center[0]);
  let angle2 = Math.atan2(end[1] - center[1], end[0] - center[0]);
  let delta = angle2 - angle1;
  if (delta > Math.PI) {
    delta -= 2 * Math.PI;
  } else if (delta < -Math.PI) {
    delta += 2 * Math.PI;
  }
  const points = [];
  // 计算平均半径
  const r1 = Math.hypot(start[0] - center[0], start[1] - center[1]);
  const r2 = Math.hypot(end[0] - center[0], end[1] - center[1]);
  const r = (r1 + r2) / 2;
  for (let i = 0; i <= segments; i++) {
    const angle = angle1 + (delta * i) / segments;
    const x = center[0] + r * Math.cos(angle);
    const y = center[1] + r * Math.sin(angle);
    points.push([x, y]);
  }
  return points;
};

// 单位转换函数（B转换为 KB/MB）
export const convertBytes = bytes => {
  if (bytes <= 0) return "0 B";
  if (bytes < 1024) return bytes.toFixed(2) + " B";
  else if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + " KB";
  else return (bytes / (1024 * 1024)).toFixed(2) + " MB";
};

// 修改节点 label formatter，显示节点名称和速率（rxByte+txByte）
// 导入全局状态以获取当前主题
import { useGlobalStore } from "@/stores/modules/global";

export const labelFormatter = params => {
  const globalStore = useGlobalStore();
  const isDark = globalStore.isDark;

  const name = params.name;
  const rate = params.data.rate || "";

  // 根据当前主题返回不同的样式
  if (isDark) {
    // 暗黑模式下使用更高对比度的颜色
    return `{a|${name}}\n{b|${rate}}`;
  } else {
    // 明亮模式下使用深色文字
    return `{a|${name}}\n{b|${rate}}`;
  }
};
export let addRnetDeviceVisible = ref(false); // 添加组网设备弹窗显示
export const configRnetDeviceVisible = ref(false); // 配置组网设备弹窗显示

// 添加组网设备弹窗显示
export const addRnetDevice = () => {
  addRnetDeviceVisible.value = true;
};

// 计算二次贝塞尔曲线上若干采样点
export function sampleQuadraticBezier(start, end, curveness, segments = 50) {
  // start: [x0, y0], end: [x2, y2]
  // 计算中点
  const mid = [(start[0] + end[0]) / 2, (start[1] + end[1]) / 2];
  // 计算向量（start -> end）及其长度
  const dx = end[0] - start[0];
  const dy = end[1] - start[1];
  const len = Math.sqrt(dx * dx + dy * dy);
  // 计算垂直方向单位向量
  const perp = [-dy / len, dx / len];
  // 控制点 = 中点 + curveness * len * 垂直向量
  const cp = [mid[0] + curveness * len * perp[0], mid[1] + curveness * len * perp[1]];
  // 采样贝塞尔曲线：B(t) = (1-t)^2 * P0 + 2(1-t)t * P1 + t^2 * P2
  const points = [];
  for (let i = 0; i <= segments; i++) {
    const t = i / segments;
    const oneMinusT = 1 - t;
    const x = oneMinusT * oneMinusT * start[0] + 2 * oneMinusT * t * cp[0] + t * t * end[0];
    const y = oneMinusT * oneMinusT * start[1] + 2 * oneMinusT * t * cp[1] + t * t * end[1];
    points.push([x, y]);
  }
  return points;
}

// 获取设备组网信息，请求状态
export const getRnetDeviceStatusJwe = async (params: { deviceId: string }) => {
  const reqParams: Project.ReqConfigParams = {
    cmd: 4,
    deviceId: params.deviceId,
    userId: useUserStore().userInfo.userId,
    data: {
      system: ["rNet"]
    }
  };
  return getDeviceConfigJwe(reqParams, false);
};

// 获取设备组网信息，请求配置
export const getRnetDeviceConfigJwe = async (params: { deviceId: string }) => {
  const reqParams: Project.ReqConfigParams = {
    cmd: 10,
    deviceId: params.deviceId,
    userId: useUserStore().userInfo.userId,
    data: {
      system: ["rNet"]
    }
  };
  console.log("getRnetDeviceConfigJwe:---", reqParams);
  return getDeviceConfigJwe(reqParams);
};

export const submitRnetDeviceConfig = async (params: { deviceId: string; config: any }) => {
  const reqParams: Project.ReqConfigParams = {
    cmd: 6,
    deviceId: params.deviceId,
    userId: useUserStore().userInfo.userId,
    data: params.config
  };
  console.log("submitRnetDeviceConfig:---", JSON.stringify(reqParams));
  return pushDeviceConfigJwe(reqParams);
};

export const selectedDevices = ref<any[]>([]);

export const handleChecked = (device: any) => {
  // 如果selectedDevices中已存在设备的国家码不一致，则不允许勾选
  // 如果 deviceId 是 15 位，则取最后一位作为国家码符号，否则默认为 0
  // const countySymbol = ref(null);
  // let first = selectedDevices.value[0];
  // if (selectedDevices.value.length > 0) {
  //   if (first.deviceId.length === 15) {
  //     countySymbol.value = parseInt(first.deviceId.slice(-1));
  //   } else {
  //     countySymbol.value = 0;
  //   }
  // }
  // if (!device && device.checked && countySymbol.value !== null) {
  //   if (device.deviceId.length === 15 ? parseInt(device.deviceId.slice(-1)) : 0 !== countySymbol.value) {
  //     ElMessage({
  //       message: t("device.selectDeviceTip"),
  //       type: "warning"
  //     });
  //     device.checked = false;
  //     return;
  //   }
  // }

  if (device.checked) {
    selectedDevices.value.push(device);
  } else {
    selectedDevices.value = selectedDevices.value.filter(item => item.deviceId !== device.deviceId);
  }
  console.log("selectedDevices:---", JSON.stringify(selectedDevices.value));
};

export const closeRnetDevices = () => {
  addRnetDeviceVisible.value = false;
  selectedDevices.value = [];
};

export const addRnetDevices = async (params: { rnetId: any; deviceIds?: any[]; peer?: any[] }, t: Function) => {
  if (selectedDevices.value.length === 0) {
    ElMessage({
      message: t("device.selectDeviceTip"),
      type: "warning"
    });
    return;
  }
  const addDeviceIds = selectedDevices.value.map(item => item.deviceId);
  params.deviceIds = addDeviceIds;
  params.peer = params.deviceIds.map(deviceId => ({ deviceId }));
  console.log("deviceIds:---", JSON.stringify(params.peer));
  // 获取组网信息
  const resultData = await saveRnetDevice({
    rnetId: params.rnetId,
    peer: params.peer
  });
  console.log("resultData:---", JSON.stringify(resultData));
  return resultData;
};

export const removeRnetDevice = async (params: { rnetId: string; peer: any[] }) => {
  return await deleteRnetDevice(params);
};
export interface Device {
  deviceId: string;
  status: number;
  supports: string[];
  rnetId: string;
  deviceName: string;
  deviceType?: string;
  data?: any;
  rate?: string;
  checked?: boolean;
}

// 图表相关函数
export const updateChartIfValid = async (
  chartInstance: echarts.ECharts | null,
  option: any,
  devices: any[],
  globalPeers: any
) => {
  // 声明变量在函数开始，确保在整个函数中都可用
  const deviceNameMap = new Map();
  let links: any[] = [];

  try {
    // 检查图表实例是否存在且有效
    let chartValid = false;
    if (chartInstance) {
      try {
        const dom = chartInstance.getDom();
        if (dom) {
          chartValid = true;
        } else {
          console.warn("Chart DOM element not found in updateChartIfValid");
        }
      } catch (e) {
        console.warn("Chart instance has been disposed in updateChartIfValid");
      }
    } else {
      // 不输出警告，因为这是正常情况
      console.log("Chart instance not available yet, skipping chart update");
    }

    // 如果图表实例无效，不尝试更新图表
    if (!chartValid) {
      return;
    }

    // 检查选项是否有效
    if (!option || !option.series || !option.series[0] || !option.series[0].links) {
      console.warn("Invalid chart options in updateChartIfValid");
      return;
    }

    // 初始化连线数组
    links = option.series[0].links;

    // 防御性地遍历设备
    if (!devices || !Array.isArray(devices)) {
      console.warn("Invalid devices data in updateChartIfValid");
      return;
    }

    // 存储所有响应信息
    const resultsForPeers: any[] = [];

    // 获取设备数据
    for (const item of devices) {
      try {
        if (!item || !item.deviceId) continue;

        const name = item.deviceName || "";
        deviceNameMap.set(item.deviceId, name);

        const rnetConfig = await getRnetDeviceStatusJwe({ deviceId: item.deviceId });
        if (rnetConfig && rnetConfig.code === "200") {
          resultsForPeers.push(rnetConfig);
        }
      } catch (error) {
        console.error(`Error fetching rnet config for device ${item?.deviceId}:`, error);
      }
    }

    // 如果没有获取到数据，直接返回
    if (!resultsForPeers.length) {
      return;
    }

    // 初始化新的节点数据
    const newPeers: any = {};

    // 首先构建设备名称映射
    devices.forEach(device => {
      if (device && device.deviceId) {
        const name = device.deviceName || "";
        deviceNameMap.set(device.deviceId, name);
      }
    });

    // 遍历所有设备 rnetConfig 结果，存入 newPeers
    resultsForPeers.forEach(resItem => {
      if (resItem && resItem.data && resItem.data.system.rNet.peer) {
        resItem.data.system.rNet.peer.forEach((p: any) => {
          // 累加相同 deviceId 的流量数据
          if (!newPeers[p.deviceId]) {
            newPeers[p.deviceId] = { rxByte: 0, txByte: 0 };
          }
          newPeers[p.deviceId].rxByte = Number(p.rxByte) || 0;
          newPeers[p.deviceId].txByte = Number(p.txByte) || 0;
          newPeers[p.deviceId].time = Number(p.time) || 0;
          newPeers[p.deviceId].status = String(p.status) || 0;
        });
        // 处理 peer 数据
        const peers = resItem.data.system.rNet.peer || [];
        // 更新连线数据
        peers.forEach((peer: any) => {
          if (resItem.deviceId === peer.deviceId) return;
          const sourceName = deviceNameMap.get(resItem.deviceId);
          const targetName = deviceNameMap.get(peer.deviceId);

          // 确保两个节点都存在
          if (!sourceName || !targetName) return;

          const [s, t] = [sourceName, targetName].sort();
          const linkKey = `${s}-${t}`;

          // 检查是否已存在相同的连接
          const existingLink = links.find(link => {
            const [ls, lt] = [link.source, link.target].sort();
            return `${ls}-${lt}` === linkKey;
          });

          // 检查是否已经存在相同的链接
          if (!existingLink) {
            console.log(`添加新连接: ${linkKey}`);
            links.push({
              source: sourceName,
              target: targetName,
              direction: "normal",
              sourceDeviceId: resItem.deviceId,
              targetDeviceId: peer.deviceId,
              lineStyle: getLineStyle(peer.status),
              linkKey
            });
          } else {
            console.log(`更新已存在的连接: ${linkKey}`);
            existingLink.lineStyle = getLineStyle(peer.status);
          }
        });
      }
    });

    // 更新每个节点的速率
    devices.forEach(device => {
      const rateData = newPeers[device.deviceId];
      const oldPeer = globalPeers[device.deviceId];
      let rateStr = "0 B/s";
      if (rateData) {
        // 计算速率差值
        const rxDiff = rateData.rxByte - (oldPeer?.rxByte || 0);
        const txDiff = rateData.txByte - (oldPeer?.txByte || 0);
        const timeDiff = rateData.time - (oldPeer?.time || 0);

        // 确保时间差不为0,并且流量差值为正数
        if (timeDiff > 0) {
          const total = Math.max(0, (rxDiff + txDiff) / timeDiff);
          if (isNumber(total)) {
            rateStr = convertBytes(total) + "/s";
          }
        }
      }
      device.rate = rateStr;
      option.series[0].data.forEach((node: any) => {
        if (node.deviceId === device.deviceId) {
          node.rate = rateStr;
        }
      });
    });

    try {
      // 检查连线数据是否有效
      if (!option.series[0].links || !Array.isArray(option.series[0].links)) {
        console.warn("Invalid links data");
        return;
      }

      // 更新连线方向
      option.series[0].links.forEach((link: any) => {
        try {
          if (!link || !link.sourceDeviceId) return;

          link.direction = "normal"; // 默认

          const peer = newPeers[link.sourceDeviceId];
          const oldPeer = globalPeers[link.sourceDeviceId];

          if (peer && oldPeer) {
            const txDiff = Number(peer.txByte) - (oldPeer.txByte || 0);
            const rxDiff = Number(peer.rxByte) - (oldPeer.rxByte || 0);
            link.direction = txDiff > rxDiff ? "normal" : "reverse";
          }
        } catch (error) {
          console.error(`Error updating link direction for source ${link?.sourceDeviceId}:`, error);
        }
      });

      // 使用 setOption 局部更新，避免重布局
      if (chartInstance) {
        try {
          // 防御性检查图表实例是否已被销毁
          try {
            const dom = chartInstance.getDom();
            if (!dom) {
              console.warn("Chart DOM element not found before setOption");
              return;
            }
          } catch (e) {
            console.warn("Chart instance has been disposed before setOption");
            return;
          }

          await nextTick();

          // 再次检查图表实例是否存在且有效
          if (!chartInstance) return;

          try {
            const dom = chartInstance.getDom();
            if (!dom) return;
          } catch (e) {
            return;
          }

          try {
            await chartInstance.setOption(
              {
                series: [
                  {
                    data: option.series[0].data, // 仅更新数据
                    links: option.series[0].links // 仅更新数据
                  }
                ]
              },
              { lazyUpdate: true }
            );
            await updateLinesEffect(chartInstance, option); // 异步更新连线
          } catch (error) {
            console.error("Error in setOption:", error);
          }
        } catch (error) {
          console.error("Error updating chart with new data:", error);
        }
      }
    } catch (error) {
      console.error("Error in link update process:", error);
    }

    // 遍历所有设备 rnetConfig 结果，存入 globalPeers
    resultsForPeers.forEach(resItem => {
      if (resItem && resItem.data && resItem.data.system.rNet.peer) {
        resItem.data.system.rNet.peer.forEach((p: any) => {
          // 累加相同 deviceId 的流量数据
          if (!globalPeers[p.deviceId]) {
            globalPeers[p.deviceId] = { rxByte: 0, txByte: 0 };
          }
          globalPeers[p.deviceId].rxByte = Number(p.rxByte) || 0;
          globalPeers[p.deviceId].txByte = Number(p.txByte) || 0;
          globalPeers[p.deviceId].time = Number(p.time) || 0;
        });
      }
    });
  } catch (error) {
    console.error("Error in updateChartIfValid:", error);
  }
};

// 更新图表效果，包括文字颜色和背景色
export const updateLinesEffect = async (chartInstance: echarts.ECharts, option: any) => {
  try {
    if (!chartInstance) {
      console.log("Chart instance is not initialized yet in updateLinesEffect");
      return;
    }

    // 防御性检查图表实例是否已被销毁
    try {
      const dom = chartInstance.getDom();
      if (!dom) {
        console.log("Chart DOM element not found in updateLinesEffect");
        return;
      }
    } catch (e) {
      console.log("Chart instance has been disposed in updateLinesEffect");
      return;
    }

    await updateAxisRange(chartInstance);

    // 再次检查 chartInstance 是否存在且有效
    if (!chartInstance) return;

    try {
      const dom = chartInstance.getDom();
      if (!dom) return;
    } catch (e) {
      return;
    }

    // 使用防御性编程获取图表模型
    let graphSeriesModel;
    try {
      graphSeriesModel = (chartInstance as any).getModel().getSeriesByIndex(0);
    } catch (e) {
      console.warn("Failed to get graph series model:", e);
      return;
    }

    if (!graphSeriesModel) {
      console.warn("Graph series model not found");
      return;
    }

    // 获取图表数据
    let graphData;
    try {
      graphData = graphSeriesModel.getData();
    } catch (e) {
      console.warn("Failed to get graph data:", e);
      return;
    }

    const nodesPos: Array<{ name: string; x: number; y: number }> = [];

    // 防御性地遍历数据
    try {
      for (let i = 0; i < graphData.count(); i++) {
        const rawItem = graphData.getRawDataItem(i) as any;
        const pos = graphData.getItemLayout(i) as [number, number];
        if (rawItem && pos) {
          nodesPos.push({ name: rawItem.name, x: pos[0], y: pos[1] });
        }
      }
    } catch (e) {
      console.warn("Error while processing node positions:", e);
      return;
    }

    // 检查节点数量
    if (nodesPos.length <= 1) {
      // 这是正常情况，当图表初始化或清空时
      console.log("Not enough nodes to create lines, initializing empty lines series");

      // 即使没有足够的节点，也初始化空的 lines 序列
      // 使用 nextTick 确保 setOption 不在主进程中调用
      await nextTick();

      try {
        // 完整更新 lines 序列，包括所有属性，但使用空数据
        await chartInstance?.setOption(
          {
            series: [
              {
                id: "linesSeries",
                type: "lines",
                coordinateSystem: "cartesian2d",
                polyline: true,
                zlevel: 1,
                silent: true, // 禁用交互，避免干扰主图表
                animation: true,
                animationDuration: 1000,
                animationEasing: "linear",
                effect: {
                  show: true,
                  period: 4,
                  trailLength: 0.1,
                  symbol: "circle",
                  symbolSize: 5,
                  loop: true
                },
                lineStyle: {
                  color: "#ff7f0e",
                  width: 0,
                  curveness: 0,
                  opacity: 0.6
                },
                data: [] // 空数据
              }
            ]
          },
          { lazyUpdate: true, silent: true }
        );
        console.log("Empty lines series initialized successfully");
      } catch (error) {
        console.error("Error initializing empty lines series:", error);
      }

      return;
    }

    // 构造 lines 数据：根据每个 link 的 direction 决定采样顺序
    const links = option.series[0].links || [];
    console.log(`Processing ${links.length} links for lines effect`);

    // 检查 links 数组是否有效
    if (!Array.isArray(links) || links.length === 0) {
      console.log("No links available for lines effect");
      return;
    }

    // 输出所有节点的位置信息以便调试
    console.log(`Available nodes positions: ${nodesPos.map(n => n.name).join(", ")}`);

    // 输出所有连线的信息以便调试
    links.forEach((link, index) => {
      console.log(`Link ${index}: ${link.source} -> ${link.target} (${link.direction || "normal"})`);
    });

    const linesData = links
      .map((link, index) => {
        // 防御性检查 link 是否有效
        if (!link || typeof link !== "object") {
          console.warn(`Invalid link at index ${index}`);
          return null;
        }

        // 防御性检查 source 和 target 是否存在
        if (!link.source || !link.target) {
          console.warn(`Link at index ${index} missing source or target`);
          return null;
        }

        const sourceNode = nodesPos.find(n => n.name === link.source);
        const targetNode = nodesPos.find(n => n.name === link.target);

        // 输出节点查找结果以便调试
        if (!sourceNode) {
          console.warn(`Source node "${link.source}" not found for link ${index}`);
        }
        if (!targetNode) {
          console.warn(`Target node "${link.target}" not found for link ${index}`);
        }

        if (sourceNode && targetNode) {
          let coords;
          // 默认使用 normal 方向，除非明确指定为 reverse
          if (link.direction === "reverse") {
            // 反向：从 target 到 source
            coords = [
              [targetNode.x, targetNode.y],
              [sourceNode.x, sourceNode.y]
            ];
          } else {
            // 正向：从 source 到 target
            coords = [
              [sourceNode.x, sourceNode.y],
              [targetNode.x, targetNode.y]
            ];
          }
          return { coords };
        }
        return null;
      })
      .filter(item => item !== null);

    // 使用 nextTick 确保 setOption 不在主进程中调用
    await nextTick();

    // 检查 linesData 是否有效
    if (!linesData || linesData.length === 0) {
      console.log("No valid lines data to update");
      return;
    }

    console.log(`Updating lines effect with ${linesData.length} lines`);

    try {
      // 完整更新 lines 序列，包括所有属性
      await chartInstance?.setOption(
        {
          series: [
            {
              id: "linesSeries",
              type: "lines",
              coordinateSystem: "cartesian2d",
              polyline: true,
              zlevel: 1,
              silent: true, // 禁用交互，避免干扰主图表
              animation: true,
              animationDuration: 1000,
              animationEasing: "linear",
              effect: {
                show: true,
                period: 4,
                trailLength: 0.1,
                symbol: "circle",
                symbolSize: 5,
                loop: true
              },
              lineStyle: {
                color: "#ff7f0e",
                width: 0,
                curveness: 0,
                opacity: 0.6
              },
              data: linesData
            }
          ]
        },
        { lazyUpdate: true, silent: true }
      );
      console.log("Lines effect updated successfully");
    } catch (error) {
      console.error("Error updating lines effect:", error);
    }
  } catch (error) {
    console.error("Error in updateLinesEffect:", error);
  }
};

// 判断是否为数字
export const isNumber = (val: unknown): val is number => {
  return typeof val === "number" && !isNaN(val);
};
