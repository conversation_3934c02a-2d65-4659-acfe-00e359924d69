import { ElNotification } from "element-plus";

/**
 * @description 全局代码错误捕捉
 * */
const errorHandler = (error: any) => {
  // 过滤 HTTP 请求错误
  if (error.status || error.status == 0) return false;
  let errorMap: { [key: string]: string } = {
    InternalError: "Javascript引擎内部错误",
    ReferenceError: "未找到对象",
    TypeError: "使用了错误的类型或对象",
    RangeError: "使用内置对象时，参数超范围",
    SyntaxError: "语法错误",
    EvalError: "错误的使用了Eval",
    URIError: "URI错误"
  };

  let errorName = error?.name ? errorMap[error.name] || "未知错误" : "未知错误";
  let errorMessage = error?.message || "未知错误信息";
  let stackInfo = error?.stack || "";

  // 从堆栈信息中提取出错的具体位置
  let locationMatch = stackInfo.match(/at\s+(.*)\s+\((.*):(\d+):(\d+)\)/) || stackInfo.match(/at\s+(.*):(\d+):(\d+)/);
  let location = locationMatch ? locationMatch[0] : "未知位置";

  // 显示通知
  ElNotification({
    title: errorName,
    message: `${errorMessage}\n位置: ${location}`,
    type: "error",
    duration: 5000
  });

  // 记录到控制台
  console.error("捕获到的错误：", error);
  console.error("出错位置：", location);
};

export default errorHandler;
