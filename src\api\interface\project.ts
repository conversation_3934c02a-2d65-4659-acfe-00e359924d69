// 项目管理模块
import { ReqPage } from "@/api/interface/index";

export namespace Project {
  export interface ReqProjectParams extends ReqPage {
    groupId: number;
    groupName: string;
    deviceId: string;
    orderNum: number;
    unifiedManageFlag: number;
    configurationItem: any;
  }

  export interface ResDeviceList {
    id: string;
    deviceModel: string;
    deviceName: string;
    deviceType: string;
    deviceTypeLabel: string;
    bootTime: number;
    deviceId: string;
    password: string;
    lastHeartBeat: string;
    mac: string;
    ipaddr: string;
    status: string;
    creator: string;
    creationTime: string;
    lastUpdater: string;
    lastUpdateTime: String;
    cmd: number;
    supports?: {
      wireless?: { supports: any };
      network?: { supports: any };
      system?: { supports: any };
    };
  }

  export interface ResConfigList {
    id: string;
    configName: string;
    configCode: string;
    configDesc: string;
    attribute: string;
    attribute2?: string; // 添加 attribute2 属性，用于非中文环境下的显示
    builtIn: number;
    lastHeartBeat: string;
    mac: string;
    ipaddr: string;
    status: string;
    creator: string;
    creationTime: string;
    lastUpdater: string;
    lastUpdateTime: String;
  }

  export interface ResDeviceModel {
    id: string;
    deviceModel: string;
    deviceName: string;
    deviceType: string;
    deviceTypeLabel: string;
    remark: number;
    creator: string;
    creationTime: string;
    lastUpdater: string;
    lastUpdateTime: string;
  }

  export interface ReqConfigParams {
    cmd: number;
    deviceId: string;
    userId: string;
    data: any;
    password?: string;
    sequenceNo?: number; // 分段数据序号
  }

  export interface ApGroup {
    group_id: number;
    alias?: any;
    ledoff?: number;
    radio1?: any;
    radio0?: any;
  }

  // 定义接口数据类型
  export interface DeviceStatistic {
    deviceType: string;
    deviceTypeName: string;
    count: number;
  }
}
