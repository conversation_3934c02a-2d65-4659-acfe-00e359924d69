/* eslint-disable @typescript-eslint/no-unused-vars */
import { ref, reactive, nextTick } from "vue";
import { TopologyData, DrawerProps, TopologyNode } from "@/api/interface/topology";
import { ElMessage } from "element-plus";
import {
  getTopologyData,
  saveDeviceName,
  deleteBridgeClient,
  getDeviceTopology,
  saveGroupExit,
  deleteExitByDeviceId,
  getExitByGroupId
} from "@/api/modules/topology";
import { useI18n } from "vue-i18n";
const t = i18n.global.t;

// 存储所有设备的MAC地址集合，用于过滤重复设备
const allDeviceMacAddresses = new Set();

// 存储所有peer设备的MAC地址集合，用于过滤子设备
const peerMacAddresses = new Set();

// 存储所有设备的节点引用，按MAC地址索引
const deviceNodesByMac = new Map();

// 存储出口设备ID的集合
const exitDeviceSet = new Set();

// 导入图标资源
import bridgeIcon_onLine from "@/assets/images/bridge_list_icon.png";
import otherDeviceIcon_online from "@/assets/images/device_other_online_icon.png";
import device_other_offline_icon from "@/assets/images/device_other_offline_icon.png";
import internetIcon from "@/assets/images/internet_icon.png";
import router_icon from "@/assets/images/router_icon.png";
import switch_icon from "@/assets/images/switch_icon.png";
import ac_icon from "@/assets/images/ac_icon.png";
import ap_icon from "@/assets/images/ap_icon.png";
import repeater_icon from "@/assets/images/zhongji_icon.png";
import device3_icon from "@/assets/images/device3_icon.png";
import i18n from "@/languages";

// 在文件顶部添加工具函数
function getCircularReplacer() {
  const seen = new WeakSet();
  return function (key, value) {
    if (typeof value === "object" && value !== null) {
      if (seen.has(value)) {
        return undefined;
      }
      seen.add(value);
    }
    // 过滤 parent 字段
    if (key === "parent") return undefined;
    return value;
  };
}

// 工具函数：递归移除 parent 字段，防止响应式死循环
// eslint-disable-next-line @typescript-eslint/no-unused-vars
function removeParentField(node, visited = new Set()) {
  if (!node || visited.has(node)) return;
  visited.add(node);
  if (node.parentNode) delete node.parentNode;
  if (Array.isArray(node.children)) {
    node.children.forEach(child => removeParentField(child, visited));
  }
}

function dedupeNodes(children) {
  const map = new Map();
  children.forEach(child => {
    if (!child || !child.mac) return; // 跳过无效节点
    const existingNode = map.get(child.mac);
    if (!existingNode) {
      map.set(child.mac, child);
    } else {
      // 找到重复节点，进行合并
      mergeNodeInfo(existingNode, child); // existingNode 是目标，child 是源

      // 合并子节点
      if (child.children && child.children.length > 0) {
        if (!existingNode.children) {
          existingNode.children = [];
        }
        // 将新节点的子节点也加入，然后统一去重
        existingNode.children.push(...child.children);
        // 递归对合并后的子节点进行去重
        existingNode.children = dedupeNodes(existingNode.children);
      }
    }
  });
  return Array.from(map.values());
}

/**
 *  合并两个节点的信息. sourceNode 的信息会合并到 targetNode.
 * @param targetNode 目标节点, 将被修改
 * @param sourceNode 源节点
 */
const mergeNodeInfo = (targetNode: any, sourceNode: any) => {
  if (!targetNode || !sourceNode) return;

  // 如果目标节点的名称是未知或为空，而源节点有有效名称，则更新名称
  if (
    (!targetNode.name || targetNode.name === t("topology.unknown") || targetNode.name === "未知") &&
    sourceNode.name &&
    sourceNode.name !== t("topology.unknown") &&
    sourceNode.name !== "未知"
  ) {
    targetNode.name = sourceNode.name;
  }

  // 如果目标节点没有 deviceId，而源节点有，则更新 deviceId
  if (!targetNode.deviceId && sourceNode.deviceId) {
    targetNode.deviceId = sourceNode.deviceId;
  }

  // 合并 extra 信息，源节点（sourceNode）的信息优先级更高，会覆盖目标节点（targetNode）的旧信息
  if (sourceNode.extra) {
    if (!targetNode.extra) {
      targetNode.extra = {};
    }
    targetNode.extra = { ...targetNode.extra, ...sourceNode.extra };
  }
};

/**
 * 拓扑图数据处理 Hook
 */
export function useTopology() {
  // 获取国际化函数
  const { t } = useI18n();

  // 基础调试日志
  console.log("[拓扑调试] useTopology_new hook 已加载");

  // 拓扑图数据
  const topologyData = reactive<TopologyData>({
    name: "internet",
    symbol: "image://" + internetIcon,
    children: []
  });

  // 抽屉组件状态
  const drawerVisible = ref(false);
  const drawerProps = ref<DrawerProps>({
    isView: false,
    title: "",
    row: {}
  });

  // 点击节点属性
  const clickNodeProps = ref<DrawerProps>({
    isView: false,
    title: "",
    row: {}
  });

  // 编辑状态
  const editName = ref(false);
  const deviceNameChanged = ref(false);
  const bridgeClientDrawerVisible = ref(false);

  /**
   * 获取设备图标
   * @param type 设备类型
   * @param online 在线状态
   * @returns 设备图标URL
   */
  const getDeviceIcon = (type: string, online: number): string => {
    // 根据设备类型和在线状态获取对应图标
    let iconUrl = "";

    if (type === "route") {
      iconUrl = online ? router_icon : router_icon; // 路由器暂时只有一种图标
    } else if (type === "switch") {
      iconUrl = switch_icon;
    } else if (type === "ac") {
      iconUrl = ac_icon;
    } else if (type === "ap") {
      iconUrl = ap_icon;
    } else if (type === "bridge") {
      iconUrl = online ? bridgeIcon_onLine : bridgeIcon_onLine; // 网桥使用在线图标
    } else if (type === "repeater") {
      iconUrl = repeater_icon;
    } else {
      iconUrl = online ? otherDeviceIcon_online : device_other_offline_icon;
    }

    return "image://" + iconUrl;
  };

  // const topologyItemArray = ref<topologyItem>(null);
  /**
   * 根据MAC地址从设备列表中查找完整的设备信息
   * @param device 当前设备信息（可能不完整）
   * @param deviceList 完整的设备列表
   * @returns 完整的设备信息
   */
  const findCompleteDeviceInfo = (device: any, deviceList: any[]) => {
    if (!device || !deviceList) return device;

    const deviceMac = (device.macaddr || device.mac || "").toLowerCase();
    if (!deviceMac) return device;

    // 从设备列表中查找匹配的设备
    const matchedDevice = deviceList.find((listDevice: any) => {
      const listMac = (listDevice.mac || listDevice.macaddr || "").toLowerCase();
      return listMac === deviceMac;
    });

    if (matchedDevice) {
      const logData = {
        deviceId: matchedDevice.deviceId,
        deviceName: matchedDevice.deviceName,
        deviceModel: matchedDevice.deviceModel,
        deviceType: matchedDevice.deviceType
      };

      // 合并设备信息，正确映射字段
      const mergedDevice = {
        ...device, // 保留拓扑数据中的特有信息（如端口信息等）
        ...matchedDevice, // 使用设备列表中的完整信息覆盖
        // 正确映射关键字段，优先使用拓扑数据中的字段（因为拓扑数据更准确）
        deviceId: matchedDevice.deviceId || matchedDevice.sn || device.deviceId,
        deviceName: device.name || matchedDevice.deviceName || device.deviceName,
        deviceModel: device.model || matchedDevice.deviceModel || device.deviceModel,
        deviceType: device.deviceType || matchedDevice.deviceType,
        // 在线状态：优先使用拓扑数据的offline字段，然后是设备列表的status
        offline: device.offline !== undefined ? device.offline : matchedDevice.status === 1 ? 1 : 0,
        status: device.offline !== undefined ? device.offline : matchedDevice.status || 0,
        mac: matchedDevice.mac || device.macaddr || device.mac,
        macaddr: matchedDevice.mac || device.macaddr || device.mac,
        ipaddr: matchedDevice.ipaddr || device.ipaddr
      };

      return mergedDevice;
    } else {
    }

    return device;
  };

  /**
   * 创建设备节点对象
   * @param device 设备信息
   * @param status 设备在线状态
   * @param deviceList 完整的设备列表（用于查找完整信息）
   * @returns 设备节点对象
   */
  const createDeviceNode = (device: any, status: number, deviceList?: any[]) => {
    if (!device) {
      console.warn("createDeviceNode: device is undefined");
      return null;
    }

    // 尝试从设备列表中获取完整的设备信息
    const completeDeviceInfo = deviceList ? findCompleteDeviceInfo(device, deviceList) : device;

    // 判断设备在线状态：offline=0在线，offline=1离线
    const isOnline = completeDeviceInfo.offline === 0 || completeDeviceInfo.offline === undefined;
    const deviceIcon = getDeviceIcon(completeDeviceInfo.deviceType, isOnline ? 1 : 0);

    console.log(`[设备状态调试] ${completeDeviceInfo.macaddr || completeDeviceInfo.mac}:`, {
      deviceType: completeDeviceInfo.deviceType,
      offline: completeDeviceInfo.offline,
      status: completeDeviceInfo.status,
      isOnline: isOnline,
      icon: deviceIcon
    });
    const mac =
      completeDeviceInfo.macaddr ||
      completeDeviceInfo.mac ||
      `unknown-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;

    return {
      name: completeDeviceInfo.deviceName || completeDeviceInfo.name || completeDeviceInfo.deviceModel || t("topology.unknown"),
      symbol: deviceIcon,
      children: [],
      extra: {
        ...completeDeviceInfo,
        // 确保offline和status字段正确
        offline: completeDeviceInfo.offline,
        status: completeDeviceInfo.status
      },
      // 添加顶层的offline和status字段，供D3组件使用
      offline: completeDeviceInfo.offline,
      status: completeDeviceInfo.status,
      forceParent: completeDeviceInfo?.dport?.toLowerCase() === "racli",
      mac: mac, // 确保mac属性始终有值
      parentNode: null,
      deviceId: completeDeviceInfo.deviceId || completeDeviceInfo.sn || mac
    };
  };

  /**
   * 检查两个节点是否相同
   * @param node1 节点1
   * @param node2 节点2
   * @returns 是否相同
   */
  const isSameNode = (node1: any, node2: any): boolean => {
    if (!node1 || !node2) return false;
    const mac1 = node1.extra?.macaddr || node1.extra?.mac;
    const mac2 = node2.extra?.macaddr || node2.extra?.mac;
    if (!mac1 || !mac2) return false;
    return mac1.toLowerCase() === mac2.toLowerCase();
  };

  /**
   * 递归在树中查找节点
   * @param node 要查找的节点
   * @param tree 要检查的树
   * @returns 找到的节点或null
   */
  const findNodeInTree = (node: any, tree: any, path: any[] = [], parentNode: any = null, visited = new Set()): any => {
    if (!tree || visited.has(tree)) return null;
    visited.add(tree);

    // 检查当前节点
    if (isSameNode(node, tree)) {
      return {
        node: tree,
        path: [...path, tree],
        parentNode: parentNode
      };
    }

    // 递归检查子节点
    if (tree.children && tree.children.length > 0) {
      for (let i = 0; i < tree.children.length; i++) {
        const child = tree.children[i];
        const result = findNodeInTree(node, child, [...path, tree], tree, visited);
        if (result) {
          result.indexInParent = i; // 记录节点在父节点children中的索引
          return result;
        }
      }
    }

    return null;
  };

  /**
   * 在拓扑图中查找节点
   * @param node 要查找的节点
   * @returns 找到的节点或null
   */
  const findNodeInTopology = (node: any): any => {
    // 检查根节点
    if (isSameNode(node, topologyData)) {
      return {
        node: topologyData,
        path: [topologyData],
        isRoot: true,
        level: 0
      };
    }

    // 检查所有子节点
    const visited = new Set();
    for (let i = 0; i < topologyData.children.length; i++) {
      const child = topologyData.children[i];
      const result = findNodeInTree(node, child, [topologyData], topologyData, visited);
      if (result) {
        result.isDirectChild = result.path.length === 2; // 如果路径长度为2，则是根节点的直接子节点
        result.indexInRoot = i; // 记录节点在根节点children中的索引
        result.level = result.path.length - 1; // 记录节点的层级，根节点为0级
        return result;
      }
    }

    return null;
  };

  /**
   * 合并两个节点的子节点
   * @param sourceNode 源节点
   * @param targetNode 目标节点
   */
  const mergeNodeChildren = (sourceNode: any, targetNode: any) => {
    // 检查源节点和目标节点是否有效
    if (!sourceNode || !targetNode) {
      console.error("mergeNodeChildren: sourceNode or targetNode is undefined", { sourceNode, targetNode });
      return;
    }

    // 目标节点已经在拓扑图中，可以查找其父节点
    const targetParentName = findParentNodeName(targetNode);

    // 源节点可能还没有被添加到拓扑图中，所以不查找其父节点
    console.log(`mergeNodeChildren: 合并节点 ${sourceNode.name} 到 ${targetNode.name} (父节点: ${targetParentName})`);

    if (sourceNode.extra?.deviceName) {
      targetNode.name = sourceNode.extra.deviceName;
    }

    // 检查源节点和目标节点的MAC地址和序列号
    const sourceMac = sourceNode.extra?.macaddr || sourceNode.extra?.mac;
    const targetMac = targetNode.extra?.macaddr || targetNode.extra?.mac;
    const sourceSN = sourceNode.extra?.sn || sourceNode.sn;
    // 获取目标节点的SN，用于比较和判断
    const targetSN = targetNode.extra?.sn;
    const sourceDeviceId = sourceNode.extra?.deviceId;
    const targetDeviceId = targetNode.extra?.deviceId;

    // 如果源节点和目标节点的MAC地址不同，但deviceId相同，这可能是一个错误
    if (
      sourceMac &&
      targetMac &&
      sourceMac !== targetMac &&
      sourceDeviceId &&
      targetDeviceId &&
      sourceDeviceId === targetDeviceId
    ) {
      console.warn(`警告: 合并节点时发现MAC地址不同但deviceId相同: ${sourceMac} 和 ${targetMac}, deviceId=${sourceDeviceId}`);
      // 在这种情况下，我们不应该覆盖deviceId
      console.log(`  保留原有deviceId值，不进行覆盖`);
    } else {
      // 处理deviceId
      if (!targetNode.extra.deviceId && sourceNode.extra?.deviceId) {
        // 只有当目标节点没有deviceId时，才使用源节点的deviceId
        targetNode.extra.deviceId = sourceNode.extra.deviceId;
      }

      // 处理sn字段
      if (!targetSN && sourceSN) {
        // 只有当目标节点没有sn时，才使用源节点的sn
        console.log(`  目标节点没有SN，使用源节点的SN: ${sourceSN}`);
        targetNode.extra.sn = sourceSN;
      } else if (targetSN && sourceSN && targetSN !== sourceSN) {
        // 如果两个节点的SN不同，输出警告
        console.log(`  警告: 源节点和目标节点的SN不同: ${sourceSN} 和 ${targetSN}`);
      }
    }

    // 如果源节点没有子节点，直接返回
    if (!sourceNode.children || sourceNode.children.length === 0) {
      console.log(`  源节点没有子节点，无需合并`);
      return;
    }

    // 确保目标节点有children数组
    if (!targetNode.children) {
      targetNode.children = [];
    }

    console.log(`  源节点有 ${sourceNode.children.length} 个子节点需要合并`);

    // 遍历源节点的所有子节点
    for (const childNode of sourceNode.children) {
      // 在目标节点的子节点中查找相同的节点
      const existingChildIndex = targetNode.children.findIndex(item => isSameNode(childNode, item));

      if (existingChildIndex !== -1) {
        // 如果找到相同的子节点，递归合并它们的子节点
        console.log(`    子节点 ${childNode.name} 已存在，递归合并`);
        mergeNodeChildren(childNode, targetNode.children[existingChildIndex]);
      } else {
        // 如果没有找到相同的子节点，将子节点添加到目标节点
        console.log(`    添加新子节点 ${childNode.name} 到目标节点`);
        targetNode.children.push(childNode);
        childNode.parentNode = targetNode;
      }
    }

    console.log(`  合并完成，目标节点现有 ${targetNode.children.length} 个子节点`);
  };

  /**
   * 查找节点的父节点
   * @param node 要查找父节点的节点
   * @returns 父节点信息或null
   */
  /**
   * 查找节点的父节点名称
   * @param node 要查找父节点的节点
   * @param currentNode 当前节点，默认为根节点
   * @returns 父节点名称
   */
  const findParentNodeName = (node: any, currentNode: any = topologyData): string => {
    if (!currentNode || !currentNode.children) {
      return "";
    }

    for (const child of currentNode.children) {
      if (isSameNode(child, node)) {
        return currentNode.name || "无名称";
      }

      const result = findParentNodeName(node, child);
      if (result !== "") {
        return result;
      }
    }

    return "";
  };

  /**
   * 查找节点的父节点
   * @param node 要查找父节点的节点
   * @param target 查找的范围
   * @returns 父节点信息或null
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const mergeSubNode = (node: any, target: any): any => {
    if (!target) {
      return node;
    }
    // 检查参数是否有效
    if (!node) {
      console.error("findParentNode: node is undefined");
      return null;
    }

    // 如果节点是根节点，返回null
    if (node.mac === target.mac) {
      // 就合并子节点
      target?.children.push(node.children);
      return target;
    }

    if (node.mac == target.children[0].mac) {
      target.children[0].children.push(node.children);
      return target;
    }

    if (node.children[0].mac == target.mac) {
      node.children[0].children.push(target.children);
      return node;
    }

    if (node.children[0].mac == target.children[0].mac) {
      delete node.children;
      target.children[0].children.push(node);
      return target;
    }
    return null;
  };

  /**
   * 根据子节点数组找到父节点
   * @param childrenArray 子节点数组
   * @returns 父节点或null
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const findParentNodeByChildrenArray = (childrenArray: any[]): any => {
    if (!childrenArray || !Array.isArray(childrenArray)) {
      console.error("findParentNodeByChildrenArray: childrenArray is invalid", childrenArray);
      return null;
    }

    // 递归查找函数
    const findParent = (node: any, path: any[] = []): any => {
      // 检查当前节点的children是否与目标数组相同
      if (node.children === childrenArray) {
        return {
          node: node,
          path: [...path, node],
          isDirectChild: path.length === 1 // 如果路径长度为1，则是根节点的直接子节点
        };
      }

      // 递归检查子节点
      if (node.children && node.children.length > 0) {
        for (const child of node.children) {
          const result = findParent(child, [...path, node]);
          if (result) {
            return result;
          }
        }
      }

      return null;
    };

    // 从根节点开始查找
    return findParent(topologyData);
  };

  /**
   * 移动节点到新的位置
   * @param nodeInfo 要移动的节点信息
   * @param targetNode 目标节点
   * @returns 是否移动成功
   */
  const moveNodeToTarget = (nodeInfo: any, targetNode: any) => {
    if (!nodeInfo || !targetNode) {
      console.error("moveNodeToTarget: nodeInfo or targetNode is undefined", { nodeInfo, targetNode });
      return false;
    }

    const { node, parentNode, indexInParent, isDirectChild, indexInRoot } = nodeInfo;

    // 查找节点的父节点
    const nodeParentName = parentNode ? parentNode.name : findParentNodeName(node);

    // 查找目标节点的父节点
    const targetParentName = findParentNodeName(targetNode);

    console.log(
      `moveNodeToTarget: 尝试移动节点 ${node.name} (父节点: ${nodeParentName}) 到目标节点 ${targetNode.name} (父节点: ${targetParentName})`
    );
    console.log(`  节点层级: ${nodeInfo.level}, 是否为根节点直接子节点: ${isDirectChild}`);

    // 确保目标节点有children属性
    if (!targetNode.children) {
      targetNode.children = [];
    }

    // 如果节点是根节点的直接子节点
    if (isDirectChild) {
      // 从根节点中移除该节点
      topologyData.children.splice(indexInRoot, 1);
      console.log(`  从根节点中移除节点 ${node.name}`);
    } else if (parentNode && typeof indexInParent === "number") {
      // 从父节点中移除该节点
      parentNode.children.splice(indexInParent, 1);
      console.log(`  从父节点 ${parentNode.name} 中移除节点 ${node.name}`);
    }

    //在这里替换正确的sport和dport
    if (targetNode?.extra?.system?.topology) {
      let { peer, subDev } = targetNode.extra?.system?.topology;
      // 在目标节点的extra中添加peer和subDev，用macaddr匹配
      let peerObj;
      let subDevObj;
      if (peer && subDev) {
        peerObj = peer.find((item: any) => item.macaddr === node.extra?.macaddr);
        subDevObj = subDev.find((item: any) => item.macaddr === node.extra?.macaddr);
      }
      if (peerObj) {
        // 注意：在父节点的peer/subDev数据中，
        // sport是父节点连接到子节点的端口，应该是子节点的dport
        // dport是子节点连接到父节点的端口，应该是子节点的sport
        node.extra.sport = peerObj.dport; // 父节点的dport是子节点的sport
        node.extra.dport = peerObj.sport; // 父节点的sport是子节点的dport
      }
      if (subDevObj) {
        // 同样需要交换sport和dport
        node.extra.sport = subDevObj.dport; // 父节点的dport是子节点的sport
        node.extra.dport = subDevObj.sport; // 父节点的sport是子节点的dport
      }
    }

    // 将节点添加到目标节点的子节点中
    targetNode.children.push(node);
    console.log(`  将节点 ${node.name} 添加到目标节点 ${targetNode.name} 的子节点中`);

    // 输出移动后的节点层级结构
    console.log("\n节点层级结构:");
    logNodeStructure(topologyData, 0);

    return true;
  };

  /**
   * 添加节点到拓扑图，全局去重并合并子节点
   * @param node 要添加的节点
   * @param targetArray 目标数组,上级节点的children数组，例如topologyData.children
   * @returns 是否添加成功（true：新增节点，false：合并到现有节点）
   */
  const addNodeWithDedupe = (node: any, targetArray: any) => {
    // 检查参数是否有效
    if (!node) {
      console.error("addNodeWithDedupe: node is undefined");
      return false;
    }

    // 检查 targetArray 是否为有效数组
    if (!targetArray.children || !Array.isArray(targetArray.children)) {
      console.error("addNodeWithDedupe: targetArray is not a valid array", targetArray);
      return false;
    }

    console.log(`addNodeWithDedupe: 尝试添加节点 ${node.name} 到目标数组`);

    // 先在目标数组中查找是否有相同节点
    const existingNodeIndex = targetArray.children.findIndex(item => isSameNode(node, item));

    // 如果在目标数组中找到相同节点,这里是判断节点在待新增的拓扑结构中，是否存在相同节点
    if (existingNodeIndex !== -1) {
      console.log(`  在目标数组中找到相同节点: ${targetArray.children[existingNodeIndex].name}`);
      // 优先级判定
      const existingNode = targetArray.children[existingNodeIndex];
      if (isNodeAPriorityHigher(node, existingNode) >= 0) {
        // node优先级高，把existingNode合并到node
        mergeNodeChildren(existingNode, node);
        // 由于和原设备的关联关系不能丢失，所以原有拓扑结构，从待删除节点的一直到根节点（除internet节点外）的路径，父子关系都要反转，然后再删除原有节点，并将反转后整个拓扑都添加到新节点的子节点中
        // 先删除原有节点，再添加新节点
        // removeNodeInTree(targetArray, existingNode);
        targetArray.children.push(node);
        node.parentNode = targetArray;
      } else {
        // existingNode优先级高，把node合并到existingNode
        mergeNodeChildren(node, existingNode);
      }
      return false;
    }

    // 再检查节点是否已存在于整个拓扑图中的其他位置
    const existingNodeInfo = findNodeInTopology(node);
    if (existingNodeInfo) {
      const existingNode = existingNodeInfo.node;
      // 查找现有节点的父节点
      const existingParentName = findParentNodeName(existingNode);
      // 判断已存在节点的父级节点是否是根节点是否需要移动节点
      // 如果现有节点是根节点的直接子节点，而目标数组不是根节点的children
      const isExistRootChildren = topologyData.name === existingParentName;

      if (isExistRootChildren) {
        // 如果现有节点是根节点的直接子节点，而目标不是根节点，则移动节点
        console.log(`  现有节点是根节点的直接子节点，需要移动到非根节点位置`);

        // 检查是否有明确的端口关系支持这种移动
        const hasValidPortRelation = hasExplicitPortRelation(node);

        if (targetArray.name !== "internet" && hasValidPortRelation) {
          // 只有在有明确端口关系时才移动节点，避免根节点被错误移动
          console.log(`  检测到明确端口关系，执行节点移动`);
          moveNodeToTarget(existingNodeInfo, targetArray);
          return true;
        } else {
          // 没有明确端口关系时，合并子节点而不移动节点
          console.log(`  没有明确端口关系，合并子节点而不移动根节点`);
          mergeNodeChildren(node, existingNode);
          return false;
        }
      }
      // 优先级判定
      if (isNodeAPriorityHigher(node, existingNode >= 0)) {
        // node优先级高，把existingNode合并到node
        mergeNodeChildren(existingNode, node);

        // 递归查找父节点和索引后删除原有节点
        // if (existingNodeInfo.parentNode && typeof existingNodeInfo.indexInParent === "number") {
        //   existingNodeInfo.parentNode.children.splice(existingNodeInfo.indexInParent, 1);
        // }
        // 在目标位置添加新节点
        targetArray.children.push(node);
        node.parentNode = targetArray;
      } else {
        // existingNode优先级高，把node合并到existingNode
        mergeNodeChildren(node, existingNode);
      }
      return false;
    }
    // 如果当前节点node.extra.topologyType 不为空，则说明是查询的离线拓扑图
    if (node.extra && node.extra.topologyType) {
      // 如果节点的extra.topologyType不为空，则说明是查询的离线拓扑图,则将extra.deviceId置空，因为数据库中保存的deviceId标识的父级节点的deviceId是子级节点的deviceId
      node.extra.deviceId = null;
    }

    // 如果不存在重复，则添加新节点
    console.log(`  未找到相同节点，添加新节点: ${node.name}`);
    targetArray.children.push(node);
    node.parentNode = targetArray;
    return true;
  };

  /**
   * 检查设备的MAC地址是否已经存在于拓扑图中
   * @param macaddr MAC地址
   * @returns 是否已经存在
   */
  const isMacAddressExists = (macaddr: string): boolean => {
    if (!macaddr) return false;

    // 转换为小写以确保匹配
    const normalizedMac = macaddr.toLowerCase();
    return allDeviceMacAddresses.has(normalizedMac);
  };

  /**
   * 添加MAC地址到集合中
   * @param macaddr MAC地址
   * @param node 节点引用
   */
  const addMacAddress = (macaddr: string, node: any): void => {
    if (!macaddr) return;

    // 转换为小写以确保匹配
    const normalizedMac = macaddr.toLowerCase();
    allDeviceMacAddresses.add(normalizedMac);
    deviceNodesByMac.set(normalizedMac, node);
  };

  /**
   * 删除重复的子设备
   * 递归遍历拓扑图，删除所有与peer设备MAC地址相同的子设备
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const removeSubDevWithSameMacAsPeer = topologyDataList => {
    // 防御性检查，确保topologyDataList存在
    if (!topologyDataList || !Array.isArray(topologyDataList)) {
      console.warn("removeSubDevWithSameMacAsPeer: topologyDataList is not valid");
      return;
    }

    // 收集所有peer设备的MAC地址
    const peerMacs = new Set();

    // 第一次遍历，收集所有peer设备的MAC地址
    const collectPeerMacs = (node: any) => {
      if (!node) return;

      // 如果节点是peer设备，收集其MAC地址
      if (node.extra?.topologyType === "peer") {
        const mac = node.extra?.macaddr || node.extra?.mac;
        if (mac) {
          peerMacs.add(mac.toLowerCase());
        }
      }

      // 递归遍历子节点
      if (node.children && Array.isArray(node.children)) {
        node.children.forEach(child => {
          if (child) collectPeerMacs(child);
        });
      }
    };

    // 第二次遍历，删除重复的子设备
    const removeSubDevs = (node: any) => {
      if (!node) return;
      if (!node.children || !Array.isArray(node.children)) return;

      // 过滤子节点，删除重复的子设备
      node.children = node.children.filter(child => {
        // 防御性检查，确保child存在
        if (!child || !child.extra) return true;

        // 如果不是子设备，保留
        if (child.extra.topologyType !== "subDev") {
          return true;
        }

        // 如果是子设备，检查MAC地址是否在peer设备中存在
        const mac = child.extra.macaddr || child.extra.mac;
        if (mac && peerMacs.has(mac.toLowerCase())) {
          return false; // 删除该子设备
        }

        return true; // 保留该子设备
      });

      // 递归处理子节点
      node.children.forEach(child => {
        if (child) removeSubDevs(child);
      });
    };

    // 执行处理
    topologyDataList.forEach(node => {
      if (node) {
        collectPeerMacs(node);
      }
    });

    topologyDataList.forEach(node => {
      if (node) {
        removeSubDevs(node);
      }
    });
  };

  // ===== 新的拓扑构建系统 =====

  /**
   * 设备关系接口定义
   */
  interface DeviceRelation {
    reporterMac: string; // 报告设备MAC
    reporterDeviceId: string; // 报告设备ID
    reporterPort: string; // 报告设备端口 (sport)
    targetMac: string; // 目标设备MAC
    targetPort: string; // 目标设备端口 (dport)

    parentMac: string; // 最终确定的父节点MAC
    childMac: string; // 最终确定的子节点MAC
    parentPort: string; // 父节点端口
    childPort: string; // 子节点端口

    relationLevel: number; // 关系优先级 1=RA最高, 2=端口, 3=设备类型, 4=状态
    isSubDev: boolean; // 是否为子设备关系
    medium?: string; // 连线方式 RADIO/CABLE

    // 原始数据保留
    rawData: any;
  }

  /**
   * 重新定义的端口优先级函数
   */
  const getPortPriorityNew = (port: string): number => {
    const portLower = (port || "").toLowerCase();

    // 空端口当LAN端口处理
    if (!portLower) return 80;

    // RA类型内部优先级：RAx > RACLI
    if (/^ra\d+/i.test(portLower)) return 100; // RA{数字} 最高优先级
    if (portLower === "racli") return 50; // RACLI 中等优先级

    // LAN类型：所有LAN口优先级相同
    if (/^(lan|ge|fe|port|gi)/i.test(portLower)) return 80;

    // WAN类型：优先级最低
    if (/^wan/i.test(portLower)) return 10;

    // 其他未知端口当LAN处理
    return 80;
  };

  /**
   * 分析单个设备关系
   */
  const analyzeDeviceRelation = (
    reporterDeviceId: string,
    reporterMac: string,
    targetMac: string,
    sport: string,
    dport: string,
    isSubDev: boolean,
    medium?: string,
    rawData?: any
  ): DeviceRelation => {
    // 基于端口优先级判断父子关系
    const sportPriority = getPortPriorityNew(sport);
    const dportPriority = getPortPriorityNew(dport);

    // 特别追踪特定MAC地址的关系分析
    if (
      reporterMac === "90-E2-FC-02-A8-84" ||
      targetMac === "90-E2-FC-02-A8-84" ||
      reporterMac === "90-E2-FC-02-C8-37" ||
      targetMac === "90-E2-FC-02-C8-37"
    ) {
      console.log(`[特定MAC调试] ${reporterMac} <-> ${targetMac}:`, {
        reporterMac,
        targetMac,
        sport,
        dport,
        sportPriority,
        dportPriority,
        medium,
        shouldBeParent: sportPriority > dportPriority ? "reporter" : "target",
        isSubDev
      });
    }

    // 特别追踪RA关系
    if (
      (sport && sport.toLowerCase() === "racli") ||
      (dport && dport.toLowerCase() === "racli") ||
      (sport && /^ra\d+/i.test(sport)) ||
      (dport && /^ra\d+/i.test(dport))
    ) {
      console.log(`[RA关系分析] ${reporterMac} <-> ${targetMac}:`, {
        reporterMac,
        targetMac,
        sport,
        dport,
        sportPriority,
        dportPriority,
        medium,
        shouldBeParent: dportPriority > sportPriority ? "target" : "reporter"
      });
    }

    let parentMac, childMac, parentPort, childPort;
    let relationLevel = 2; // 默认端口关系

    // 在peer关系中：
    // sport是报告设备连接到目标设备的端口
    // dport是目标设备连接到报告设备的端口
    // 拥有高优先级端口的设备应该是父节点
    if (sportPriority > dportPriority) {
      // 报告设备端口优先级高，报告设备是父节点
      parentMac = reporterMac;
      childMac = targetMac;
      parentPort = sport;
      childPort = dport;
    } else if (sportPriority < dportPriority) {
      // 目标设备端口优先级高，目标设备是父节点
      parentMac = targetMac;
      childMac = reporterMac;
      parentPort = dport;
      childPort = sport;
    } else {
      // 同优先级（LAN=LAN），需要设备类型判断
      relationLevel = 3;
      // 暂时按报告设备为父节点，后续根据设备类型调整
      parentMac = reporterMac;
      childMac = targetMac;
      parentPort = sport;
      childPort = dport;
    }

    // 检查是否为RA关系（最高优先级）
    if (parentPort && childPort && /^ra\d+/i.test(parentPort) && childPort.toLowerCase() === "racli") {
      relationLevel = 1; // RA关系最高优先级
    }

    const result = {
      reporterMac,
      reporterDeviceId,
      reporterPort: sport,
      targetMac,
      targetPort: dport,
      parentMac,
      childMac,
      parentPort,
      childPort,
      relationLevel,
      isSubDev,
      medium,
      rawData
    };

    // 特别追踪RA关系的最终结果
    if (
      (sport && sport.toLowerCase() === "racli") ||
      (dport && dport.toLowerCase() === "racli") ||
      (sport && /^ra\d+/i.test(sport)) ||
      (dport && /^ra\d+/i.test(dport))
    ) {
      console.log(`[RA关系结果] 最终生成的RA关系:`, {
        finalParent: result.parentMac,
        finalChild: result.childMac,
        finalParentPort: result.parentPort,
        finalChildPort: result.childPort,
        relationLevel: result.relationLevel,
        medium: result.medium,
        direction: `${result.parentMac}(${result.parentPort}) -> ${result.childMac}(${result.childPort})`
      });
    }

    return result;
  };

  /**
   * 收集所有设备关系
   */
  const collectAllDeviceRelations = (deviceList: any[], topologyResList: any[]): DeviceRelation[] => {
    const relations: DeviceRelation[] = [];

    deviceList.forEach((device, index) => {
      const topologyRes = topologyResList[index];
      if (!topologyRes?.data?.system?.topology) return;

      const topology = topologyRes.data.system.topology;
      const reporterMac = (device.mac || device.macaddr || "").toLowerCase();
      const reporterDeviceId = device.deviceId;

      console.log(`  拓扑数据数量: ${topology.length}`);

      if (!reporterMac) return;

      // 处理 peer 关系
      topology.peer?.forEach((peer: any) => {
        if (!peer.macaddr) return;

        const peerMac = (peer.macaddr || "").toLowerCase();
        console.log(`    [Peer] 报告者: ${reporterMac} -> 目标: ${peerMac}, 端口: ${peer.sport || ""} -> ${peer.dport || ""}`);

        // 特别追踪问题MAC
        if (peerMac === "90-e2-fc-02-a8-74") {
        }

        const relation = analyzeDeviceRelation(
          reporterDeviceId,
          reporterMac,
          peerMac,
          peer.sport || "",
          peer.dport || "",
          false, // peer关系
          peer.medium,
          peer
        );
        relations.push(relation);
      });

      // 处理 subDev 关系
      topology.subDev?.forEach((subDev: any) => {
        if (!subDev.macaddr) return;

        const subDevMac = (subDev.macaddr || "").toLowerCase();
        console.log(
          `    [SubDev] 报告者: ${reporterMac} -> 目标: ${subDevMac}, 端口: ${subDev.sport || ""} -> ${subDev.dport || ""}`
        );

        // 特别追踪问题MAC
        if (subDevMac === "90-e2-fc-02-a8-74") {
        }

        const relation = analyzeDeviceRelation(
          reporterDeviceId,
          reporterMac,
          subDevMac,
          subDev.sport || "",
          subDev.dport || "",
          true, // subDev关系
          subDev.medium,
          subDev
        );
        relations.push(relation);
      });
    });

    // 去除重复的关系（包括双向重复）
    const uniqueRelations = relations.filter((rel, index, arr) => {
      // 首先检查完全相同的关系
      const exactMatch = arr.findIndex(
        r =>
          r.parentMac === rel.parentMac &&
          r.childMac === rel.childMac &&
          r.parentPort === rel.parentPort &&
          r.childPort === rel.childPort &&
          r.isSubDev === rel.isSubDev
      );

      if (exactMatch !== index) return false;

      // 检查双向关系（反向关系）
      const reverseMatch = arr.findIndex(
        r =>
          r.parentMac === rel.childMac &&
          r.childMac === rel.parentMac &&
          r.parentPort === rel.childPort &&
          r.childPort === rel.parentPort &&
          r.isSubDev === rel.isSubDev
      );

      // 如果存在反向关系，选择relationLevel更低（优先级更高）的关系
      if (reverseMatch !== -1 && reverseMatch !== index) {
        const reverseRel = arr[reverseMatch];

        // 保留优先级更高的关系，如果优先级相同则保留索引较小的
        if (rel.relationLevel < reverseRel.relationLevel) {
          return true; // 保留当前关系
        } else if (rel.relationLevel > reverseRel.relationLevel) {
          return false; // 保留反向关系
        } else {
          return index < reverseMatch; // 优先级相同时保留索引较小的
        }
      }

      return true; // 没有重复，保留该关系
    });

    console.log("[拓扑调试] 去重前关系数量:", relations.length, "去重后:", uniqueRelations.length);

    return uniqueRelations;
  };

  /**
   * 第二阶段：关系优化和冲突解决
   */

  /**
   * 处理RA关系的优先级覆盖
   * RA关系具有最高优先级，可以覆盖其他关系
   */
  const handleRARelationshipPriority = (relations: DeviceRelation[]): DeviceRelation[] => {
    const raRelations = relations.filter(
      rel => (rel.parentPort && rel.parentPort.startsWith("RA")) || (rel.childPort && rel.childPort.startsWith("RA"))
    );

    if (raRelations.length === 0) return relations;

    const nonRARelations = relations.filter(
      rel => !(rel.parentPort && rel.parentPort.startsWith("RA")) && !(rel.childPort && rel.childPort.startsWith("RA"))
    );

    // RA关系优先，直接使用
    const optimizedRelations = [...raRelations];

    // 检查非RA关系是否与RA关系冲突
    nonRARelations.forEach(nonRARel => {
      const hasConflict = raRelations.some(
        raRel =>
          (nonRARel.parentMac === raRel.childMac && nonRARel.childMac === raRel.parentMac) ||
          (nonRARel.parentMac === raRel.parentMac && nonRARel.childMac === raRel.childMac)
      );

      if (!hasConflict) {
        optimizedRelations.push(nonRARel);
      } else {
        console.log(`[拓扑调试] 冲突关系被过滤: ${nonRARel.parentMac} -> ${nonRARel.childMac}`);
      }
    });

    return optimizedRelations;
  };

  /**
   * 根据设备类型优先级解决同端口级别的关系冲突
   */
  const resolveDeviceTypePriority = (relations: DeviceRelation[], deviceList: any[]): DeviceRelation[] => {
    const deviceTypeMap = new Map();
    deviceList.forEach(device => {
      const mac = (device.mac || device.macaddr || "").toLowerCase();
      if (mac) {
        deviceTypeMap.set(mac, device.deviceType || "");
      }
    });

    const getDeviceTypePriority = (deviceType: string): number => {
      const priorities = { route: 100, ac: 80, switch: 60, reaper: 40, ap: 20, bridge: 10 };
      return priorities[deviceType] || 0;
    };

    // 按关系级别分组
    const relationsByLevel = new Map<number, DeviceRelation[]>();
    relations.forEach(rel => {
      if (!relationsByLevel.has(rel.relationLevel)) {
        relationsByLevel.set(rel.relationLevel, []);
      }
      relationsByLevel.get(rel.relationLevel)!.push(rel);
    });

    const optimizedRelations: DeviceRelation[] = [];

    relationsByLevel.forEach(levelRelations => {
      if (levelRelations.length <= 1) {
        optimizedRelations.push(...levelRelations);
        return;
      }

      // 检查是否有冲突关系（相同设备对但方向相反）
      const conflictGroups = new Map<string, DeviceRelation[]>();

      levelRelations.forEach(rel => {
        const key1 = `${rel.parentMac}-${rel.childMac}`;
        const key2 = `${rel.childMac}-${rel.parentMac}`;

        if (conflictGroups.has(key2)) {
          conflictGroups.get(key2)!.push(rel);
        } else if (conflictGroups.has(key1)) {
          conflictGroups.get(key1)!.push(rel);
        } else {
          conflictGroups.set(key1, [rel]);
        }
      });

      conflictGroups.forEach(conflictRels => {
        if (conflictRels.length === 1) {
          optimizedRelations.push(conflictRels[0]);
        } else {
          // 根据设备类型优先级选择关系方向
          const sortedRels = conflictRels.sort((a, b) => {
            const aParentType = deviceTypeMap.get(a.parentMac) || "";
            const bParentType = deviceTypeMap.get(b.parentMac) || "";
            const aPriority = getDeviceTypePriority(aParentType);
            const bPriority = getDeviceTypePriority(bParentType);

            console.log(
              `[拓扑调试] 设备类型冲突解决: ${a.parentMac}(${aParentType}:${aPriority}) vs ${b.parentMac}(${bParentType}:${bPriority})`
            );

            return bPriority - aPriority;
          });

          console.log(
            `[拓扑调试] 选择关系: ${sortedRels[0].parentMac}(${sortedRels[0].parentPort}) -> ${sortedRels[0].childMac}(${sortedRels[0].childPort})`
          );
          optimizedRelations.push(sortedRels[0]);
        }
      });
    });

    return optimizedRelations;
  };

  /**
   * 实现关系反转逻辑
   * 当端口优先级相同时，根据设备类型优先级确定父子关系
   * 当端口优先级不同但设备类型优先级有明显差异时，可能需要反转关系
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const applyRelationReversal = (relations: DeviceRelation[], deviceList: any[]): DeviceRelation[] => {
    try {
      const deviceTypeMap = new Map();
      deviceList.forEach(device => {
        const mac = (device.mac || device.macaddr || "").toLowerCase();
        if (mac) {
          deviceTypeMap.set(mac, device.deviceType || "");
        }
      });

      const getDeviceTypePriority = (deviceType: string): number => {
        const priorities = { route: 100, ac: 80, switch: 60, reaper: 40, ap: 20, bridge: 10 };
        return priorities[deviceType] || 0;
      };

      // 检查端口是否为RA端口
      const isRAPort = (port: string): boolean => {
        return port && port.toUpperCase().startsWith("RA");
      };

      // 按设备分组，分析每个设备作为子设备的所有关系
      const childDeviceRelationMap = new Map<string, DeviceRelation[]>();
      relations.forEach(rel => {
        if (!childDeviceRelationMap.has(rel.childMac)) {
          childDeviceRelationMap.set(rel.childMac, []);
        }
        childDeviceRelationMap.get(rel.childMac)!.push(rel);
      });

      const result = relations.map(rel => {
        const parentType = deviceTypeMap.get(rel.parentMac) || "";
        const childType = deviceTypeMap.get(rel.childMac) || "";
        const parentTypePriority = getDeviceTypePriority(parentType);
        const childTypePriority = getDeviceTypePriority(childType);

        // 检查子设备是否有RA关系（作为子设备）
        const childRelations = childDeviceRelationMap.get(rel.childMac) || [];
        const childHasRARelation = childRelations.some(r => isRAPort(r.parentPort || "") || isRAPort(r.childPort || ""));

        // 当前关系是否为RA关系
        const isCurrentRARelation = isRAPort(rel.parentPort || "") || isRAPort(rel.childPort || "");

        // 特别追踪特定MAC地址的关系反转分析
        if (
          rel.parentMac === "90-E2-FC-02-A8-84" ||
          rel.childMac === "90-E2-FC-02-A8-84" ||
          rel.parentMac === "90-E2-FC-02-C8-37" ||
          rel.childMac === "90-E2-FC-02-C8-37"
        ) {
          console.log(`[特定MAC关系反转分析] ${rel.parentMac} -> ${rel.childMac}:`, {
            parentPort: rel.parentPort,
            childPort: rel.childPort,
            isCurrentRARelation,
            childHasRARelation,
            relationLevel: rel.relationLevel,
            parentType,
            childType,
            parentTypePriority,
            childTypePriority,
            childRelationsCount: childRelations.length,
            shouldReverse:
              (childHasRARelation && !isCurrentRARelation && rel.relationLevel > 1) ||
              (!childHasRARelation && rel.relationLevel > 1 && childTypePriority > parentTypePriority + 20)
          });
        }

        console.log(`[拓扑调试] 分析关系 ${rel.parentMac} -> ${rel.childMac}:`, {
          parentPort: rel.parentPort,
          childPort: rel.childPort,
          isCurrentRARelation,
          childHasRARelation,
          relationLevel: rel.relationLevel,
          parentType,
          childType,
          childRelationsCount: childRelations.length
        });

        // RA关系优先级处理：
        // 如果子设备有RA关系，且当前关系不是RA关系，且关系级别>1，则考虑反转
        // 这意味着RA关系应该优先，其他关系应该被反转以符合层级结构
        const shouldReverse =
          (childHasRARelation && !isCurrentRARelation && rel.relationLevel > 1) ||
          // 原有的设备类型优先级反转逻辑（作为备选）
          (!childHasRARelation && rel.relationLevel > 1 && childTypePriority > parentTypePriority + 20);

        if (shouldReverse) {
          console.log(
            `[拓扑调试] 关系反转: ${rel.parentMac}(${parentType}:${parentTypePriority}) <-> ${rel.childMac}(${childType}:${childTypePriority})`,
            `原因: ${childHasRARelation ? "RA关系优先级" : "设备类型优先级"}`
          );

          // 特别追踪特定MAC地址的关系反转
          if (
            rel.parentMac === "90-E2-FC-02-A8-84" ||
            rel.childMac === "90-E2-FC-02-A8-84" ||
            rel.parentMac === "90-E2-FC-02-C8-37" ||
            rel.childMac === "90-E2-FC-02-C8-37"
          ) {
            console.log(`[特定MAC关系反转执行] 反转前: ${rel.parentMac}(${rel.parentPort}) -> ${rel.childMac}(${rel.childPort})`);
            console.log(`[特定MAC关系反转执行] 反转后: ${rel.childMac}(${rel.childPort}) -> ${rel.parentMac}(${rel.parentPort})`);
          }

          return {
            ...rel,
            parentMac: rel.childMac,
            childMac: rel.parentMac,
            parentPort: rel.childPort,
            childPort: rel.parentPort
          };
        }

        return rel;
      });

      console.log("[拓扑调试] 关系反转处理完成，输出关系数量:", result.length);

      return result;
    } catch (error) {
      console.error("[拓扑调试] 关系反转处理出错:", error);
      return relations; // 出错时返回原始关系
    }
  };

  /**
   * 严格的树形结构规则：确保每个节点只能有一个父节点
   * 当出现多个父节点争夺同一个子节点时，选择优先级最高的关系
   */
  const enforceStrictTreeStructure = (relations: DeviceRelation[]): DeviceRelation[] => {
    console.log("[拓扑调试] enforceStrictTreeStructure开始，输入关系数量:", relations.length);

    // 按关系优先级排序（RA > WAN > LAN）
    const sortedRelations = [...relations].sort((a, b) => a.relationLevel - b.relationLevel);

    const childNodeMap = new Map<string, DeviceRelation>(); // 每个子节点只能有一个父节点
    const finalRelations: DeviceRelation[] = [];

    sortedRelations.forEach(rel => {
      const childMac = rel.childMac;

      // 如果这个子节点已经有父节点了
      if (childNodeMap.has(childMac)) {
        const existingRel = childNodeMap.get(childMac)!;

        console.log(`[拓扑调试] 发现重复子节点: ${childMac}`);
        console.log(`[拓扑调试] 现有关系: ${existingRel.parentMac} -> ${childMac} (优先级: ${existingRel.relationLevel})`);
        console.log(`[拓扑调试] 新关系: ${rel.parentMac} -> ${childMac} (优先级: ${rel.relationLevel})`);

        // 比较关系优先级，只保留优先级更高的关系
        if (rel.relationLevel < existingRel.relationLevel) {
          // 新关系优先级更高，替换现有关系
          console.log(
            `[拓扑调试] 使用新关系 (优先级更高): ${rel.parentMac} -> ${rel.childMac} (${rel.relationLevel}) 替换 ${existingRel.parentMac} -> ${existingRel.childMac} (${existingRel.relationLevel})`
          );
          const existingIndex = finalRelations.findIndex(r => r === existingRel);
          if (existingIndex !== -1) {
            finalRelations.splice(existingIndex, 1);
          }
          childNodeMap.set(childMac, rel);
          finalRelations.push(rel);
        } else if (rel.relationLevel === existingRel.relationLevel) {
          // 优先级相同，比较端口优先级
          console.log(`[拓扑调试] 关系优先级相同，比较端口优先级`);

          const existingParentPortPriority = getPortPriorityNew(existingRel.parentPort || "");
          const existingChildPortPriority = getPortPriorityNew(existingRel.childPort || "");
          const newParentPortPriority = getPortPriorityNew(rel.parentPort || "");
          const newChildPortPriority = getPortPriorityNew(rel.childPort || "");

          // 计算关系的总体端口优先级（父端口优先级 + 子端口优先级）
          const existingTotalPriority = existingParentPortPriority + existingChildPortPriority;
          const newTotalPriority = newParentPortPriority + newChildPortPriority;

          if (newTotalPriority > existingTotalPriority) {
            console.log(
              `[拓扑调试] 使用新关系 (端口优先级更高): ${rel.parentMac} -> ${rel.childMac} (${newTotalPriority}) 替换 ${existingRel.parentMac} -> ${existingRel.childMac} (${existingTotalPriority})`
            );
            const existingIndex = finalRelations.findIndex(r => r === existingRel);
            if (existingIndex !== -1) {
              finalRelations.splice(existingIndex, 1);
            }
            childNodeMap.set(childMac, rel);
            finalRelations.push(rel);
          } else {
            console.log(
              `[拓扑调试] 保留现有关系 (端口优先级更高或相等): ${existingRel.parentMac} -> ${existingRel.childMac} (${existingTotalPriority}) vs ${rel.parentMac} -> ${rel.childMac} (${newTotalPriority})`
            );
          }
        } else {
          console.log(
            `[拓扑调试] 保留现有关系 (优先级更高): ${existingRel.parentMac} -> ${existingRel.childMac} (${existingRel.relationLevel}) vs ${rel.parentMac} -> ${rel.childMac} (${rel.relationLevel})`
          );
        }
      } else {
        // 新的子节点，直接添加
        childNodeMap.set(childMac, rel);
        finalRelations.push(rel);
      }
    });

    console.log(`[拓扑调试] 严格树形结构处理完成: ${relations.length} -> ${finalRelations.length}`);

    return finalRelations;
  };

  /**
   * 处理SubDev和Peer关系的去重
   */
  const deduplicateSubDevRelations = (relations: DeviceRelation[]): DeviceRelation[] => {
    const peerRelations = relations.filter(rel => !rel.isSubDev);
    const subDevRelations = relations.filter(rel => rel.isSubDev);

    // 创建Peer关系的设备对集合（父子关系对）
    const peerRelationPairs = new Set<string>();
    peerRelations.forEach(rel => {
      // 双向检查，因为关系可能是A->B或B->A
      peerRelationPairs.add(`${rel.parentMac}-${rel.childMac}`);
      peerRelationPairs.add(`${rel.childMac}-${rel.parentMac}`);
    });

    // 只过滤掉与Peer关系完全重复的SubDev关系
    const uniqueSubDevRelations = subDevRelations.filter(rel => {
      const relationPair1 = `${rel.parentMac}-${rel.childMac}`;
      const relationPair2 = `${rel.childMac}-${rel.parentMac}`;
      return !peerRelationPairs.has(relationPair1) && !peerRelationPairs.has(relationPair2);
    });

    console.log("[拓扑调试] SubDev去重:", {
      原始SubDev关系: subDevRelations.length,
      保留SubDev关系: uniqueSubDevRelations.length,
      Peer关系: peerRelations.length,
      被过滤的SubDev关系: subDevRelations
        .filter(rel => {
          const relationPair1 = `${rel.parentMac}-${rel.childMac}`;
          const relationPair2 = `${rel.childMac}-${rel.parentMac}`;
          return peerRelationPairs.has(relationPair1) || peerRelationPairs.has(relationPair2);
        })
        .map(rel => `${rel.parentMac}(${rel.parentPort}) -> ${rel.childMac}(${rel.childPort})`),
      保留的SubDev关系: uniqueSubDevRelations.map(
        rel => `${rel.parentMac}(${rel.parentPort}) -> ${rel.childMac}(${rel.childPort})`
      )
    });

    return [...peerRelations, ...uniqueSubDevRelations];
  };

  /**
   * 检测关系中的循环引用
   */
  const detectCycles = (relations: DeviceRelation[]): { hasCycles: boolean; cycles: string[][] } => {
    const graph = new Map<string, string[]>();
    const cycles: string[][] = [];

    // 构建图
    relations.forEach(rel => {
      if (!graph.has(rel.parentMac)) {
        graph.set(rel.parentMac, []);
      }
      graph.get(rel.parentMac)!.push(rel.childMac);
    });

    // DFS检测循环
    const visited = new Set<string>();
    const recStack = new Set<string>();
    const path: string[] = [];

    const dfs = (node: string): boolean => {
      if (recStack.has(node)) {
        // 找到循环，记录循环路径
        const cycleStart = path.indexOf(node);
        const cycle = path.slice(cycleStart).concat([node]);
        cycles.push(cycle);
        return true;
      }

      if (visited.has(node)) {
        return false;
      }

      visited.add(node);
      recStack.add(node);
      path.push(node);

      const neighbors = graph.get(node) || [];
      for (const neighbor of neighbors) {
        if (dfs(neighbor)) {
          return true;
        }
      }

      recStack.delete(node);
      path.pop();
      return false;
    };

    let hasCycles = false;
    for (const node of graph.keys()) {
      if (!visited.has(node)) {
        if (dfs(node)) {
          hasCycles = true;
        }
      }
    }

    return { hasCycles, cycles };
  };

  /**
   * 移除导致循环的关系
   */
  const removeCyclicRelations = (relations: DeviceRelation[], cycles: string[][]): DeviceRelation[] => {
    const cyclePairs = new Set<string>();

    // 收集所有循环中的连接对
    cycles.forEach(cycle => {
      for (let i = 0; i < cycle.length - 1; i++) {
        const pair1 = `${cycle[i]}-${cycle[i + 1]}`;
        const pair2 = `${cycle[i + 1]}-${cycle[i]}`;
        cyclePairs.add(pair1);
        cyclePairs.add(pair2);
      }
    });

    // 移除循环关系，优先保留优先级高的关系
    const filteredRelations = relations.filter(rel => {
      const pairKey = `${rel.parentMac}-${rel.childMac}`;
      if (cyclePairs.has(pairKey)) {
        // 检查是否有反向关系
        const reversePair = `${rel.childMac}-${rel.parentMac}`;
        const reverseRel = relations.find(r => `${r.parentMac}-${r.childMac}` === reversePair);

        if (reverseRel) {
          // 保留优先级更高的关系
          return rel.relationLevel <= reverseRel.relationLevel;
        }
      }
      return true;
    });

    console.log(`[循环检测] 移除了 ${relations.length - filteredRelations.length} 个循环关系`);
    return filteredRelations;
  };

  /**
   * 从关系列表构建拓扑树
   */
  const buildTopologyTree = (relations: DeviceRelation[], deviceList: any[]): any[] => {
    // 收集所有涉及的MAC地址
    const allMacs = new Set<string>();
    relations.forEach(rel => {
      allMacs.add(rel.parentMac);
      allMacs.add(rel.childMac);
    });

    console.log("[拓扑调试] 需要创建节点的MAC地址:", Array.from(allMacs));

    // 创建节点映射和子节点映射
    const nodeMap = new Map<string, any>();
    const childrenMap = new Map<string, any[]>();

    // 为每个MAC创建节点
    allMacs.forEach(mac => {
      let deviceInfo = deviceList.find(device => {
        const deviceMac = (device.mac || device.macaddr || "").toLowerCase();
        return deviceMac === mac.toLowerCase();
      });

      // 如果在deviceList中找不到，尝试从关系的rawData中获取设备信息
      if (!deviceInfo) {
        const relatedRelations = relations.filter(rel => rel.parentMac === mac || rel.childMac === mac);

        // 查找包含此MAC的关系，获取rawData信息
        for (const relation of relatedRelations) {
          if (relation.rawData) {
            const rawData = relation.rawData;
            const rawMac = (rawData.macaddr || rawData.mac || "").toLowerCase();

            if (rawMac === mac.toLowerCase()) {
              deviceInfo = {
                mac: rawData.macaddr || rawData.mac,
                macaddr: rawData.macaddr || rawData.mac,
                deviceType: rawData.deviceType,
                deviceModel: rawData.model,
                model: rawData.model,
                deviceName: rawData.name || rawData.deviceName,
                name: rawData.name || rawData.deviceName,
                ipaddr: rawData.ipaddr,
                offline: rawData.offline,
                sport: rawData.sport,
                dport: rawData.dport,
                medium: rawData.medium
              };

              console.log(`[拓扑调试] 从rawData为MAC ${mac} 创建设备信息:`, {
                deviceType: deviceInfo.deviceType,
                model: deviceInfo.model,
                name: deviceInfo.name,
                offline: deviceInfo.offline
              });
              break;
            }
          }
        }

        // 如果还是没有找到，创建默认设备信息
        if (!deviceInfo) {
          deviceInfo = { mac: mac, macaddr: mac };
        }
      }

      console.log(`[拓扑调试] 为MAC ${mac} 创建节点:`, {
        设备信息: deviceInfo,
        设备名称: deviceInfo.deviceName || deviceInfo.name,
        设备类型: deviceInfo.deviceType
      });

      // 使用现有的createDeviceNode函数创建完整的设备节点
      const deviceNode = createDeviceNode(deviceInfo, 0, deviceList);

      if (deviceNode) {
        // 确保节点有正确的结构
        deviceNode.children = [];
        nodeMap.set(mac, deviceNode);
        childrenMap.set(mac, []);
      }
    });

    // 创建关系映射，存储端口信息
    const relationMap = new Map<string, DeviceRelation>();
    relations.forEach(rel => {
      relationMap.set(`${rel.parentMac}-${rel.childMac}`, rel);
    });

    // 建立父子关系，同时保存端口信息
    relations.forEach(rel => {
      const parentChildren = childrenMap.get(rel.parentMac);
      if (parentChildren && !parentChildren.includes(rel.childMac)) {
        parentChildren.push(rel.childMac);
        console.log(`[拓扑调试] 建立关系: ${rel.parentMac} -> ${rel.childMac}`, {
          parentPort: rel.parentPort,
          childPort: rel.childPort,
          relationLevel: rel.relationLevel
        });
      }
    });

    // 构建树结构，添加端口信息
    childrenMap.forEach((children, parentMac) => {
      const parentNode = nodeMap.get(parentMac);
      if (parentNode) {
        parentNode.children = children
          .map(childMac => {
            const childNode = nodeMap.get(childMac);
            if (childNode) {
              // 获取关系信息，添加端口信息
              const relation = relationMap.get(`${parentMac}-${childMac}`);
              if (relation) {
                // 为子节点添加上联口信息（sport: 连接到父节点的端口）
                if (!childNode.extra) childNode.extra = {};
                childNode.extra.sport = relation.childPort || "";

                // 为子节点添加下联口信息（dport: 父节点连接到此子节点的端口）
                childNode.extra.dport = relation.parentPort || "";

                // 不在这里设置父节点的dport，因为父节点可能有多个子节点
                // 父节点的dport应该在显示时动态计算

                // 添加连接线类型信息 - 根据端口类型判断medium
                let medium = relation.medium || "CABLE";
                // 如果没有medium字段，根据端口类型推断
                if (!relation.medium) {
                  const parentPort = relation.parentPort || "";
                  const childPort = relation.childPort || "";
                  // RA端口通常是无线连接
                  if (parentPort.toUpperCase().includes("RA") || childPort.toUpperCase().includes("RA")) {
                    medium = "RADIO";
                  }
                }
                childNode.medium = medium;

                // 确保extra对象存在并设置medium
                if (!childNode.extra) childNode.extra = {};
                childNode.extra.medium = medium;

                // 检查父子都是网桥的情况，子节点使用网桥客户端图标
                const parentDeviceType = parentNode.extra?.deviceType || parentNode.deviceType || "";
                const childDeviceType = childNode.extra?.deviceType || childNode.deviceType || "";

                if (parentDeviceType === "bridge" && childDeviceType === "bridge") {
                  // 判断子节点在线状态
                  const isChildOnline = childNode.offline === 0 || childNode.offline === undefined;

                  // 使用网桥客户端图标
                  childNode.symbol = "image://" + device3_icon;
                  childNode.isBridgeClient = true;

                  console.log(`[拓扑调试] 父子都是网桥，子节点 ${childNode.name} 使用网桥客户端图标`, {
                    父设备类型: parentDeviceType,
                    子设备类型: childDeviceType,
                    子设备在线状态: isChildOnline,
                    子设备图标: childNode.symbol
                  });
                }

                console.log(`[拓扑调试] 端口信息添加: ${parentMac} -> ${childMac}`, {
                  子节点上联口sport: relation.childPort,
                  子节点下联口dport: relation.parentPort,
                  childNodeSport: childNode.extra.sport,
                  childNodeDport: childNode.extra.dport,
                  medium: medium,
                  parentDeviceType: parentDeviceType,
                  childDeviceType: childDeviceType,
                  isBridgeClient: childNode.isBridgeClient
                });
              }
            }
            return childNode;
          })
          .filter(Boolean);
      }
    });

    // 找到根节点（没有作为子节点的节点）
    const childMacs = new Set<string>();
    relations.forEach(rel => childMacs.add(rel.childMac));

    const rootNodes = Array.from(allMacs)
      .filter(mac => !childMacs.has(mac))
      .map(mac => nodeMap.get(mac))
      .filter(Boolean);

    console.log("[拓扑调试] 找到根节点:", rootNodes.map(n => n.name).join(", "));
    return rootNodes;
  };
  /* 获取节点显示名称
   * 优先级：deviceName > topology.name > deviceId > "未知"
   */
  const getNodeName = (deviceInfo: any): string => {
    if (deviceInfo.deviceName) return deviceInfo.deviceName;
    if (deviceInfo.name) return deviceInfo.name;
    if (deviceInfo.deviceId) return deviceInfo.deviceId;
    return t("topology.unknown") || "未知";
  };

  /**
   * 选择出口设备（Exit设备）
   * 基于相同端口优先级的设备中选择一个作为出口
   */
  const selectExitDevices = (relations: DeviceRelation[]): string[] => {
    // 按端口优先级分组
    const priorityGroups = new Map<number, Set<string>>();

    relations.forEach(rel => {
      if (!priorityGroups.has(rel.relationLevel)) {
        priorityGroups.set(rel.relationLevel, new Set());
      }
      priorityGroups.get(rel.relationLevel)!.add(rel.parentMac);
      priorityGroups.get(rel.relationLevel)!.add(rel.childMac);
    });

    const exitDevices: string[] = [];

    // 从每个优先级组中选择一个设备作为Exit设备
    priorityGroups.forEach(macs => {
      if (macs.size > 1) {
        // 选择第一个作为Exit设备（可以根据需要调整选择逻辑）
        const firstMac = Array.from(macs)[0];
        exitDevices.push(firstMac);
      }
    });

    return exitDevices;
  };

  /**
   * 新的拓扑数据生成函数（两阶段方法）
   */
  const generateDataNew = (deviceList: any[], topologyResList: any[]): any[] => {
    console.log(t("topology.startNewSystem") || "[拓扑重构] 开始新的拓扑构建流程");

    // 第一阶段：收集所有设备关系
    const allRelations = collectAllDeviceRelations(deviceList, topologyResList);
    console.log(t("topology.phase1Complete") || "[拓扑重构] 第一阶段完成，收集到关系数量:", allRelations.length);

    // 调试：打印收集到的关系
    console.log(
      "[拓扑调试] 收集到的所有关系:",
      allRelations.map(rel => ({
        parent: rel.parentMac + "(" + rel.parentPort + ")",
        child: rel.childMac + "(" + rel.childPort + ")",
        level: rel.relationLevel,
        isSubDev: rel.isSubDev
      }))
    );

    // 第二阶段：关系优化
    let optimizedRelations = allRelations;

    // 步骤1：处理RA关系的优先级
    optimizedRelations = handleRARelationshipPriority(optimizedRelations);

    // 步骤2：根据设备类型优先级解决冲突
    optimizedRelations = resolveDeviceTypePriority(optimizedRelations, deviceList);

    // 步骤3：应用关系反转逻辑
    optimizedRelations = applyRelationReversal(optimizedRelations, deviceList);

    // 步骤4：严格的节点去重 - 确保每个节点只能有一个父节点
    optimizedRelations = enforceStrictTreeStructure(optimizedRelations);

    // 第三阶段：构建拓扑树
    const topologyTree = buildTopologyTree(optimizedRelations, deviceList);
    console.log(t("topology.treeConstructComplete") || "[拓扑重构] 拓扑树构建完成，根节点数量:", topologyTree.length);

    return topologyTree;
  };

  /**
   * 生成拓扑图数据
   * @param topologyResList 设备数据列表
   * @param deviceList 主设备信息列表
   * @param t 国际化函数
   */
  const generateData = async (topologyResList: any, deviceList: any, t: any) => {
    console.log(t("topology.usingNewSystem") || "[拓扑重构] 使用新的拓扑构建系统");

    // 使用新的两阶段拓扑构建方法
    const newTopologyTree = generateDataNew(deviceList, topologyResList);

    // 清空原有拓扑数据并设置新的拓扑树
    topologyData.children = newTopologyTree;

    // 处理网桥客户端显示（保留原有逻辑）
    processBridgeClients(topologyData);

    // 输出日志
    try {
      console.log("拓扑图数据生成完成:", JSON.stringify(topologyData, getCircularReplacer()));
    } catch (e) {
      console.log("拓扑图数据生成完成（对象有循环引用，无法序列化为JSON）:", topologyData);
    }
    console.log("\n节点层级结构:");
    logNodeStructure(topologyData, 0);

    // 确保视图更新
    await nextTick();
    return topologyData;
  };

  // 组装拓扑图数据
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const assembleTopologyData = (topologyDataList: any[]) => {
    // 用于记录每个 mac 的主节点
    const mainNodeMap = new Map(); // mac -> 主节点
    const toDeleteIdxs: Set<number> = new Set();

    // 合并同 mac 的节点，只保留第一个出现的主节点
    for (let i = 0; i < topologyDataList.length; i++) {
      const node = topologyDataList[i];
      if (node && node.mac) {
        const mac = node.mac.toLowerCase();
        if (mainNodeMap.has(mac)) {
          console.log(t("topology.mergeNodeFoundMainNode") + ":", mac);
          // 已有主节点，当前节点合并到主节点，并标记为要删除
          const mainNode = mainNodeMap.get(mac);

          // 合并设备信息，确保信息完整
          // 如果主节点的名称是未知或为空，而新节点有有效名称，则更新名称
          if ((!mainNode.name || mainNode.name === t("topology.unknown")) && node.name && node.name !== t("topology.unknown")) {
            mainNode.name = node.name;
          }
          // 如果主节点没有 deviceId，而新节点有，则更新 deviceId
          if (!mainNode.deviceId && node.deviceId) {
            mainNode.deviceId = node.deviceId;
          }
          // 合并 extra 信息，新节点（node）的信息优先级更高，会覆盖主节点（mainNode）的旧信息
          mainNode.extra = { ...mainNode.extra, ...node.extra };

          // 合并子节点
          if (Array.isArray(node.children) && node.children.length > 0) {
            if (!Array.isArray(mainNode.children)) mainNode.children = [];
            mainNode.children.push(...node.children);
            // 合并完子节点后，进行去重
            mainNode.children = dedupeNodes(mainNode.children);
          }
          toDeleteIdxs.add(i);
        } else {
          // 当前节点作为主节点
          mainNodeMap.set(mac, node);
        }
      }
    }

    // 倒序删除被合并的节点
    [...toDeleteIdxs]
      .sort((a: number, b: number) => b - a)
      .forEach((idx: number) => {
        topologyDataList.splice(idx, 1);
      });

    // 递归查找父节点
    const findRootNodes = (nodes: any[]): any[] => {
      const rootNodes: any[] = [];
      for (const node of nodes) {
        if (!node.parentNode) {
          rootNodes.push(node);
        }
      }
      return rootNodes;
    };

    // 将根节点添加到拓扑数据中
    const rootNodes = findRootNodes(topologyDataList);
    topologyData.children = rootNodes;
  };

  /**
              if (!exists) {
                topologyDataList.push(deviceNode);
              }
            } else {
              // 如果没有MAC地址，仍然添加节点但记录警告
              console.warn(t("topology.noMacAddress"), device);
              topologyDataList.push(deviceNode);
            }
            console.warn(t("topology.noTopologyData"));
            continue; // 修改这里：将return改为continue
          }
          device.system = topologyRes.data?.system;
  
          // 处理拓扑数据
          if (topologyRes.data?.system === undefined) {
            // 处理超时设备
            const timeoutDeviceNode = createDeviceNode(device, device.status, deviceList);
            // 添加防御性检查，确保timeoutDeviceNode.mac存在
            if (timeoutDeviceNode && timeoutDeviceNode.mac) {
              const exists = topologyDataList.find(n => n && (n.mac || n.macaddr) === timeoutDeviceNode.mac);
              if (!exists) {
                topologyDataList.push(timeoutDeviceNode);
              }
            } else {
              // 如果没有MAC地址，仍然添加节点但记录警告
              console.warn(t("topology.noMacAddress"), device);
              topologyDataList.push(timeoutDeviceNode);
            }
          } else {
            // let subTopology: TopologyNode | null = null;
            let peerTopology: TopologyNode;
            // 先收集所有peer设备的MAC地址
            const allPeerDevices = topologyRes.data?.system?.topology?.peer || [];
  
            // 处理peer设备
            allPeerDevices.forEach((peer: any) => {
              const peerMac = (peer.macaddr || peer.mac || "").toUpperCase();
              if (peerMac === "90-E2-FC-02-A8-74") {
                console.log(`[特殊MAC调试] 处理peer设备 ${peerMac}:`, {
                  原始peer数据: peer,
                  主设备信息: {
                    deviceId: device.deviceId,
                    deviceName: device.deviceName,
                    mac: device.mac
                  }
                });
              }
              peerTopology = processDeviceRelation(
                peer,
                device,
                peer.offline || 0,
                false,
                topologyDataList,
                peerTopology,
                deviceList
              );
            });
  
            // 处理sub设备
            const subDevices = topologyRes.data?.system?.topology?.subDev || [];
            subDevices.forEach((sub: any) => {
              const macaddr = sub.macaddr || sub.mac;
              const subMac = (macaddr || "").toUpperCase();
  
              if (subMac === "90-E2-FC-02-A8-74") {
                console.log(`[特殊MAC调试] 处理subDev设备 ${subMac}:`, {
                  原始sub数据: sub,
                  主设备信息: {
                    deviceId: device.deviceId,
                    deviceName: device.deviceName,
                    mac: device.mac
                  }
                });
              }
  
              // 如果节点已存在，则跳过
              // const existingNode = topologyDataList.find(
              //   node => (node.mac || node.macaddr)?.toLowerCase() === macaddr?.toLowerCase()
              // );
              // if (macaddr && existingNode && existingNode.parentNode) {
              //   const subNode = createDeviceNode(sub, sub.offline || 0);
              //   // 补全信息
              //   if ((existingNode?.name == null || existingNode?.name === "") && subNode.name) {
              //     existingNode.name = subNode.name;
              //   }
              //   if ((existingNode?.deviceId == null || existingNode?.deviceId === "") && subNode.deviceId) {
              //     existingNode.deviceId = subNode.deviceId;
              //   }
              //   console.log(`子设备 ${subNode.name || ""} (MAC: ${macaddr}) 已经存在于peer数据中，跳过显示`);
              //   return;
              // }
              // 这里 subTopology 应该是 TopologyNode 类型
              peerTopology = processDeviceRelation(sub, device, sub.offline || 0, true, topologyDataList, peerTopology, deviceList);
              // if (result && typeof result === "object") {
              //   subTopology = result;
              // }
            });
            // if (subTopology) {
            //   addNodeWithDedupe(subTopology, peerTopology);
            // }
            topologyDataList.push(peerTopology);
  
            // 处理孤立设备
            if (allPeerDevices.length === 0 && subDevices.length === 0) {
              console.log(`设备 ${device.deviceId} 没有peer和subDev连接，作为孤立设备添加到拓扑图中`);
              const isolatedDeviceNode = createDeviceNode(device, device.status, deviceList);
              if (topologyRes.data?.system?.topology) {
                isolatedDeviceNode.extra.topology = topologyRes.data.system.topology;
              }
              // addNodeWithDedupe(isolatedDeviceNode, topologyData);
              topologyDataList.push(isolatedDeviceNode);
            }
          }
        } catch (error) {
          console.error(t("topology.loadFailed") + ":", error);
        }
      }
  
      // for (const subTopology of topologyDataList) {
      // 删除重复子设备
      removeSubDevWithSameMacAsPeer(topologyDataList);
  
      // 合并重复节点
      mergeDuplicateMacNodes(topologyDataList);
      for (const subTopology of topologyDataList) {
        removeParentField(subTopology);
      }
      // }
  
      // 构建父节点映射
      // buildParentMapAndLinkInfo(topologyData);
      // 组装拓扑图数据
      assembleTopologyData(topologyDataList);
  
      // 处理网桥客户端显示
      processBridgeClients(topologyData);
  
      // mergeDuplicateMacNodes(topologyDataList);
      // removeParentField(topologyData);
      // 输出日志
      try {
        console.log("拓扑图数据生成完成:", JSON.stringify(topologyData, getCircularReplacer()));
      } catch (e) {
        console.log("拓扑图数据生成完成（对象有循环引用，无法序列化为JSON）:", topologyData);
      }
      console.log("\n节点层级结构:");
      logNodeStructure(topologyData, 0);
  
      // 确保视图更新
      await nextTick();
      return topologyData; // 将return移到循环外部
    };
  
    // 组装拓扑图数据
    const assembleTopologyData = (topologyDataList: any[]) => {
      // 用于记录每个 mac 的主节点
      const mainNodeMap = new Map(); // mac -> 主节点
      const toDeleteIdxs: Set<number> = new Set();
  
      // 合并同 mac 的节点，只保留第一个出现的主节点
      for (let i = 0; i < topologyDataList.length; i++) {
        const node = topologyDataList[i];
        if (node && node.mac) {
          const mac = node.mac.toLowerCase();
          if (mainNodeMap.has(mac)) {
            console.log("找到父节点mac:", mac);
            // 已有主节点，当前节点合并到主节点，并标记为要删除
            const mainNode = mainNodeMap.get(mac);
            // 合并子节点
            // 合并设备信息，确保信息完整
            // 如果主节点的名称是未知或为空，而新节点有有效名称，则更新名称
            if ((!mainNode.name || mainNode.name === t("topology.unknown")) && node.name && node.name !== t("topology.unknown")) {
              mainNode.name = node.name;
            }
            // 如果主节点没有 deviceId，而新节点有，则更新 deviceId
            if (!mainNode.deviceId && node.deviceId) {
              mainNode.deviceId = node.deviceId;
            }
            // 合并 extra 信息，新节点（node）的信息优先级更高，会覆盖主节点（mainNode）的旧信息
            mainNode.extra = { ...mainNode.extra, ...node.extra };
  
            // 合并子节点
            if (Array.isArray(node.children) && node.children.length > 0) {
              if (!Array.isArray(mainNode.children)) mainNode.children = [];
              mainNode.children.push(...node.children);
              // 合并完子节点后，进行去重
              mainNode.children = dedupeNodes(mainNode.children);
            }
            toDeleteIdxs.add(i);
          } else {
            // 当前节点作为主节点
            mainNodeMap.set(mac, node);
          }
        }
      }
  
      // 倒序删除被合并的节点
      [...toDeleteIdxs]
        .sort((a: number, b: number) => b - a)
        .forEach((idx: number) => {
          topologyDataList.splice(idx, 1);
        });
  
      // 递归查找父节点
      function findParentNode(node: any, candidates: any[], visited = new Set()): any | null {
        for (const candi of candidates) {
          if (!candi) continue;
          if (visited.has(candi)) continue; // 防止死循环
          visited.add(candi);
  
          if (candi.mac === node.mac) {
            node.parentNode = candi;
            if (Array.isArray(candi.children)) {
              if (Array.isArray(node.children) && node.children.length > 0) {
                candi.children.push(...node.children);
              }
            } else {
              candi.children = node.children ? [...node.children] : [];
            }
            return candi;
          }
          if (candi.children && candi.children.length > 0) {
            const found = findParentNode(node, candi.children, visited);
            if (found) return found;
          }
        }
        return null;
      }
  
      // 先收集需要挂载的节点和索引
      const toMount: Array<{ node: any[]; idx: number; parent: any }> = [];
      for (let i = 0; i < topologyDataList.length; i++) {
        const node = topologyDataList[i];
        if (node && node.mac) {
          // 构造不包含当前节点的临时数组
          const candidates = topologyDataList.slice(0, i).concat(topologyDataList.slice(i + 1));
          const parent = findParentNode(node, candidates);
          if (parent && node.children.length > 0) {
            toMount.push({ node, idx: i, parent });
          }
        }
      }
  
      // 先收集所有要删除的索引（已合并的节点已删，这里一般不会重复）
      const toDeleteIdxs2: number[] = toMount.map(item => Number(item.idx)).sort((a, b) => b - a);
      // 倒序删除
      toDeleteIdxs2.forEach((idx: number) => {
        topologyDataList.splice(idx, 1);
      });
  
      // 剩下的都是一级节点，挂到 topologyData.children
      topologyDataList.forEach(item => {
        if (!item) return; // 防御性处理
        addNodeWithDedupe(item, topologyData);
      });
    };
  
    /**
     * 处理网桥客户端显示
     * 如果上下两级都是网桥，下级网桥显示为网桥客户端
     */
  const processBridgeClients = (node: any = topologyData) => {
    if (!node || !node.children) return;

    // 递归处理子节点
    node.children.forEach((child: any) => {
      // 跳过internet根节点，从实际设备节点开始检查
      // 检查当前节点和子节点是否都是网桥（排除internet根节点）
      if (node.name !== "internet" && isBridgeDevice(node) && isBridgeDevice(child)) {
        // 将子节点转换为网桥客户端显示
        convertToBridgeClient(child);
        console.log(`将网桥设备 ${child.name} 转换为网桥客户端显示`);
      }

      // 递归处理子节点的子节点
      processBridgeClients(child);
    });
  };

  /**
   * 判断节点是否为网桥设备
   * @param node 节点
   * @returns 是否为网桥设备
   */
  const isBridgeDevice = (node: any): boolean => {
    if (!node || !node.extra) return false;
    return node.extra.deviceType === "bridge";
  };

  /**
   * 将网桥节点转换为网桥客户端显示
   * @param node 网桥节点
   */
  const convertToBridgeClient = (node: any) => {
    if (!node) return;

    // 更新图标为网桥客户端图标
    const isOffline = node.extra?.status !== 0;
    if (isOffline) {
      // 离线状态，使用灰色图标
      node.symbol = "image://gray:" + device3_icon;
    } else {
      // 在线状态，使用正常图标
      node.symbol = "image://" + device3_icon;
    }

    // 可以添加特殊标记，表明这是网桥客户端
    node.isBridgeClient = true;

    console.log(`节点 ${node.name} 已转换为网桥客户端显示，图标：${node.symbol}`);
  };

  /**
   * 处理设备关系的通用函数
   * @param relatedDevice peer或subDev设备
   * @param mainDevice 主设备
   * @param itemStatus 状态
   * @param isSubDevice 是否为subDev设备
   * @returns TopologyNode | null
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const processDeviceRelation = (
    relatedDevice: any,
    mainDevice: any,
    itemStatus: number,
    isSubDevice: boolean,
    topologyDataList?: any[], // 新增参数，递归查找用
    subTopology?: any, // subDev的集合或者peer的集合，可为TopologyNode或undefined
    deviceList?: any[] // 完整的设备列表，用于查找完整设备信息
  ): TopologyNode | null => {
    // 检查参数是否有效
    if (!relatedDevice || !mainDevice) {
      console.error("processDeviceRelation: relatedDevice or mainDevice is undefined", { relatedDevice, mainDevice });
      return null;
    }

    // 获取设备的MAC地址
    const macaddr = relatedDevice.mac || relatedDevice.macaddr;

    // 递归查找树结构中 macaddr 匹配的节点
    function findNodeByMac(list: any[], mac: string): any | null {
      for (const node of list) {
        if ((node.mac || node.macaddr)?.toLowerCase() === mac.toLowerCase()) {
          return node;
        }
        if (node.children && node.children.length > 0) {
          const found = findNodeByMac(node.children, mac);
          if (found) return found;
        }
      }
      return null;
    }

    // 如果是子设备，先检查MAC地址是否已经存在
    if (isSubDevice && macaddr && topologyDataList) {
      if (isMacAddressExists(macaddr)) {
        console.log(`子设备 ${relatedDevice.name || ""} (MAC: ${macaddr}) 已经存在于拓扑图中，跳过显示`);
        const existingNode = findNodeByMac(topologyDataList, macaddr);
        if (existingNode && (existingNode.name == null || existingNode.name === "") && relatedDevice.name) {
          existingNode.name = relatedDevice.name;
        }
        if (existingNode && (existingNode.deviceId == null || existingNode.deviceId === "") && relatedDevice.deviceId) {
          existingNode.deviceId = relatedDevice.deviceId;
        }
        return subTopology; // 跳过这个子设备
      }
    }

    console.log(
      `处理设备关系: ${mainDevice.deviceName || mainDevice.name} <-> ${relatedDevice.deviceName || relatedDevice.name}`
    );
    console.log(`  主设备状态: ${mainDevice.status}, 关联设备状态: ${relatedDevice.status}, 是否为子设备: ${isSubDevice}`);
    console.log(`  关联设备端口: sport=${relatedDevice.sport}, dport=${relatedDevice.dport}`);

    // 标记设备类型，便于后续过滤
    if (isSubDevice) {
      relatedDevice.topologyType = "subDev";
    } else {
      relatedDevice.topologyType = "peer";
    }

    // 对于peer或subDev设备，应使用sn字段作为设备自身的序列号，deviceId字段指向父级设备
    if (relatedDevice.sn) {
      console.log(`  关联设备有sn字段: ${relatedDevice.sn}，使用它作为设备自身的序列号`);
      // 备份原始的deviceId，它指向父级设备
      relatedDevice.parentDeviceId = relatedDevice.deviceId;
      // 使用sn作为设备自身的序列号
      relatedDevice.deviceId = relatedDevice.sn;
    }

    // 如果设备有MAC地址，将其添加到集合中
    if (macaddr) {
      addMacAddress(macaddr, relatedDevice);
    }

    // 确定节点关系方向
    let isReverse = isNodeReverse(relatedDevice);

    // 构建主设备节点，先查找subTopology中是否已存在
    let mainNode = null;
    // let mainExist = true;
    if (mainDevice.mac || mainDevice.macaddr) {
      const mainMac = (mainDevice.mac || mainDevice.macaddr).toLowerCase();
      // 先从整体数据中查找
      // if (topologyDataList && topologyDataList.length > 0) {
      //   for (const topo of topologyDataList) {
      //     if (topo && mainNode === topo.mac?.toLowerCase()) {
      //       mainNode = topo;
      //     }
      //     if (topo && Array.isArray(topo.children)) {
      //       mainNode = findNodeByMac(topo.children, mainMac);
      //     }
      //   }
      // }
      // 找不到则从子数据中查找
      if (!mainNode) {
        if (subTopology && mainMac === subTopology.mac?.toLowerCase()) {
          mainNode = subTopology;
        }
        if (!mainNode) {
          if (subTopology && Array.isArray(subTopology.children) && subTopology.children[0].mac?.toLowerCase() === mainMac) {
            mainNode = subTopology.children[0];
          }
        }
      }
    }
    // 尝试从设备列表中获取关联设备的完整信息
    const completeRelatedDevice = deviceList ? findCompleteDeviceInfo(relatedDevice, deviceList) : relatedDevice;

    // 特殊MAC地址的调试日志
    const relatedMac = (completeRelatedDevice.macaddr || completeRelatedDevice.mac || "").toLowerCase();
    if (relatedMac === "90-e2-fc-02-a8-74") {
      console.log(`[特殊MAC调试] 处理设备关系 ${relatedMac}:`, {
        原始设备数据: relatedDevice,
        完整设备数据: completeRelatedDevice,
        主设备信息: {
          deviceId: mainDevice.deviceId,
          deviceName: mainDevice.deviceName,
          mac: mainDevice.mac
        },
        是否为子设备: isSubDevice
      });
    }

    if (!mainNode) {
      mainNode = createDeviceNode(mainDevice, itemStatus, deviceList);
    }

    // 构建关联设备节点，先查找subTopology中是否已存在existNode
    let existNode = null;
    let relatedNode: any = null;
    if (completeRelatedDevice.mac || completeRelatedDevice.macaddr) {
      const relatedMac = (completeRelatedDevice.mac || completeRelatedDevice.macaddr).toLowerCase();
      if (topologyDataList && topologyDataList.length > 0) {
        for (const topo of topologyDataList) {
          if (topo && relatedMac === topo.mac?.toLowerCase()) {
            existNode = topo;
            break;
          }
          if (!existNode) {
            if (topo && Array.isArray(topo.children)) {
              existNode = findNodeByMac(topo.children, relatedMac);
            }
          }
        }
      }
      if (!existNode) {
        if (subTopology && relatedMac === subTopology.mac?.toLowerCase()) {
          existNode = subTopology;
        }
        if (!existNode) {
          if (subTopology && Array.isArray(subTopology.children)) {
            existNode = findNodeByMac(subTopology.children, relatedMac);
          }
        }
      }
    }
    relatedNode = createDeviceNode(completeRelatedDevice, itemStatus, deviceList);
    if (existNode) {
      // const dupNode = createDeviceNode(relatedDevice, itemStatus);
      if (relatedNode && isNodeAPriorityHigher(existNode, relatedNode) > 0 && existNode.parentNode) {
        // 如果已存在的节点优先级高
        isReverse = true;
        relatedNode.parentNode = existNode.parentNode;
      } else if (relatedNode && isNodeAPriorityHigher(existNode, relatedNode) < 0) {
        // 如果已存在的节点优先级低
        // isReverse = false;
        let topLevel;
        // 如果已存在的节点还有父节点，则将其从父节点的子节点列表中删除,并将该节点到顶级节点的父子关系反转
        const parentNode = existNode.parentNode;
        const grandParentNode = existNode?.parentNode?.parentNode;
        existNode.parentNode = null;
        if (grandParentNode) {
          grandParentNode.parentNode = null;
          grandParentNode.children.splice(grandParentNode.children.indexOf(parentNode), 1);
          parentNode.children.push(grandParentNode);
          dedupeNodes(parentNode.children);
          grandParentNode.parentNode = parentNode;
          topLevel = grandParentNode;
        }

        if (parentNode) {
          parentNode?.children.splice(parentNode.children.indexOf(existNode), 1);
          parentNode.parentNode = existNode;

          // console.log(
          //   "已存在的节点优先级低，将其从父节点的子节点列表中删除,并将该节点到顶级节点的父子关系反转:" +
          //     JSON.stringify(relatedNode.parentNode)
          // );
          existNode.children.push(parentNode);
          dedupeNodes(existNode.children);
          topLevel = parentNode;
        }
        if (topLevel != null && topLevel != undefined) {
          for (let i = 0; i < topologyDataList.length; i++) {
            if (topologyDataList[i].mac == topLevel.mac) {
              topologyDataList[i] = existNode;
            }
          }
        }
      }
      console.log(`  在子拓扑中找到关联设备节点: ${existNode.name}`);
      if (!existNode.name) {
        existNode.name = relatedDevice.deviceName;
        // relatedNode.mac = device.macaddr || device.mac;
      }
      existNode.extra.dport = relatedDevice.dport;
      existNode.extra.sport = relatedDevice.sport;
      existNode.parentNode = mainNode;
    }

    // 确保 topologyData 有 children 属性
    if (!topologyData.children) {
      topologyData.children = [];
    }

    if (mainDevice.status === 1 && relatedDevice.status === 0) {
      isReverse = true;
    }

    if (!isReverse && !mainNode.parentNode && !isSamePriority(relatedNode) && !hasExplicitPortRelation(relatedDevice)) {
      // 只有在没有明确端口关系时，才根据设备类型判断是否需要反转
      const typeOrder = ["route", "ac", "switch", "reaper", "ap", "bridge"];
      const getTypePriority = (type: string) => {
        const idx = typeOrder.indexOf((type || "").toLowerCase());
        return idx === -1 ? 99 : idx;
      };
      const mainTypePriority = getTypePriority(mainDevice.deviceType);
      const relatedTypePriority = getTypePriority(relatedDevice.deviceType);
      isReverse = mainTypePriority > relatedTypePriority;

      console.log(
        `  没有明确端口关系，使用设备类型优先级判断: ${mainDevice.deviceType}(${mainTypePriority}) vs ${relatedDevice.deviceType}(${relatedTypePriority}) -> ${isReverse ? "反转" : "不反转"}`
      );
    } else if (hasExplicitPortRelation(relatedDevice)) {
      console.log(`  检测到明确的端口关系，保持端口关系决定的方向: ${isReverse ? "反转" : "不反转"}`);
    }

    if (isReverse && mainNode.parentNode && isSamePriority(relatedNode)) {
      isReverse = false;
    }

    console.log(`  节点关系方向: ${isReverse ? "反向" : "正向"}`);

    // 处理子设备关系
    let rootNodeToAdd: TopologyNode;

    if (isSubDevice) {
      console.log(`  处理子设备关系`);
      const parentNodeName = findParentNodeName(mainNode);
      console.log(`  主节点的父节点: ${parentNodeName}`);
      // 如果主节点的父节点是 "internet"，则将关联设备添加为主设备的子节点
      if (isReverse) {
        mainDevice.parentNode = relatedDevice;
        // 如果是反向关系，将主设备添加为关联设备的子节点
        rootNodeToAdd = handleSubPortRelation(mainNode, relatedNode);
      } else {
        relatedDevice.parentNode = mainDevice;
        // 如果是正向关系，将关联设备添加为主设备的子节点
        rootNodeToAdd = handleSubPortRelation(relatedNode, mainNode);
      }
    } else {
      // 处理普通设备关系
      console.log(`  处理普通设备关系`);
      if (isReverse) {
        // 如果是反向关系，将主设备添加为关联设备的子节点
        rootNodeToAdd = handleSpecialPortRelation(relatedNode, mainNode);
        // rootNodeToAdd = handleNormalPortRelation(mainNode, relatedNode);
      } else {
        // 如果是正向关系，将关联设备添加为主设备的子节点
        rootNodeToAdd = handleNormalPortRelation(relatedNode, mainNode);
      }
    }

    // 将处理后的节点添加到拓扑图根节点
    // if (rootNodeToAdd) {
    //   console.log(`  将节点 ${rootNodeToAdd.name} 添加到拓扑图根节点`);
    //   addNodeWithDedupe(rootNodeToAdd, topologyData);
    // } else {
    //   // 如果没有返回节点，则添加主节点和关联节点
    //   console.log(`  未返回节点，添加主节点和关联节点到拓扑图根节点`);
    //   addNodeWithDedupe(mainNode, topologyData);
    //   addNodeWithDedupe(relatedNode, topologyData);
    // }

    // 记录处理后的节点状态
    console.log(`  处理后的节点状态:`);
    console.log(`    主节点: ${mainNode.name}, 子节点数量: ${mainNode.children?.length || 0}`);
    console.log(`    关联节点: ${relatedNode?.name || "null"}, 子节点数量: ${relatedNode?.children?.length || 0}`);

    // 记录此设备与主设备的关系，这样其他地方可以引用到正确的端口信息
    // if (relatedDevice && mainDevice && macaddr) {
    //   // 添加到关系映射中，保存设备与其父设备的端口关系
    //   if (!relatedDevice._parentRelations) {
    //     relatedDevice._parentRelations = [];
    //   }
    //
    //   relatedDevice._parentRelations.push({
    //     parentMac: mainDevice.mac || mainDevice.macaddr,
    //     parentDeviceId: mainDevice.deviceId,
    //     sport: relatedDevice.sport,
    //     dport: relatedDevice.dport
    //   });
    //
    //   console.log(`记录设备 ${relatedDevice.name || macaddr} 与父设备 ${mainDevice.deviceName || mainDevice.name} 的端口关系`);
    // }
    // findParentNode(rootNodeToAdd, subTopology);
    // const result = mergeSubNode(rootNodeToAdd, subTopology);
    // addNodeWithDedupe(rootNodeToAdd, topologyData);
    if (!isReverse && subTopology) {
      return subTopology;
    }
    return rootNodeToAdd;
  };

  // 存储父节点映射关系
  const parentMap = new Map();
  const deviceNodesByDeviceId = new Map();
  const deviceNodesByMacAddress = new Map();

  /**
   * 构建父节点映射关系
   * 记录每个节点的父节点及其peer/subDev信息
   * @param topologyTree 拓扑树
   * @param parentNode 父节点
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const buildParentMapAndLinkInfo = (topologyTree: any, parentNode: any = null) => {
    if (!topologyTree) return;

    // 记录当前节点
    const deviceId = topologyTree.extra?.deviceId;
    const macaddr = (topologyTree.extra?.macaddr || topologyTree.extra?.mac || "").toLowerCase();

    // 记录节点引用
    if (deviceId) {
      deviceNodesByDeviceId.set(deviceId, topologyTree);
    }

    if (macaddr) {
      deviceNodesByMacAddress.set(macaddr, topologyTree);
    }

    // 记录父子关系
    if (parentNode) {
      const parentDeviceId = parentNode.extra?.deviceId;
      const parentMacaddr = (parentNode.extra?.macaddr || parentNode.extra?.mac || "").toLowerCase();

      // 记录节点的父节点关系
      if (deviceId) {
        parentMap.set(deviceId, {
          parentNode: parentNode,
          parentDeviceId: parentDeviceId,
          parentMacaddr: parentMacaddr
        });
      }

      if (macaddr) {
        parentMap.set(macaddr, {
          parentNode: parentNode,
          parentDeviceId: parentDeviceId,
          parentMacaddr: parentMacaddr
        });
      }

      // 在父节点的peer/subDev中查找当前节点的端口信息
      if (parentNode.extra?.topology) {
        const peers = parentNode.extra.topology.peer || [];
        const subDevs = parentNode.extra.topology.subDev || [];

        // 合并peer和subDev
        const allConnections = [...peers, ...subDevs];

        // 查找匹配的连接
        const matchingConnection = allConnections.find((conn: any) => {
          const connMac = (conn.macaddr || conn.mac || "").toLowerCase();
          const connDeviceId = conn.deviceId;
          return (macaddr && connMac === macaddr) || (deviceId && connDeviceId === deviceId);
        });

        // 如果找到匹配的连接，记录端口信息
        if (matchingConnection) {
          // 注意：在父节点的peer/subDev数据中，
          // sport是父节点连接到子节点的端口，应该是子节点的dport
          // dport是子节点连接到父节点的端口，应该是子节点的sport
          if (!topologyTree.extra.originalSport && matchingConnection.dport) {
            topologyTree.extra.originalSport = topologyTree.extra.sport; // 备份原始值
            topologyTree.extra.sport = matchingConnection.dport; // 父节点的dport是子节点的sport
          }

          if (!topologyTree.extra.originalDport && matchingConnection.sport) {
            topologyTree.extra.originalDport = topologyTree.extra.dport; // 备份原始值
            topologyTree.extra.dport = matchingConnection.sport; // 父节点的sport是子节点的dport
          }

          console.log(
            `为节点 ${topologyTree.name} 设置端口信息: sport=${topologyTree.extra.sport}, dport=${topologyTree.extra.dport}`
          );
        }
      }
    }

    // 递归遍历子节点
    if (Array.isArray(topologyTree.children)) {
      topologyTree.children.forEach((child: any) => {
        buildParentMapAndLinkInfo(child, topologyTree);
      });
    }
  };

  /**
   * 获取节点在父节点中的端口信息
   * @param node 当前节点
   * @returns 从父节点获取的端口信息 {sport, dport}
   */
  const getNodePortsFromParent = (node: any): { sport?: string; dport?: string } => {
    // 检查参数是否有效
    if (!node || !node.extra) {
      return { sport: undefined, dport: undefined };
    }

    // 如果节点已经有端口信息，直接返回
    if (node.extra.sport || node.extra.dport) {
      return { sport: node.extra.sport, dport: node.extra.dport };
    }

    // 获取节点的MAC地址和设备ID
    const nodeMac = node.extra.macaddr || node.extra.mac;
    const nodeDeviceId = node.extra.deviceId;

    if (!nodeMac && !nodeDeviceId) {
      return { sport: node.extra.sport, dport: node.extra.dport };
    }

    // 首先尝试使用拓扑图中的实际位置关系获取端口信息
    const nodeInfo = findNodeInTopology(node);
    if (nodeInfo && nodeInfo.parentNode) {
      const parentNode = nodeInfo.parentNode;

      // 在父节点的peer或subDev数组中查找当前节点
      const peers = parentNode.extra?.topology?.peer || [];
      const subDevs = parentNode.extra?.topology?.subDev || [];

      // 合并peer和subDev
      const allConnections = [...peers, ...subDevs];

      // 查找匹配的连接
      const match = allConnections.find((conn: any) => {
        const connMac = (conn.macaddr || conn.mac || "").toLowerCase();
        const connDeviceId = conn.deviceId;
        const nMac = nodeMac ? nodeMac.toLowerCase() : "";
        return (nMac && connMac === nMac) || (nodeDeviceId && connDeviceId === nodeDeviceId);
      });

      if (match) {
        // 注意：在父节点的peer/subDev数据中，
        // sport是父节点连接到子节点的端口，应该是子节点的dport
        // dport是子节点连接到父节点的端口，应该是子节点的sport
        const sport = match.dport; // 父节点的dport是子节点的sport
        const dport = match.sport; // 父节点的sport是子节点的dport

        console.log(`从父节点 ${parentNode.name} 中找到设备 ${node.name} 的端口信息: sport=${sport}, dport=${dport}`);
        return { sport, dport };
      }

      console.log(`在父节点 ${parentNode.name} 中未找到设备 ${node.name} 的端口信息，尝试使用parentMap`);
    }

    // 如果在拓扑中没有找到父子关系，尝试使用parentMap
    let key = null;
    if (nodeMac) {
      key = nodeMac.toLowerCase();
    } else if (nodeDeviceId) {
      key = nodeDeviceId;
    }

    if (key && parentMap.has(key)) {
      const parentInfo = parentMap.get(key);
      if (parentInfo && parentInfo.parentNode && parentInfo.parentNode.extra?.topology) {
        // 在父节点的peer/subDev中查找匹配项
        const peers = parentInfo.parentNode.extra.topology.peer || [];
        const subDevs = parentInfo.parentNode.extra.topology.subDev || [];
        const allConnections = [...peers, ...subDevs];

        // 查找匹配的设备
        const match = allConnections.find((conn: any) => {
          const connMac = (conn.macaddr || conn.mac || "").toLowerCase();
          const connDeviceId = conn.deviceId;
          const nMac = nodeMac ? nodeMac.toLowerCase() : "";
          return (nMac && connMac === nMac) || (nodeDeviceId && connDeviceId === nodeDeviceId);
        });

        if (match) {
          // 注意：在父节点的peer/subDev数据中，
          // sport是父节点连接到子节点的端口，应该是子节点的dport
          // dport是子节点连接到父节点的端口，应该是子节点的sport
          const sport = match.dport; // 父节点的dport是子节点的sport
          const dport = match.sport; // 父节点的sport是子节点的dport

          console.log(`从parentMap中找到设备 ${node.name} 的端口信息: sport=${sport}, dport=${dport}`);
          return { sport, dport };
        }
      }
    }

    // 如果都没有找到，则使用设备自身数据
    console.log(`未找到设备 ${node.name} 的父节点端口信息，使用设备自身数据`);
    return { sport: node.extra.sport, dport: node.extra.dport };
  };

  /**
   * 检查节点是否可以设置为出口设备
   * @param node 要检查的节点
   * @returns 是否可以设置为出口设备
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const isEligibleForExitDevice = (node: any): boolean => {
    // 检查节点是否有效
    if (!node || !node.extra) return false;

    // 获取节点在父节点中的端口信息
    const { sport, dport } = getNodePortsFromParent(node);

    // 转换为小写并确保有值
    const sportLower = sport?.toLowerCase() || "";
    const dportLower = dport?.toLowerCase() || "";

    // 检查端口类型是否相同
    const sportType = getPortType(sportLower);
    const dportType = getPortType(dportLower);

    console.log(`检查节点 ${node.name} 的端口类型: sport=${sportLower}(${sportType}), dport=${dportLower}(${dportType})`);

    // 同类型端口可以设为出口设备
    return sportType === dportType;
  };

  /**
   * 判断是否为特殊端口类型
   * @param device 设备信息
   * @returns 是否为特殊端口
   */
  // const isSpecialPortType = (device: any): boolean => {
  //   return (
  //     device.sport?.toLowerCase().startsWith("lan") ||
  //     device.sport?.toLowerCase().startsWith("ge") ||
  //     device.sport?.toLowerCase().startsWith("fe")
  //   );
  // };

  /**
   * 判断是否为RA端口类型
   * @param device 设备信息
   * @returns 是否为RA端口
   */
  // const isRaPortType = (device: any): boolean => {
  //   return device.sport?.toLowerCase() === "racli" || /^ra\d$/i.test(device.dport || "");
  // };

  /**
   * 判断节点是否需要反转
   * @param node
   */
  const isNodeReverse = (device: any): boolean => {
    // RAx <- RACLI
    if (/^ra\d+(\/\d+)?/i.test(device.sport) && device.dport?.toLowerCase() === "racli") return false;
    if (/^ra\d+(\/\d+)?/i.test(device.dport) && device.sport?.toLowerCase() === "racli") return true;
    // LAN(上级) <- WAN(下级)
    if (/^(lan|ge|fe|port)/i.test(device.sport) && /^wan/i.test(device.dport)) return true;
    if (/^(lan|ge|fe|port)/i.test(device.dport) && /^wan/i.test(device.sport)) return false;
    return false;
    // return (
    //   device.sport?.toLowerCase().startsWith("wan") ||
    //   device.sport?.toLowerCase() === "racli" ||
    //   device.dport?.toLowerCase().startsWith("lan") ||
    //   // device.dport?.toLowerCase().startsWith("ge") ||
    //   device.dport?.toLowerCase().startsWith("fe") ||
    //   /^ra\d+(\/\d+)?/i.test(device.dport || "")
    //
    //
    // );
  };

  /**
   * 检查设备是否有明确的端口关系指向
   * @param device 设备信息
   * @returns 是否有明确的端口关系
   */
  const hasExplicitPortRelation = (device: any): boolean => {
    const sport = device.sport?.toLowerCase() || "";
    const dport = device.dport?.toLowerCase() || "";

    // 如果有明确的LAN(上级)<-WAN(下级)或RA->RACLI关系，则不应该被设备类型覆盖
    return (
      (/^(lan|ge|fe|port)/i.test(sport) && /^wan/i.test(dport)) ||
      (/^ra\d+(\/\d+)?/i.test(sport) && dport === "racli") ||
      (/^(lan|ge|fe|port)/i.test(dport) && /^wan/i.test(sport)) ||
      (/^ra\d+(\/\d+)?/i.test(dport) && sport === "racli")
    );
  };

  // 判断dport和sport是否处于同一级别

  const isSamePriority = (device: any): boolean => {
    // RAx <- RACLI
    if (/^ra\d+(\/\d+)?/i.test(device.extra.sport) && /^ra\d+(\/\d+)?/i.test(device.extra.dport)) return true;
    if (device.extra.dport?.toLowerCase() === "racli" && device.extra.sport?.toLowerCase() === "racli") return true;
    // LAN <- WAN
    if (/^(lan|ge|fe|port)/i.test(device.extra.sport) && /^(lan|ge|fe|port)/i.test(device.extra.dport)) return true;
    if (/^wan/i.test(device.extra.dport) && /^wan/i.test(device.extra.sport)) return true;
    return false;
  };

  /**
   * 处理普通端口设备关系
   * @param relatedDevice 关联设备
   * @param mainNode 主设备节点
   *
   */
  const handleNormalPortRelation = (relatedDevice: any, mainNode: any) => {
    relatedDevice.parentNode = mainNode;
    // 检查参数是否有效
    if (!relatedDevice || !mainNode) {
      console.error("handleNormalPortRelation: relatedDevice, mainNode or mainDevice is undefined", {
        relatedDevice,
        mainNode
      });
      return;
    }

    console.log(
      `  handleNormalPortRelation: 主设备=${mainNode.name}, 关联设备=${relatedDevice.deviceName || relatedDevice.name}`
    );

    // 确保 mainNode 有 children 属性
    if (!mainNode.children) {
      mainNode.children = [];
    }

    if (mainNode.extra.status === 0 || (mainNode.extra?.status !== 0 && relatedDevice.extra?.offline !== 0)) {
      // 主设备在线或两者都离线，将关联设备添加为主设备的子节点
      console.log(`    主设备在线或两者都离线，将关联设备添加为主设备的子节点`);
      // const relatedNodeData = createDeviceNode(relatedDevice, relatedDevice.status || 0);
      addNodeWithDedupe(relatedDevice, mainNode);
      return mainNode;
    } else {
      // 否则将主设备添加为关联设备的子节点
      console.log(`    主设备离线且关联设备在线，将关联设备添加为主设备的子节点`);

      // 创建关联设备节点
      // const relatedNodeData = createDeviceNode(relatedDevice, relatedDevice.status || 0);

      // 确保关联设备节点有 children 属性
      if (!relatedDevice.children) {
        relatedDevice.children = [];
      }

      // 将主设备添加为关联设备的子节点
      addNodeWithDedupe(mainNode, relatedDevice);

      // 返回关联设备节点，以便在调用方将其添加到拓扑图根节点
      return relatedDevice;
    }

    // 返回主设备节点，以便在调用方将其添加到拓扑图根节点
    // return mainNode;
  };

  const handleSubPortRelation = (relatedNodeData: any, mainNode: any) => {
    // mainNode.parentNode = relatedNodeData;
    // 检查参数是否有效
    if (!relatedNodeData || !mainNode) {
      console.error("handleSubPortRelation: relatedDevice or mainNode is undefined", { relatedNodeData, mainNode });
      return;
    }

    console.log(`  handleSubPortRelation: 主节点=${mainNode.name}, 关联节点=${relatedNodeData.name}`);

    // 确保 mainNode 有 children 属性
    if (!mainNode.children) {
      mainNode.children = [];
    }

    // 将关联设备添加为主设备的子节点
    mainNode.children.push(relatedNodeData);
    relatedNodeData.parentNode = mainNode;
    // addNodeWithDedupe(relatedNodeData, mainNode);

    // 返回主节点，以便在调用方将其添加到拓扑图根节点
    return mainNode;
  };

  /**
   * 处理特殊端口设备关系
   * @param relatedDevice 关联设备
   * @param mainNode 主设备节点
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleSpecialPortRelation = (relatedNode: any, mainNode: any) => {
    // mainNode.parentNode = relatedNode;
    // 检查参数是否有效
    if (!relatedNode || !mainNode) {
      console.error("handleSpecialPortRelation: relatedDevice or mainNode is undefined", { relatedNode, mainNode });
      return;
    }

    console.log(`  handleSpecialPortRelation: 主节点=${mainNode.name}, 关联节点=${relatedNode.name}`);

    // 确保关联节点有 children 属性
    if (!relatedNode.children) {
      relatedNode.children = [];
    }

    const parentNodeName = findParentNodeName(mainNode);
    console.log(`  主节点的父节点: ${parentNodeName}`);
    // 如果主节点的父节点是 "internet"，则将关联设备添加为主设备的子节点
    if (parentNodeName !== "internet" && parentNodeName !== "") {
      console.log(`    主节点的父节点是 "internet"，将关联设备添加为主设备的子节点`);
      mainNode.children.push(relatedNode);
      relatedNode.parent = mainNode;
      return mainNode;
    } else {
      // 将主设备添加为关联设备的子节点
      relatedNode.children.push(mainNode);
      if (!mainNode.extra.sport) {
        mainNode.extra.sport = relatedNode.extra.dport;
      }
      if (!mainNode.extra.dport) {
        mainNode.extra.dport = relatedNode.extra.sport;
      }
      mainNode.parentNode = relatedNode;
      // addNodeWithDedupe(mainNode, relatedNode);

      // 返回关联节点，以便在调用方将其添加到拓扑图根节点
      return relatedNode;
    }
  };

  /**
   * 切换编辑设备名称状态
   * @param row 设备数据
   */
  const toggleEditDeviceName = (row: any) => {
    console.log("toggleEditDeviceName called. row:", row);
    editName.value = !editName.value;
  };

  /**
   * 保存设备名称
   * @param row 设备数据
   * @param t 国际化函数
   */
  const handleSaveDeviceName = async (row: any, t: any) => {
    try {
      console.log("saveDeviceName called. row:", JSON.stringify(row));

      if (!drawerProps.value.row.deviceId) {
        ElMessage.error({ message: t("topology.deviceIdRequired") });
        return;
      }

      const response = await saveDeviceName(drawerProps.value.row.deviceId, row.name, row.extra.macaddr);

      console.log("API 响应数据:", response);

      if (!response || response.code !== "200") {
        ElMessage.error({ message: response.msg || t("topology.operationFailed") });
        return;
      }

      ElMessage.success({ message: t("topology.operationSuccess") });
      editName.value = false;
      deviceNameChanged.value = true;
    } catch (error) {
      console.error("保存设备名称失败:", error);
      ElMessage.error({ message: t("topology.operationFailed") });
    }
  };

  /**
   * 删除网桥客户端
   * @param row 设备数据
   * @param t 国际化函数
   */
  const handleDeleteBridgeClient = async (row: any, t: any) => {
    try {
      console.log("deleteBridgeClient called. row:", JSON.stringify(row));

      if (!drawerProps.value.row.deviceId) {
        ElMessage.error({ message: t("topology.deviceIdRequired") });
        return;
      }

      const response = await deleteBridgeClient(drawerProps.value.row.deviceId, row.extra.macaddr);

      console.log("API 响应数据:", response);

      if (!response || response.code !== "200") {
        ElMessage.error({ message: response.msg || t("topology.operationFailed") });
        return;
      }

      ElMessage.success({ message: t("topology.operationSuccess") });
      bridgeClientDrawerVisible.value = false;
    } catch (error) {
      console.error("删除网桥客户端失败:", error);
      ElMessage.error({ message: t("topology.operationFailed") });
    }
  };

  /**
   * 加载拓扑数据
   * @param deviceId 设备ID
   * @param t 国际化函数
   */
  const loadTopologyData = async (deviceId: string, t: any) => {
    try {
      if (!deviceId) {
        console.warn(t("topology.noDeviceSelected"));
        return null;
      }

      const response = await getTopologyData(deviceId);

      // if (!response || response.code !== "200") {
      //   ElMessage.error({ message: response.msg || t("topology.loadFailed") });
      //   return null;
      // }

      // return response.data?.system?.topology || [];
      return response;
    } catch (error) {
      console.error("加载拓扑数据失败:", error);
      ElMessage.error({ message: t("topology.loadFailed") });
      return null;
    }
  };

  const loadOfflineTopologyData = async (deviceId: string, t: any) => {
    if (!deviceId) {
      console.warn(t("topology.noDeviceSelected"));
      return null;
    }

    const response = await getDeviceTopology({ deviceId: deviceId });

    if (!response || response.code !== "200") {
      ElMessage.error({ message: response.msg || t("topology.loadFailed") });
      return null;
    }
  };

  /**
   * 输出节点层级结构
   * @param node 节点
   * @param level 层级
   */
  const logNodeStructure = (node: any, level: number) => {
    if (!node) return;

    // 创建缩进
    const indent = "  ".repeat(level);

    // 输出当前节点
    console.log(
      `${indent}${level === 0 ? "\u2514\u2500" : "\u251c\u2500"} ${node.name} ${node.children?.length ? `(子节点: ${node.children.length})` : "(叶节点)"}`
    );

    // 递归输出子节点
    if (node.children && node.children.length > 0) {
      node.children.forEach((child: any) => {
        logNodeStructure(child, level + 1);
      });
    }
  };

  /**
   * 检查节点是否可以设置为出口设备
   * @param node 要检查的节点
   * @returns 是否可以设置为出口设备
   */
  const canBeSetAsExitDevice = (node: any): boolean => {
    // 检查节点是否有效
    if (!node || !node.extra) return false;

    // 获取端口类型
    const sport = node.extra.sport?.toLowerCase() || "";
    const dport = node.extra.dport?.toLowerCase() || "";

    // 检查端口是否为空
    if (!sport || !dport) {
      console.log(`节点 ${node.name} 的端口信息不完整，sport=${sport}, dport=${dport}`);
      return false;
    }
    // 检查端口类型是否相同
    const sportType = getPortType(sport);
    const dportType = getPortType(dport);

    // 同类型端口可以设为出口设备
    return sportType === dportType;
  };

  /**
   * 获取端口类型，分为LAN、WAN、RA三大类
   * @param port 端口名称
   * @returns 端口类型
   */
  const getPortType = (port: string): string => {
    if (!port) return "";

    // WAN类型端口
    if (port.startsWith("wan")) {
      return "WAN";
    }

    // RA类型端口
    if (port === "racli" || /^ra\d+$/i.test(port)) {
      return "RA";
    }

    // LAN类型端口，包括lan、ge、port、fe等
    if (port.startsWith("lan") || port.startsWith("ge") || port.startsWith("port") || port.startsWith("fe")) {
      return "LAN";
    }

    // 默认为LAN类型
    return "LAN";
  };

  /**
   * 递归检查节点是否可以设为出口设备
   * 规则：
   * 1. 一级节点不能设为出口设备(拓扑图中二级节点作为实际的一级节点)
   * 2. 二级及以下节点需要满足sport和dport同类型才能设为出口设备
   * 3. 如果父节点不能设为出口设备，所有子节点都不能设为出口设备
   * @param node 要检查的节点
   * @param parentCanBeExit 父节点是否可以设为出口设备
   * @param level 节点层级
   * @returns 是否可以设为出口设备
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const checkNodeExitEligibility = (node: any, parentCanBeExit: boolean = true, level: number = 0): boolean => {
    // 一级节点(根节点)不能设为出口设备
    if (level <= 1) return false;

    // 如果父节点不能设为出口设备，子节点也不能
    if (!parentCanBeExit) return false;

    // 根据端口类型判断当前节点是否可以设为出口设备
    const currentNodeCanBeExit = canBeSetAsExitDevice(node);

    // 如果节点有子节点，递归检查
    if (node.children && node.children.length > 0) {
      for (const child of node.children) {
        // 递归检查子节点，传递当前节点的出口设备资格
        checkNodeExitEligibility(child, currentNodeCanBeExit, level + 1);
      }
    }

    return currentNodeCanBeExit;
  };

  /**
   * 加载出口设备信息
   * @param groupId 分组ID
   * @returns 出口设备列表
   */
  const loadExitDevices = async (groupId: string | number) => {
    try {
      // 清空之前的出口设备集合
      exitDeviceSet.clear();

      // 查询分组的出口设备
      const response = await getExitByGroupId(groupId);

      if (response && response.code === "200" && response.data) {
        // 将出口设备ID添加到集合中，统一用大写
        response.data.forEach(item => {
          const mac = (item.exitDeviceId || "").toUpperCase();
          if (mac) {
            exitDeviceSet.add(mac);
          }
        });

        console.log(`加载到 ${exitDeviceSet.size} 个出口设备信息`);
        return response.data;
      }
      return [];
    } catch (error) {
      console.error("加载出口设备信息失败:", error);
      return [];
    }
  };

  /**
   * 设置出口设备
   * @param groupId 分组ID
   * @param deviceInfo 设备信息
   * @param t 国际化函数
   * @returns 是否设置成功
   */
  const handleSetAsExitDevice = async (groupId: string | number, deviceInfo: any, t: any): Promise<boolean> => {
    try {
      // 统一使用mac或macaddr作为出口设备ID（大写）
      const exitDeviceId = (
        deviceInfo.extra?.macaddr ||
        deviceInfo.extra?.mac ||
        deviceInfo.macaddr ||
        deviceInfo.mac ||
        ""
      ).toUpperCase();

      if (!exitDeviceId) {
        ElMessage.error(t("topology.deviceIdRequired"));
        return false;
      }

      // 检查设备是否可以设为出口设备
      if (!canBeSetAsExitDevice(deviceInfo)) {
        ElMessage.error(t("device.cannotSetAsExitDevice"));
        return false;
      }

      // 准备请求参数
      const params = [
        {
          groupId,
          exitDeviceId
        }
      ];

      // 调用API设置出口设备
      const response = await saveGroupExit(params);

      if (response && response.code === "200") {
        // 更新出口设备集合（大写）
        exitDeviceSet.add(exitDeviceId);

        // 返回成功消息，让调用方刷新拓扑图
        ElMessage.success(t("device.setAsExitDeviceSuccess"));
        return true;
      } else {
        ElMessage.error(response?.msg || t("topology.operationFailed"));
        return false;
      }
    } catch (error) {
      console.error("设置出口设备失败:", error);
      ElMessage.error(t("topology.operationFailed"));
      return false;
    }
  };

  /**
   * 取消出口设备
   * @param deviceInfo 设备信息
   * @param t 国际化函数
   * @returns 是否取消成功
   */
  const handleCancelExitDevice = async (deviceInfo: any, t: any): Promise<boolean> => {
    try {
      // 统一使用mac或macaddr作为出口设备ID（大写）
      const exitDeviceId = (
        deviceInfo.extra?.macaddr ||
        deviceInfo.extra?.mac ||
        deviceInfo.macaddr ||
        deviceInfo.mac ||
        ""
      ).toUpperCase();

      if (!exitDeviceId) {
        ElMessage.error(t("topology.deviceIdRequired"));
        return false;
      }

      // 调用API删除出口设备
      const response = await deleteExitByDeviceId({ exitDeviceId });

      if (response && response.code === "200") {
        // 从出口设备集合中移除（大写）
        exitDeviceSet.delete(exitDeviceId);

        // 返回成功消息，让调用方刷新拓扑图
        ElMessage.success(t("device.cancelExitDeviceSuccess"));
        return true;
      } else {
        ElMessage.error(response?.msg || t("topology.operationFailed"));
        return false;
      }
    } catch (error) {
      console.error("取消出口设备失败:", error);
      ElMessage.error(t("topology.operationFailed"));
      return false;
    }
  };

  /**
   * 处理出口设备，重组拓扑结构
   * @param exitDevices 出口设备列表
   */
  const processExitDevices = (exitDevices: any[]) => {
    if (!exitDevices || !exitDevices.length) return;

    // 首先清除所有节点的出口设备标记
    const clearExitDeviceFlags = (node: any) => {
      if (!node) return;

      // 清除当前节点的出口设备标记
      if (node.isExitDevice) {
        node.isExitDevice = false;
      }

      // 递归清除子节点的出口设备标记
      if (node.children && node.children.length > 0) {
        node.children.forEach((child: any) => clearExitDeviceFlags(child));
      }
    };

    // 清除所有节点的出口设备标记
    clearExitDeviceFlags(topologyData);

    // 遍历所有出口设备
    exitDevices.forEach(exitDevice => {
      // 统一用大写
      const exitDeviceId = (exitDevice.exitDeviceId || "").toUpperCase();
      if (!exitDeviceId) return;

      // 在拓扑图中查找出口设备节点
      let foundExitNode = null;

      // 递归查找函数
      const findExitNode = (node: any): any => {
        if (!node) return null;

        // 检查当前节点是否是出口设备，忽略大小写
        const nodeMac = (node.extra?.macaddr || node.extra?.mac || "").toUpperCase();
        const nodeId = (node.extra?.deviceId || "").toUpperCase();

        // 使用MAC地址或设备ID进行匹配
        if (nodeMac === exitDeviceId || nodeId === exitDeviceId) {
          console.log(`找到出口设备节点:`, {
            name: node.name,
            mac: nodeMac,
            deviceId: nodeId,
            match: nodeMac === exitDeviceId ? "MAC匹配" : "ID匹配"
          });
          return node;
        }

        // 递归查找子节点
        if (node.children && node.children.length > 0) {
          for (const child of node.children) {
            const result = findExitNode(child);
            if (result) return result;
          }
        }

        return null;
      };

      // 从拓扑图中查找出口设备
      foundExitNode = findExitNode(topologyData);

      if (foundExitNode) {
        // 标记该节点为出口设备
        foundExitNode.isExitDevice = true;
        console.log(`标记节点 ${foundExitNode.name} 为出口设备`);

        // 获取出口设备在拓扑图中的位置信息
        const nodeInfo = findNodeInTopology(foundExitNode);

        // 如果找到了出口设备节点的信息
        if (nodeInfo && nodeInfo.parentNode) {
          const parentNode = nodeInfo.parentNode;

          // 输出节点信息进行调试
          console.log(`出口设备节点信息:`, {
            name: foundExitNode.name,
            isDirectChild: nodeInfo.isDirectChild,
            level: nodeInfo.level,
            parentNode: parentNode.name,
            path: nodeInfo.path.map(n => n.name).join(" -> ")
          });

          // 如果出口设备不是一级节点（即parentNode不是internet），则需要进行路径反转
          if (parentNode.name !== "internet") {
            console.log(`出口设备 ${foundExitNode.name} 不是一级节点，开始路径反转`);

            // 实现路径反转逻辑
            performPathReversal(foundExitNode, nodeInfo);
          } else {
            console.log(`出口设备 ${foundExitNode.name} 已经是顶级节点，无需提升`);
          }
        }
      }
    });

    // 输出处理后的拓扑图结构
    console.log("\n处理后的拓扑图结构:");
    logNodeStructure(topologyData, 0);
  };

  /**
   * 合并拓扑树中所有 MAC 重复的节点，只保留信息最全的唯一节点
   * @param topologyDataList
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const mergeDuplicateMacNodes = topologyDataList => {
    type Node = any;
    const macMap = new Map<string, { node: Node; parent: Node | null }>();
    const duplicates: Array<{ main: Node; dup: Node; dupParent: Node | null }> = [];

    // 收集所有节点，记录重复
    const collect = (subTopology, subTopologyChildrens) => {
      if (!subTopologyChildrens || subTopologyChildrens.length === 0) return; // 防御性处理，防止为null
      // for (const node of subTopology.children) {
      // subTopology.extra;
      for (const nodeData of subTopologyChildrens) {
        let mac: any;
        mac = nodeData.mac;
        // 需要清空children，但是不能影响到原始数据
        const node = nodeData;
        // delete node.children;
        if (mac) {
          const key = mac.toLowerCase();
          if (macMap.has(key)) {
            console.log(`发现重复的MAC: ${mac}`);
            duplicates.push({ main: macMap.get(key)!.node, dup: node, dupParent: subTopology });
          } else {
            macMap.set(key, { node, parent: null });
          }
        }
        if (nodeData.children && Array.isArray(nodeData.children) && nodeData.children.length > 0) {
          collect(nodeData, nodeData.children);
        }
      }
    };

    /**
     * 合并节点信息和子节点
     * @param main 保留的节点
     * @param dup 被合并的节点
     */
    const mergeNode = (main: Node, dup: Node, dupParent: Node | null) => {
      // 名称补全
      if ((!main.name || main.name === t("topology.unknown") || main.name === "未知") && dup.name) main.name = dup.name;
      if (main.name && !main.extra.deviceName && dup.name) main.name = dup.name;
      // deviceId设备ID补全
      if (!main.deviceId && dup.deviceId) main.deviceId = dup.deviceId;
      // extra 字段补全
      if (main.extra && dup.extra) {
        ["deviceName", "deviceId", "deviceModel"].forEach(key => {
          if (!main.extra[key] && dup.extra[key]) main.extra[key] = dup.extra[key];
        });
        // 合并 system.topology.peer/subDev
        // const mTopo = main.extra.system?.topology,
        //   dTopo = dup.extra.system?.topology;
        // if (dTopo) {
        //   if (dTopo.peer) {
        //     mTopo.peer = [
        //       ...(mTopo?.peer || []),
        //       ...dTopo.peer.filter(
        //         (p: any) =>
        //           !(mTopo?.peer || []).some(
        //             (ep: any) => (ep.macaddr || ep.mac)?.toLowerCase() === (p.macaddr || p.mac)?.toLowerCase()
        //           )
        //       )
        //     ];
        //   }
        //   if (dTopo.subDev) {
        //     mTopo.subDev = [
        //       ...(mTopo?.subDev || []),
        //       ...dTopo.subDev.filter(
        //         (s: any) =>
        //           !(mTopo?.subDev || []).some(
        //             (es: any) => (es.macaddr || es.mac)?.toLowerCase() === (s.macaddr || s.mac)?.toLowerCase()
        //           )
        //       )
        //     ];
        //   }
        // }
      }
      // 添加防御性检查，确保所有对象和属性存在
      if (dup.parentNode == null) {
        // dup.parentNode = main.parentNode;
        if (dupParent && Array.isArray(dupParent.children)) {
          dupParent.children = dupParent.children.filter(c => c !== dup);
        }
      } else {
        // let dupParent = dup.parentNode;
        // 先从原节点的子节点中移除
        if (main.parentNode == null) {
          main.parentNode = dupParent;
          if (dupParent && Array.isArray(dupParent.children)) {
            dupParent.children.push(main);
            if (dupParent.children.children) {
              dedupeNodes(dupParent.children.children);
            }
          }
        } else {
          // 避免造成循环引用
          if (main.parentNode && dup.parentNode && main.parentNode.mac !== dup.parentNode.mac) {
            if (main.parentNode && Array.isArray(main.parentNode.children)) {
              main.parentNode.children.push(dupParent);
              dedupeNodes(main.parentNode.children);
              dupParent.parentNode = main.parentNode;
            }
          }
        }

        // 合并子节点
        if (main.children && dup.children) {
          main.children.push(...dup.children);
        } else if (!main.children && dup.children) {
          main.children = [...dup.children];
        }

        // 从父节点中移除重复节点
        if (dupParent && Array.isArray(dupParent.children)) {
          const idx = dupParent.children.findIndex((c: Node) => isSameNode(c, dup));
          if (idx !== -1) dupParent.children.splice(idx, 1);
        }
        // 如果原节点的父节点还有父节点，则原节点的父节点的父节点添加到原节点的父节点的子节点中（如果重复节点是三级节点,先将一级节点和二级节点关系反转）
        // const parent = dupParent.parentNode;
        // if (parent) {
        //   dupParent.parentNode = null;
        //   dupParent.children = dupParent.children.filter(c => c !== dup);
        //   dupParent.children.push(parent);
        //   parent.parentNode = dupParent;
        // }
        // 将原节点的父节点添加到原节点的子节点中(关系反转)
        // dup.parentNode = main.parentNode;
        // dupParent.children.filter(c => c !== dup);
        // dup.children.push(dupParent);
      }
      // 合并子节点
      // if (dup.children?.length) {
      //   if (!main.children) main.children = [];
      //   dup.children.forEach((child: Node) => {
      //     const idx = main.children.findIndex((c: Node) => isSameNode(c, child));
      //     if (idx !== -1) mergeNode(main.children[idx], child);
      //     else main.children.push(child);
      //   });
      // }
    };

    // 从父节点移除重复节点
    // const removeFromParent = (parent: Node | null, node: Node) => {
    //   if (!parent?.children) return;
    //   const idx = parent.children.indexOf(node);
    //   if (idx !== -1) parent.children.splice(idx, 1);
    // };

    topologyDataList.forEach(subTopology => {
      // 添加防御性检查，确保subTopology存在且有children属性
      if (subTopology && Array.isArray(subTopology?.children) && subTopology.children.length > 0) {
        collect(subTopology, subTopology.children);
      }
    });
    duplicates.forEach(({ main, dup, dupParent }) => {
      // if (isNodeAPriorityHigher(main, dup) >= 0) {
      mergeNode(main, dup, dupParent);
      // removeFromParent(dupParent, dup);
      // } else {
      //   mergeNode(dup, main, dupParent);
      // removeFromParent(main.parent, main);
      // }
    });
    console.log(`处理完成，合并了 ${duplicates.length} 个重复MAC地址的节点`);
  };

  /**
   * 清空所有设备MAC地址集合
   */
  const clearAllDeviceMacAddresses = () => {
    allDeviceMacAddresses.clear();
    deviceNodesByMac.clear();
    peerMacAddresses.clear();
    parentMap.clear();
    deviceNodesByDeviceId.clear();
    deviceNodesByMacAddress.clear();
  };

  /**
   * 执行路径反转操作 - 将出口设备提升为一级节点，严格路径反转
   * @param exitNode 出口设备节点
   * @param nodeInfo 节点信息
   */
  const performPathReversal = (exitNode: any, nodeInfo: any) => {
    console.log(`开始执行出口设备路径反转操作: ${exitNode.name}`);

    // 只处理非一级节点
    if (nodeInfo.level <= 1) {
      console.log(`节点 ${exitNode.name} 已经是一级节点，无需反转`);
      return;
    }

    // 获取路径 [internet, A, B, D]
    const path = nodeInfo.path;
    if (path.length < 3) return; // 已经是一级节点，无需提升

    // 记录路径上每个节点原有的children（去掉路径下一个节点）
    const oldChildren = path.map((node, idx) => {
      const next = path[idx + 1];
      return node.children ? node.children.filter(child => child !== next) : [];
    });

    // 反转父子关系
    // 从出口设备（最后一个）往前，依次把前一个节点作为当前节点的唯一子节点
    for (let i = path.length - 1; i > 1; i--) {
      path[i].children = [path[i - 1], ...oldChildren[i]];
    }
    // path[1]（原一级节点）children 只保留原有非路径子节点
    path[1].children = oldChildren[1];

    // internet.children 用出口设备替换原一级节点
    const idx = path[0].children.findIndex(child => child === path[1]);
    if (idx !== -1) {
      path[0].children[idx] = path[path.length - 1];
    }
  };

  /**
   * 判断节点A是否比节点B优先级高
   * @param nodeA
   * @param nodeB
   * @returns true: nodeA优先级高，1: nodeB优先级高 -1 : 等级相同 0
   */
  function isNodeAPriorityHigher(nodeA: any, nodeB: any): number {
    // 端口优先级
    const portPriority = (sport: string, dport: string) => {
      // RAx <- RACLI
      if (/^ra\d+(\/\d+)?/i.test(sport) && dport?.toLowerCase() === "racli") return 2;
      if (/^ra\d+(\/\d+)?/i.test(dport) && sport?.toLowerCase() === "racli") return 2;
      // LAN(上级) <- WAN(下级)
      if (/^(lan|ge|fe|port)/i.test(sport) && /^wan/i.test(dport)) return 1;
      if (/^(lan|ge|fe|port)/i.test(dport) && /^wan/i.test(sport)) return 1;
      return 0;
    };

    // 设备类型优先级
    // const typeOrder = ["route", "ac", "switch", "reaper", "ap", "bridge"];
    // const getTypePriority = (type: string) => {
    //   const idx = typeOrder.indexOf((type || "").toLowerCase());
    //   return idx === -1 ? 99 : idx;
    // };

    // 1. 端口优先级
    const aSport = nodeA.extra?.sport || "";
    const aDport = nodeA.extra?.dport || "";
    const bSport = nodeB.extra?.sport || "";
    const bDport = nodeB.extra?.dport || "";

    // 先判断 RAx <- RACLI
    let resA = portPriority(aSport, aDport);
    let resB = portPriority(bSport, bDport);

    // // 2. 设备类型优先级
    // const aType = getTypePriority(nodeA.extra?.deviceType);
    // const bType = getTypePriority(nodeB.extra?.deviceType);
    // if (aType !== bType) return aType < bType;

    // 默认nodeA优先级不高
    if (resA === resB) return 0;
    if (resA > resB) return 1;
    if (resA < resB) return -1;
  }

  /**
   * 通用树节点删除工具：可删除任意节点（包括根节点本身）
   * @param root 树的根节点
   * @param nodeToRemove 要删除的节点
   * @returns { newTree, removed } newTree为删除后的树，removed为是否删除成功
   */
  // function removeNodeInTree(root: any, nodeToRemove: any): { newTree: any; removed: boolean } {
  //   if (!root) return { newTree: null, removed: false };
  //   if (root === nodeToRemove) {
  //     // 根节点就是要删除的节点
  //     return { newTree: null, removed: true };
  //   }
  //   if (root.children && Array.isArray(root.children)) {
  //     const idx = root.children.findIndex(child => child === nodeToRemove);
  //     if (idx !== -1) {
  //       root.children.splice(idx, 1);
  //       return { newTree: root, removed: true };
  //     }
  //     // 递归对子节点
  //     for (let i = 0; i < root.children.length; i++) {
  //       const result = removeNodeInTree(root.children[i], nodeToRemove);
  //       if (result.removed) {
  //         // 如果子树被删空，则从children中移除
  //         if (!result.newTree) {
  //           root.children.splice(i, 1);
  //         }
  //         return { newTree: root, removed: true };
  //       }
  //     }
  //   }
  //   return { newTree: root, removed: false };
  // }

  return {
    t,
    topologyData,
    drawerVisible,
    drawerProps,
    clickNodeProps,
    editName,
    deviceNameChanged,
    bridgeClientDrawerVisible,
    generateData,
    toggleEditDeviceName,
    handleSaveDeviceName,
    handleDeleteBridgeClient,
    loadTopologyData,
    loadOfflineTopologyData,
    clearAllDeviceMacAddresses,
    loadExitDevices,
    handleSetAsExitDevice,
    handleCancelExitDevice,
    processExitDevices
  };
}
