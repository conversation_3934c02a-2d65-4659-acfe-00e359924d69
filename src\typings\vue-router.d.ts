declare module "vue-router" {
  import type { App, Ref, Component } from "vue";

  // 基本类型定义
  export interface Router {
    currentRoute: Ref<RouteLocationNormalizedLoaded>;
    push(to: RouteLocationRaw): Promise<NavigationFailure | void | undefined>;
    replace(to: RouteLocationRaw): Promise<NavigationFailure | void | undefined>;
    go(delta: number): void;
    back(): void;
    forward(): void;
    beforeEach(guard: NavigationGuard): () => void;
    beforeResolve(guard: NavigationGuard): () => void;
    afterEach(guard: NavigationHookAfter): () => void;
    install(app: App): void;
    addRoute(route: RouteRecordRaw): () => void;
    addRoute(parentName: string | symbol, route: RouteRecordRaw): () => void;
    removeRoute(name: string | symbol): void;
    hasRoute(name: string | symbol): boolean;
    getRoutes(): RouteRecord[];
    resolve(to: RouteLocationRaw): RouteLocationResolved;
    onError(handler: (error: any) => void): () => void;
  }

  export interface RouteLocationNormalizedLoaded {
    fullPath: string;
    path: string;
    query: Record<string, string | string[]>;
    hash: string;
    name: string | symbol | null | undefined;
    params: Record<string, string | string[]>;
    matched: RouteRecord[];
    meta: Record<string | number | symbol, unknown>;
  }

  export interface RouteLocationNormalized extends RouteLocationNormalizedLoaded {}

  export interface RouteRecordRaw {
    path: string;
    name?: string | symbol;
    component?: Component;
    components?: Record<string, Component>;
    redirect?: RouteLocationRaw;
    children?: RouteRecordRaw[];
    meta?: Record<string | number | symbol, unknown>;
    beforeEnter?: NavigationGuard | NavigationGuard[];
    props?: boolean | Record<string, any> | ((to: RouteLocationNormalized) => Record<string, any>);
    alias?: string | string[];
  }

  export interface RouteRecord extends RouteRecordRaw {
    aliasOf?: RouteRecord;
  }

  export interface RouterHistory {
    readonly base: string;
    readonly location: string;
    readonly state: Record<string, any>;
    push(to: string, data?: Record<string, any>): void;
    replace(to: string, data?: Record<string, any>): void;
    go(delta: number, triggerListeners?: boolean): void;
    listen(callback: Function): () => void;
    createHref(location: string): string;
    destroy(): void;
  }

  export interface RouterOptions {
    history: RouterHistory;
    routes: RouteRecordRaw[];
    linkActiveClass?: string;
    linkExactActiveClass?: string;
    parseQuery?: (query: string) => Record<string, string | string[]>;
    stringifyQuery?: (query: Record<string, any>) => string;
    scrollBehavior?: Function;
    strict?: boolean;
    sensitive?: boolean;
    end?: boolean;
  }

  export type RouteLocationRaw =
    | string
    | {
        name?: string | symbol;
        path?: string;
        params?: Record<string, any>;
        query?: Record<string, any>;
        hash?: string;
        replace?: boolean;
        force?: boolean;
      };

  export interface RouteLocationResolved extends RouteLocationNormalizedLoaded {
    href: string;
  }

  export interface NavigationFailure extends Error {
    type: number;
    from: RouteLocationNormalized;
    to: RouteLocationNormalized;
  }

  export interface NavigationGuard {
    (to: RouteLocationNormalized, from: RouteLocationNormalizedLoaded, next: Function): any;
  }

  export interface NavigationHookAfter {
    (to: RouteLocationNormalized, from: RouteLocationNormalizedLoaded, failure?: NavigationFailure): any;
  }

  // 导出路由创建函数
  export function createRouter(options: RouterOptions): Router;
  export function createWebHistory(base?: string): RouterHistory;
  export function createWebHashHistory(base?: string): RouterHistory;
  export function createMemoryHistory(base?: string): RouterHistory;

  // 导出组合式API
  export function useRouter(): Router;
  export function useRoute(): RouteLocationNormalizedLoaded;

  // 导出其他常用函数
  export function onBeforeRouteUpdate(updateGuard: NavigationGuard): void;
  export function onBeforeRouteLeave(leaveGuard: NavigationGuard): void;

  // 导出组件
  export const RouterView: any;
  export const RouterLink: any;
}
