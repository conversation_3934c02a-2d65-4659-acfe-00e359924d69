import { computed } from "vue";
import { useGlobalStore } from "@/stores/modules/global";

/**
 * 格式化运行时间
 * @param bootTimeInSeconds 运行时间（秒）
 * @returns 格式化后的时间字符串
 */
export const formatBootTime = (bootTimeInSeconds: number): string => {
  const years = Math.floor(bootTimeInSeconds / (365 * 24 * 60 * 60));
  const months = Math.floor((bootTimeInSeconds % (365 * 24 * 60 * 60)) / (30 * 24 * 60 * 60));
  const days = Math.floor((bootTimeInSeconds % (30 * 24 * 60 * 60)) / (24 * 60 * 60));
  const hours = Math.floor((bootTimeInSeconds % (24 * 60 * 60)) / 3600);
  const minutes = Math.floor((bootTimeInSeconds % 3600) / 60);
  const seconds = bootTimeInSeconds % 60;

  // 获取浏览器语言
  const globalStore = useGlobalStore();
  const isChinese = computed(() => globalStore.language === "zh"); // 使用全局语言

  // 以数组的形式存储各部分
  const timeParts: string[] = [];

  if (years > 0) {
    timeParts.push(`${years}${isChinese.value ? "年" : "yr"}`);
  }
  if (months > 0) {
    timeParts.push(`${months}${isChinese.value ? "月" : "mo"} `);
  }
  if (days > 0) {
    timeParts.push(`${days}${isChinese.value ? "天" : "d"} `);
  }
  if (hours > 0) {
    timeParts.push(`${hours}${isChinese.value ? "时" : "h"} `);
  }
  if (minutes > 0) {
    timeParts.push(`${minutes}${isChinese.value ? "分" : "min"} `);
  }
  if (seconds > 0 || timeParts.length === 0) {
    timeParts.push(`${seconds}${isChinese.value ? "秒" : "s"} `);
  }

  // 拼接时间部分，使用空格分隔
  return timeParts.join("");
};

/**
 * 将图片URL转换为Base64
 * @param url 图片URL
 * @returns Base64编码的图片
 */
export const loadImageAsBase64 = async (url: string): Promise<string> => {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
    }

    const blob = await response.blob();
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    console.error("Error loading image as base64:", error);
    return ""; // 返回空字符串作为默认值
  }
};
