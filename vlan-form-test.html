<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VLAN表单验证测试</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css" />
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        .title {
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 20px;
            text-align: center;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e4e7ed;
            border-radius: 6px;
        }
        .section-title {
            font-size: 18px;
            font-weight: 500;
            color: #409eff;
            margin-bottom: 15px;
        }
        .form-container {
            background: #fafafa;
            padding: 20px;
            border-radius: 6px;
        }
        .button-group {
            margin-top: 20px;
            text-align: center;
        }
        .status-info {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            font-size: 14px;
        }
        .status-success {
            background-color: #f0f9ff;
            border: 1px solid #67c23a;
            color: #67c23a;
        }
        .status-error {
            background-color: #fef0f0;
            border: 1px solid #f56c6c;
            color: #f56c6c;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <h1 class="title">VLAN表单验证修复测试</h1>
            
            <div class="test-section">
                <h2 class="section-title">问题描述</h2>
                <p>弹出框中VLAN修改后无法提交，控制台显示"表单验证失败"。</p>
                <p><strong>根本原因：</strong></p>
                <ul>
                    <li>VLAN表单使用了 <code>ref="vlanForm"</code>，但JavaScript中没有定义对应的ref变量</li>
                    <li>handleSubmit函数中没有正确处理第三个标签页（VLAN设置）的表单引用</li>
                    <li>VLAN表单缺少验证规则</li>
                </ul>
            </div>

            <div class="test-section">
                <h2 class="section-title">修复方案</h2>
                <ol>
                    <li>添加 <code>const vlanForm = ref&lt;FormInstance&gt;();</code> 定义</li>
                    <li>修改handleSubmit函数，正确处理第三个标签页的表单引用</li>
                    <li>为VLAN表单添加验证规则</li>
                    <li>为表单字段添加prop属性</li>
                </ol>
            </div>

            <div class="test-section">
                <h2 class="section-title">VLAN表单模拟测试</h2>
                <div class="form-container">
                    <el-form ref="vlanFormRef" :model="vlanForm" :rules="vlanRules" label-width="120px">
                        <el-form-item label="端口VLAN模式" prop="vlanmode">
                            <el-select v-model="vlanForm.vlanmode" placeholder="请选择">
                                <el-option label="Access" :value="0"></el-option>
                                <el-option label="Trunk" :value="1"></el-option>
                                <el-option label="Hybrid" :value="2"></el-option>
                            </el-select>
                        </el-form-item>
                        
                        <el-form-item label="VLAN ID" prop="pvid">
                            <el-input-number v-model="vlanForm.pvid" :min="1" :max="4094" placeholder="请输入VLAN ID"></el-input-number>
                        </el-form-item>
                        
                        <el-form-item label="Permit VLAN" prop="permit" v-if="vlanForm.vlanmode === 1 || vlanForm.vlanmode === 2">
                            <el-input v-model="vlanForm.permitString" placeholder="请输入VLAN ID，多个用逗号分隔"></el-input>
                        </el-form-item>
                        
                        <el-form-item label="Untag VLAN" prop="untag" v-if="vlanForm.vlanmode === 2">
                            <el-input v-model="vlanForm.untagString" placeholder="请输入VLAN ID，多个用逗号分隔"></el-input>
                        </el-form-item>
                    </el-form>
                    
                    <div class="button-group">
                        <el-button type="primary" @click="submitForm">提交</el-button>
                        <el-button @click="resetForm">重置</el-button>
                        <el-button type="info" @click="testValidation">测试验证</el-button>
                    </div>
                    
                    <div v-if="validationStatus" :class="['status-info', validationStatus.type === 'success' ? 'status-success' : 'status-error']">
                        {{ validationStatus.message }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, computed } = Vue;
        const { ElMessage } = ElementPlus;

        createApp({
            setup() {
                const vlanFormRef = ref();
                const validationStatus = ref(null);
                
                const vlanForm = ref({
                    vlanmode: null,
                    pvid: null,
                    permitString: '',
                    untagString: ''
                });

                const vlanRules = computed(() => ({
                    vlanmode: [
                        {
                            required: true,
                            message: '请选择端口VLAN模式',
                            trigger: 'change'
                        }
                    ],
                    pvid: [
                        {
                            required: true,
                            message: '请输入VLAN ID',
                            trigger: 'blur'
                        },
                        {
                            validator: (rule, value, callback) => {
                                if (value !== null && value !== undefined) {
                                    const num = Number(value);
                                    if (isNaN(num) || !Number.isInteger(num) || num < 1 || num > 4094) {
                                        callback(new Error('VLAN ID必须是1-4094之间的整数'));
                                    } else {
                                        callback();
                                    }
                                } else {
                                    callback();
                                }
                            },
                            trigger: 'blur'
                        }
                    ],
                    permit: [
                        {
                            validator: (rule, value, callback) => {
                                if (vlanForm.value.vlanmode === 1 || vlanForm.value.vlanmode === 2) {
                                    if (!vlanForm.value.permitString || vlanForm.value.permitString.trim() === '') {
                                        callback(new Error('请输入Permit VLAN'));
                                    } else {
                                        const vlanIds = vlanForm.value.permitString.split(',').map(id => id.trim());
                                        for (const id of vlanIds) {
                                            const num = parseInt(id, 10);
                                            if (isNaN(num) || num < 1 || num > 4094) {
                                                callback(new Error('Permit VLAN ID必须是1-4094之间的整数，多个ID用逗号分隔'));
                                                return;
                                            }
                                        }
                                        callback();
                                    }
                                } else {
                                    callback();
                                }
                            },
                            trigger: 'blur'
                        }
                    ],
                    untag: [
                        {
                            validator: (rule, value, callback) => {
                                if (vlanForm.value.vlanmode === 2) {
                                    if (vlanForm.value.untagString && vlanForm.value.untagString.trim() !== '') {
                                        const vlanIds = vlanForm.value.untagString.split(',').map(id => id.trim());
                                        for (const id of vlanIds) {
                                            const num = parseInt(id, 10);
                                            if (isNaN(num) || num < 1 || num > 4094) {
                                                callback(new Error('Untag VLAN ID必须是1-4094之间的整数，多个ID用逗号分隔'));
                                                return;
                                            }
                                        }
                                    }
                                    callback();
                                } else {
                                    callback();
                                }
                            },
                            trigger: 'blur'
                        }
                    ]
                }));

                const submitForm = async () => {
                    if (!vlanFormRef.value) return;
                    
                    try {
                        await vlanFormRef.value.validate();
                        validationStatus.value = {
                            type: 'success',
                            message: '✅ 表单验证通过！可以正常提交了。'
                        };
                        ElMessage.success('表单验证通过！');
                    } catch (error) {
                        validationStatus.value = {
                            type: 'error',
                            message: '❌ 表单验证失败，请检查输入内容。'
                        };
                        ElMessage.error('表单验证失败');
                    }
                };

                const resetForm = () => {
                    if (vlanFormRef.value) {
                        vlanFormRef.value.resetFields();
                    }
                    validationStatus.value = null;
                };

                const testValidation = () => {
                    // 设置一些测试数据
                    vlanForm.value = {
                        vlanmode: 1, // Trunk模式
                        pvid: 100,
                        permitString: '1,2,3,100',
                        untagString: ''
                    };
                    validationStatus.value = {
                        type: 'success',
                        message: '🧪 已填入测试数据，可以点击提交测试验证功能。'
                    };
                };

                return {
                    vlanFormRef,
                    vlanForm,
                    vlanRules,
                    validationStatus,
                    submitForm,
                    resetForm,
                    testValidation
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
