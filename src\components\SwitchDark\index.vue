<template>
  <el-tooltip :content="globalStore.isDark ? t('header.toggleLightMode') : t('header.toggleDarkMode')" placement="bottom">
    <el-switch v-model="globalStore.isDark" inline-prompt :active-icon="Sunny" :inactive-icon="Moon" @change="switchDark" />
  </el-tooltip>
</template>

<script setup lang="ts" name="SwitchDark">
import { useTheme } from "@/hooks/useTheme";
import { useGlobalStore } from "@/stores/modules/global";
import { Sunny, Moon } from "@element-plus/icons-vue";
import { useI18n } from "vue-i18n";

const { switchDark } = useTheme();
const globalStore = useGlobalStore();
const { t } = useI18n();
</script>
