# DeviceConfigDrawer 修复验证测试

## 测试步骤

### 1. WAN组件显示测试
**测试目标**: 验证WAN设置组件是否正常显示

**测试步骤**:
1. 打开设备配置抽屉
2. 切换到"网络设置"标签页
3. 检查是否显示"WAN设置"折叠面板
4. 展开WAN设置面板
5. 检查以下字段是否正常显示：
   - 连接类型下拉框
   - 静态IP配置（当选择静态IP时）
   - PPPoE配置（当选择PPPoE时）
   - MTU设置
   - DNS设置

**预期结果**: 
- WAN设置面板正常显示
- 所有字段正确绑定数据
- 条件显示逻辑正常工作

### 2. 国际化翻译测试
**测试目标**: 验证缺失的翻译是否已添加

**测试步骤**:
1. 检查WiFi设置中的加密方式开关
2. 检查"清空密码"按钮
3. 检查WAN设置中的各个字段标签
4. 检查DHCP、LAN、DNS设置中的字段标签

**预期结果**: 
- 不再出现 `[intlify] Not found 'device.wpa2' key` 等错误
- 所有字段显示正确的中文标签
- 加密方式开关显示"WPA2加密"和"不加密"

### 3. 数据绑定测试
**测试目标**: 验证数据访问路径是否正确

**测试步骤**:
1. 在WAN设置中修改连接类型
2. 在LAN设置中修改IP地址
3. 在DHCP设置中开启/关闭DHCP服务器
4. 在DNS设置中修改DNS服务器地址
5. 检查数据是否正确保存到 `deviceConfig.network` 对象中

**预期结果**: 
- 数据修改后正确保存到对应的数据结构中
- 表单验证正常工作
- 数据提交时包含正确的字段

### 4. 组件显示条件测试
**测试目标**: 验证组件显示条件是否正确

**测试设备支持配置**:
```javascript
const deviceSupports = {
  wireless: {
    supports: ["radio0", "radio1", "guest", "wifiTime"]
  },
  network: {
    supports: ["wan", "lan", "dhcp", "dns", "brAp"]
  },
  system: {
    supports: ["led", "reboot"]
  }
}
```

**测试步骤**:
1. 使用支持WAN的设备配置
2. 检查WAN设置是否显示
3. 使用不支持WAN的设备配置
4. 检查WAN设置是否隐藏
5. 对LAN、DHCP、DNS、桥接WiFi重复相同测试

**预期结果**: 
- 组件根据设备支持情况正确显示/隐藏
- 不支持的功能不会显示对应的设置面板

### 5. 事件处理测试
**测试目标**: 验证事件处理是否正确

**测试步骤**:
1. 修改WAN网关地址
2. 检查是否调用 `handleWanUpdate('gawa', value)`
3. 修改LAN IP地址
4. 检查是否调用 `handleLanUpdate('ipaddr', value)`
5. 修改DHCP设置
6. 检查是否调用 `handleDhcpUpdate(field, value)`

**预期结果**: 
- 事件正确触发
- 数据正确更新到对应的数据结构中
- 网关字段使用正确的字段名 `gawa`

## 常见问题排查

### 问题1: WAN设置不显示
**可能原因**: 
- 设备支持配置中缺少 `wan` 支持
- 显示条件检查错误

**排查步骤**:
1. 检查 `drawerProps.value.row.supports.network.supports` 是否包含 `"wan"`
2. 检查 `deviceConfig.network.wan` 是否存在且为数组

### 问题2: 国际化翻译缺失
**可能原因**: 
- 翻译文件未正确加载
- 翻译键名错误

**排查步骤**:
1. 检查浏览器控制台是否有 `[intlify] Not found` 错误
2. 检查 `src/languages/modules/zh.ts` 文件中是否包含对应翻译
3. 检查翻译键名是否正确

### 问题3: 数据绑定错误
**可能原因**: 
- 数据访问路径错误
- 数据结构不匹配

**排查步骤**:
1. 检查 `deviceConfig.network` 对象结构
2. 检查 WAN 数据是否为数组格式
3. 检查事件处理函数是否正确更新数据

### 问题4: 组件不显示
**可能原因**: 
- 显示条件检查错误
- 数据结构不匹配

**排查步骤**:
1. 检查 `v-if` 条件是否正确
2. 检查设备支持配置是否正确
3. 检查数据是否已正确加载

## 测试数据示例

### 设备支持配置示例
```javascript
const deviceSupports = {
  wireless: {
    supports: ["radio0", "radio1", "guest", "wifiTime"]
  },
  network: {
    supports: ["wan", "lan", "dhcp", "dns", "brAp", "mtu", "stp", "igmp_snooping"]
  },
  system: {
    supports: ["led", "reboot", "swPort"]
  }
}
```

### 设备配置数据示例
```javascript
const deviceConfig = {
  network: {
    wan: [{
      proto: "dhcp",
      ipaddr: "",
      netmask: "",
      gawa: "",
      username: "",
      password: "",
      dns1: "",
      dns2: ""
    }],
    lan: {
      ipaddr: "***********",
      netmask: "*************",
      gateway: "",
      mtu: 1500,
      stp: 0,
      igmp_snooping: 0
    },
    dhcp: {
      enabled: 1,
      start: "100",
      end: "200",
      leasetime: "24h",
      gateway: "***********",
      netmask: "*************",
      dns1: "*******",
      dns2: "*******"
    },
    dns: {
      enabled: 1,
      server1: "*******",
      server2: "*******",
      cache: 1
    }
  }
}
```
